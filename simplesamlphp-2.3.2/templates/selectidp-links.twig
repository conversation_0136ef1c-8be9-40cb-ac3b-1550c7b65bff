{% set pagetitle = "Select your identity provider"|trans %}
{% extends "base.twig" %}

{% block content %}
    <h2>{{ pagetitle }}</h2>

    {% if idplist|length == 0 %}
    <div class="message-box message-box-error">{{ "No identity providers found. Cannot continue." | trans }}</div>
    {% else %}

    <p>{{ "Please select the identity provider where you want to authenticate:" | trans }}</p>
    <form method="get" action="{{ urlpattern }}">
        <input type="hidden" name="entityID" value="{{ entityID }}">
        <input type="hidden" name="return" value="{{ return }}">
        <input type="hidden" name="returnIDParam" value="{{ returnIDParam }}">
        {% if rememberenabled %}
        <div class="pure-control-group">
            <label for="remember">
                <input type="checkbox" name="remember" id="remember" value="1"{% if rememberchecked %} checked{% endif %}>
                {{ 'Remember my choice' | trans }}
            </label>
        </div>
        {% endif %}

        {% for idpentry in idplist %}
        {% if idpentry.entityid == preferredidp %}
                <div class="idp preferredidp pure-g">
                <div class="pure-u-1-8">
                    <button type="submit" class="pure-button pure-button-primary" name="idp_{{ idpentry.entityid }}">{{'Select'|trans}}</button>
                </div>
                {% if idpentry.iconurl is defined %}
                    <div class="pure-u-1-8">
                        <img src="{{ idpentry.iconurl }}" alt="">
                    </div>
                {% endif %}
                <div class="pure-u-7-8">
                <span class="idpname"><i class="fa fa-star"></i> {{ idpentry.name }}</span>
                {% if idpentry.description is defined and idpentry.description != idpentry.name %}
                    <br><span class="idpdesc">{{ idpentry.description }}</span>
                {% endif %}
                </div>
                </div>
        {% endif %}
        {% endfor %}

        {% for idpentry in idplist %}
        {% if idpentry.entityid != preferredidp %}
                <div class="idp pure-g">
                <div class="pure-u-1-8">
                    <button type="submit" class="pure-button" name="idp_{{ idpentry.entityid }}">{{'Select'|trans}}</button>
                </div>
                {% if idpentry.iconurl is defined %}
                    <div class="pure-u-1-8">
                        <img src="{{ idpentry.iconurl }}" alt="">
                    </div>
                {% endif %}
                <div class="pure-u-7-8">
                <span class="idpname">{{ idpentry.name }}</span>
                {% if idpentry.description is defined and idpentry.description != idpentry.name %}
                    <br><span class="idpdesc">{{ idpentry.description }}</span>
                {% endif %}
                </div>
                </div>
        {% endif %}
        {% endfor %}
    </form>
    {% endif %}
{% endblock %}
