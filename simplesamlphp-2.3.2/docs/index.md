# SimpleSAMLphp Documentation

* Installation and upgrading
  * [Installing SimpleSAMLphp](simplesamlphp-install)
  * [Upgrade notes](simplesamlphp-upgrade-notes)
  * [Installation from the repository](simplesamlphp-install-repo)
  * [Changelog](simplesamlphp-changelog)
* Using SimpleSAMLphp as a SAML Service Provider
  * [Service Provider Quickstart](simplesamlphp-sp)
  * [Hosted SP Configuration Reference](./saml:sp)
  * [IdP remote reference](simplesamlphp-reference-idp-remote)
  * [SP API reference (for your application)](simplesamlphp-sp-api).
  * [Configuring HTTP-Artifact](./simplesamlphp-artifact-sp)
  * [Using scoping](./simplesamlphp-scoping)
  * [Holder-of-Key profile](simplesamlphp-hok-sp)
* Using SimpleSAMLphp as a SAML Identity Provider
  * [Identity Provider QuickStart](simplesamlphp-idp)
  * [IdP hosted reference](simplesamlphp-reference-idp-hosted)
  * [SP remote reference](simplesamlphp-reference-sp-remote)
  * [Use case: Setting up an IdP for Google Workspace (G Suite / Google Apps)](simplesamlphp-googleapps)
  * [Configuring HTTP-Artifact](./simplesamlphp-artifact-idp)
  * [Identity Provider Advanced Topics](simplesamlphp-idp-more)
  * [Holder-of-Key profile](simplesamlphp-hok-idp)
* Further topics
  * [Maintenance and configuration](simplesamlphp-maintenance) - covers session handling, php configuration etc.
  * [Automated Metadata Management](/docs/contrib_modules/metarefresh/simplesamlphp-automated_metadata)
  * [Key rollover](./saml:keyrollover)
  * [Authentication Processing Filters](simplesamlphp-authproc) - attribute filtering, attribute mapping, consent, group generation etc.
  * [State Information Lost](simplesamlphp-nostate) - more about this common error message
  * [Advanced features](simplesamlphp-advancedfeatures) - covers bridging protocols, attribute filtering, etc.
  * [Developer documentation](simplesamlphp-developer-information) - code overview and other docs for new and returning developers
* SimpleSAMLphp Modules
  * [Documentation for specific modules](/docs/contributed_modules.html)
  * [Theming SimpleSAMLphp](simplesamlphp-theming)
  * [Creating authentication sources](./simplesamlphp-authsource)
  * [Create your own customized modules](simplesamlphp-modules)
