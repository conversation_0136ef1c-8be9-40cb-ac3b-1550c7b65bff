<?php

namespace Framework\plugins\asset_optimizer\migrations;

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AssetOptimizerMigration{
    /**
     * @return void
     */
    public static function ensureSchemaStructure(): void{
        if(!Schema::hasTable('asset_optimize'))
            Schema::create('asset_optimize', static function (Blueprint $table){
                $table->id();
                $table->string('orig_name');
                $table->string('orig_hash');
                $table->string('after_name');
                $table->string('after_hash');
                $table->string('backup_name');
                $table->bigInteger('old_size');
                $table->bigInteger('new_size');
                $table->timestamp('created_at')->useCurrent();
                $table->timestamp('updated_at')->useCurrent();
            });
    }
}
