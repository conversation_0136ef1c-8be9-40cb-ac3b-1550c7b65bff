<?php

namespace ContainerGRXmlel;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/*
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getDiscoService extends SimpleSAML_KernelProdContainer
{
    /*
     * Gets the public 'SimpleSAML\Module\saml\Controller\Disco' shared autowired service.
     *
     * @return \SimpleSAML\Module\saml\Controller\Disco
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->services['SimpleSAML\\Module\\saml\\Controller\\Disco'] = new \SimpleSAML\Module\saml\Controller\Disco(($container->privates['SimpleSAML\\Configuration'] ?? $container->load('getConfigurationService')));
    }
}
