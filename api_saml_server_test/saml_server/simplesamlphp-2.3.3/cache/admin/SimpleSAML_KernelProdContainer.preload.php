<?php

// This file has been auto-generated by the Symfony Dependency Injection Component
// You can reference it in the "opcache.preload" php.ini setting on PHP >= 7.4 when preloading is desired

use Symfony\Component\DependencyInjection\Dumper\Preloader;

if (in_array(PHP_SAPI, ['cli', 'phpdbg', 'embed'], true)) {
    return;
}

require dirname(__DIR__, 2).''.\DIRECTORY_SEPARATOR.'vendor/autoload.php';
(require __DIR__.'/SimpleSAML_KernelProdContainer.php')->set(\ContainerW8VU7O8\SimpleSAML_KernelProdContainer::class, null);
require __DIR__.'/ContainerW8VU7O8/RequestPayloadValueResolverGhostC1069a2.php';
require __DIR__.'/ContainerW8VU7O8/getServicesResetterService.php';
require __DIR__.'/ContainerW8VU7O8/getSecrets_VaultService.php';
require __DIR__.'/ContainerW8VU7O8/getHttpKernelService.php';
require __DIR__.'/ContainerW8VU7O8/getErrorControllerService.php';
require __DIR__.'/ContainerW8VU7O8/getDebug_ErrorHandlerConfiguratorService.php';
require __DIR__.'/ContainerW8VU7O8/getContainer_GetRoutingConditionServiceService.php';
require __DIR__.'/ContainerW8VU7O8/getContainer_EnvVarProcessorsLocatorService.php';
require __DIR__.'/ContainerW8VU7O8/getContainer_EnvVarProcessorService.php';
require __DIR__.'/ContainerW8VU7O8/getCache_SystemClearerService.php';
require __DIR__.'/ContainerW8VU7O8/getCache_SystemService.php';
require __DIR__.'/ContainerW8VU7O8/getCache_GlobalClearerService.php';
require __DIR__.'/ContainerW8VU7O8/getCache_AppClearerService.php';
require __DIR__.'/ContainerW8VU7O8/getCache_AppService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_VariadicService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_SessionService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_ServiceService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_RequestAttributeService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_RequestService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_QueryParameterValueResolverService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_DefaultService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_DatetimeService.php';
require __DIR__.'/ContainerW8VU7O8/getArgumentResolver_BackedEnumResolverService.php';
require __DIR__.'/ContainerW8VU7O8/getTemplateControllerService.php';
require __DIR__.'/ContainerW8VU7O8/getRedirectControllerService.php';
require __DIR__.'/ContainerW8VU7O8/getSessionService.php';
require __DIR__.'/ContainerW8VU7O8/getTestService.php';
require __DIR__.'/ContainerW8VU7O8/getSandboxService.php';
require __DIR__.'/ContainerW8VU7O8/getMenuService.php';
require __DIR__.'/ContainerW8VU7O8/getFederationService.php';
require __DIR__.'/ContainerW8VU7O8/getConfigService.php';
require __DIR__.'/ContainerW8VU7O8/getConfigurationService.php';
require __DIR__.'/ContainerW8VU7O8/get_ServiceLocator_Y4Zrx_Service.php';

$classes = [];
$classes[] = 'Symfony\Bundle\FrameworkBundle\FrameworkBundle';
$classes[] = 'Symfony\Component\DependencyInjection\ServiceLocator';
$classes[] = 'SimpleSAML\Configuration';
$classes[] = 'SimpleSAML\Module\admin\Controller\Config';
$classes[] = 'SimpleSAML\Module\admin\Controller\Federation';
$classes[] = 'SimpleSAML\Module\admin\Controller\Menu';
$classes[] = 'SimpleSAML\Module\admin\Controller\Sandbox';
$classes[] = 'SimpleSAML\Module\admin\Controller\Test';
$classes[] = 'SimpleSAML\Session';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\RedirectController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\TemplateController';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\BackedEnumValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DateTimeValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\QueryParameterValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver';
$classes[] = 'Symfony\Component\Cache\Adapter\FilesystemAdapter';
$classes[] = 'Symfony\Component\Cache\Marshaller\DefaultMarshaller';
$classes[] = 'Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer';
$classes[] = 'Symfony\Component\Cache\Adapter\AdapterInterface';
$classes[] = 'Symfony\Component\Cache\Adapter\AbstractAdapter';
$classes[] = 'Symfony\Component\DependencyInjection\EnvVarProcessor';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\CacheAttributeListener';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DebugHandlersListener';
$classes[] = 'Symfony\Component\HttpKernel\Debug\ErrorHandlerConfigurator';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\FileLinkFormatter';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ErrorController';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer';
$classes[] = 'Symfony\Component\EventDispatcher\EventDispatcher';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ErrorListener';
$classes[] = 'Symfony\Component\HttpKernel\HttpKernel';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ResponseListener';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ContainerControllerResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadataFactory';
$classes[] = 'SimpleSAML\Kernel';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleAwareListener';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleListener';
$classes[] = 'Symfony\Component\HttpKernel\Log\Logger';
$classes[] = 'Symfony\Component\HttpFoundation\RequestStack';
$classes[] = 'Symfony\Component\Routing\Router';
$classes[] = 'Symfony\Component\Routing\RequestContext';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\RouterListener';
$classes[] = 'Symfony\Component\Config\Loader\DelegatingLoader';
$classes[] = 'Symfony\Component\Config\Loader\LoaderResolver';
$classes[] = 'Symfony\Component\Routing\Loader\XmlFileLoader';
$classes[] = 'Symfony\Component\Config\FileLocator';
$classes[] = 'Symfony\Component\Routing\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\GlobFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\DirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\ContainerLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\AttributeRouteControllerLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeDirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\Psr4DirectoryLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Secrets\SodiumVault';
$classes[] = 'Symfony\Component\String\LazyString';
$classes[] = 'Symfony\Component\DependencyInjection\ContainerInterface';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\ServicesResetter';
$classes[] = 'Symfony\Component\String\Slugger\AsciiSlugger';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ValidateRequestListener';

$preloaded = Preloader::preload($classes);
