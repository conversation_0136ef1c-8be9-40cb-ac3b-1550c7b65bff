<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitd92254016c92c1549c1187a87107524d
{
    public static $files = array (
        '6e3fae29631ef280660b3cdad06f25a8' => __DIR__ . '/..' . '/symfony/deprecation-contracts/function.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        '320cde22f66dd4f5d3fd621d3e88b98f' => __DIR__ . '/..' . '/symfony/polyfill-ctype/bootstrap.php',
        '662a729f963d39afe703c9d9b7ab4a8c' => __DIR__ . '/..' . '/symfony/polyfill-php83/bootstrap.php',
        '667aeda72477189d0494fecd327c3641' => __DIR__ . '/..' . '/symfony/var-dumper/Resources/functions/dump.php',
        '23c18046f52bef3eea034657bafda50f' => __DIR__ . '/..' . '/symfony/polyfill-php81/bootstrap.php',
        '8825ede83f2f289127722d4e842cf7e8' => __DIR__ . '/..' . '/symfony/polyfill-intl-grapheme/bootstrap.php',
        'e69f7f6ee287b969198c3c9d6777bd38' => __DIR__ . '/..' . '/symfony/polyfill-intl-normalizer/bootstrap.php',
        '89efb1254ef2d1c5d80096acd12c4098' => __DIR__ . '/..' . '/twig/twig/src/Resources/core.php',
        'ffecb95d45175fd40f75be8a23b34f90' => __DIR__ . '/..' . '/twig/twig/src/Resources/debug.php',
        'c7baa00073ee9c61edf148c51917cfb4' => __DIR__ . '/..' . '/twig/twig/src/Resources/escaper.php',
        'f844ccf1d25df8663951193c3fc307c8' => __DIR__ . '/..' . '/twig/twig/src/Resources/string_loader.php',
        'b6b991a57620e2fb6b2f66f03fe9ddc2' => __DIR__ . '/..' . '/symfony/string/Resources/functions.php',
        '6a47392539ca2329373e0d33e1dba053' => __DIR__ . '/..' . '/symfony/polyfill-intl-icu/bootstrap.php',
        '334983c1b64a31de3c73827b7a6a1e88' => __DIR__ . '/../..' . '/src/_autoload_modules.php',
    );

    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'Webmozart\\Assert\\' => 17,
        ),
        'T' => 
        array (
            'Twig\\Extra\\Intl\\' => 16,
            'Twig\\' => 5,
        ),
        'S' => 
        array (
            'Symfony\\Polyfill\\Php83\\' => 23,
            'Symfony\\Polyfill\\Php81\\' => 23,
            'Symfony\\Polyfill\\Mbstring\\' => 26,
            'Symfony\\Polyfill\\Intl\\Normalizer\\' => 33,
            'Symfony\\Polyfill\\Intl\\Icu\\' => 26,
            'Symfony\\Polyfill\\Intl\\Grapheme\\' => 31,
            'Symfony\\Polyfill\\Ctype\\' => 23,
            'Symfony\\Contracts\\Translation\\' => 30,
            'Symfony\\Contracts\\Service\\' => 26,
            'Symfony\\Contracts\\EventDispatcher\\' => 34,
            'Symfony\\Contracts\\Cache\\' => 24,
            'Symfony\\Component\\Yaml\\' => 23,
            'Symfony\\Component\\VarExporter\\' => 30,
            'Symfony\\Component\\VarDumper\\' => 28,
            'Symfony\\Component\\String\\' => 25,
            'Symfony\\Component\\Routing\\' => 26,
            'Symfony\\Component\\PasswordHasher\\' => 33,
            'Symfony\\Component\\OptionsResolver\\' => 34,
            'Symfony\\Component\\Ldap\\' => 23,
            'Symfony\\Component\\Intl\\' => 23,
            'Symfony\\Component\\HttpKernel\\' => 29,
            'Symfony\\Component\\HttpFoundation\\' => 33,
            'Symfony\\Component\\Finder\\' => 25,
            'Symfony\\Component\\Filesystem\\' => 29,
            'Symfony\\Component\\EventDispatcher\\' => 34,
            'Symfony\\Component\\ErrorHandler\\' => 31,
            'Symfony\\Component\\DependencyInjection\\' => 38,
            'Symfony\\Component\\Console\\' => 26,
            'Symfony\\Component\\Config\\' => 25,
            'Symfony\\Component\\Cache\\' => 24,
            'Symfony\\Bundle\\FrameworkBundle\\' => 31,
            'Symfony\\Bridge\\Twig\\' => 20,
            'SimpleSAML\\modules\\discopower\\' => 30,
            'SimpleSAML\\XML\\' => 15,
            'SimpleSAML\\XMLSecurity\\' => 23,
            'SimpleSAML\\Module\\statistics\\' => 29,
            'SimpleSAML\\Module\\sqlauth\\' => 26,
            'SimpleSAML\\Module\\saml\\' => 23,
            'SimpleSAML\\Module\\radius\\' => 25,
            'SimpleSAML\\Module\\multiauth\\' => 28,
            'SimpleSAML\\Module\\metarefresh\\' => 30,
            'SimpleSAML\\Module\\ldap\\' => 23,
            'SimpleSAML\\Module\\exampleauth\\' => 30,
            'SimpleSAML\\Module\\cron\\' => 23,
            'SimpleSAML\\Module\\core\\' => 23,
            'SimpleSAML\\Module\\consent\\' => 26,
            'SimpleSAML\\Module\\consentAdmin\\' => 31,
            'SimpleSAML\\Module\\authorize\\' => 28,
            'SimpleSAML\\Module\\admin\\' => 24,
            'SimpleSAML\\Composer\\XMLProvider\\' => 32,
            'SimpleSAML\\Composer\\' => 20,
            'SimpleSAML\\Assert\\' => 18,
            'SimpleSAML\\' => 11,
            'SAML2\\' => 6,
        ),
        'R' => 
        array (
            'RobRichards\\XMLSecLibs\\' => 23,
        ),
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\EventDispatcher\\' => 20,
            'Psr\\Container\\' => 14,
            'Psr\\Cache\\' => 10,
            'PHPMailer\\PHPMailer\\' => 20,
        ),
        'L' => 
        array (
            'League\\Uri\\' => 11,
        ),
        'G' => 
        array (
            'Gettext\\Languages\\' => 18,
            'Gettext\\' => 8,
        ),
        'D' => 
        array (
            'Dapphp\\Radius\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Webmozart\\Assert\\' => 
        array (
            0 => __DIR__ . '/..' . '/webmozart/assert/src',
        ),
        'Twig\\Extra\\Intl\\' => 
        array (
            0 => __DIR__ . '/..' . '/twig/intl-extra',
        ),
        'Twig\\' => 
        array (
            0 => __DIR__ . '/..' . '/twig/twig/src',
        ),
        'Symfony\\Polyfill\\Php83\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php83',
        ),
        'Symfony\\Polyfill\\Php81\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php81',
        ),
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Symfony\\Polyfill\\Intl\\Normalizer\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-intl-normalizer',
        ),
        'Symfony\\Polyfill\\Intl\\Icu\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-intl-icu',
        ),
        'Symfony\\Polyfill\\Intl\\Grapheme\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-intl-grapheme',
        ),
        'Symfony\\Polyfill\\Ctype\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-ctype',
        ),
        'Symfony\\Contracts\\Translation\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/translation-contracts',
        ),
        'Symfony\\Contracts\\Service\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/service-contracts',
        ),
        'Symfony\\Contracts\\EventDispatcher\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/event-dispatcher-contracts',
        ),
        'Symfony\\Contracts\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/cache-contracts',
        ),
        'Symfony\\Component\\Yaml\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/yaml',
        ),
        'Symfony\\Component\\VarExporter\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/var-exporter',
        ),
        'Symfony\\Component\\VarDumper\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/var-dumper',
        ),
        'Symfony\\Component\\String\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/string',
        ),
        'Symfony\\Component\\Routing\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/routing',
        ),
        'Symfony\\Component\\PasswordHasher\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/password-hasher',
        ),
        'Symfony\\Component\\OptionsResolver\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/options-resolver',
        ),
        'Symfony\\Component\\Ldap\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/ldap',
        ),
        'Symfony\\Component\\Intl\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/intl',
        ),
        'Symfony\\Component\\HttpKernel\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/http-kernel',
        ),
        'Symfony\\Component\\HttpFoundation\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/http-foundation',
        ),
        'Symfony\\Component\\Finder\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/finder',
        ),
        'Symfony\\Component\\Filesystem\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/filesystem',
        ),
        'Symfony\\Component\\EventDispatcher\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/event-dispatcher',
        ),
        'Symfony\\Component\\ErrorHandler\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/error-handler',
        ),
        'Symfony\\Component\\DependencyInjection\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/dependency-injection',
        ),
        'Symfony\\Component\\Console\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/console',
        ),
        'Symfony\\Component\\Config\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/config',
        ),
        'Symfony\\Component\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/cache',
        ),
        'Symfony\\Bundle\\FrameworkBundle\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/framework-bundle',
        ),
        'Symfony\\Bridge\\Twig\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/twig-bridge',
        ),
        'SimpleSAML\\modules\\discopower\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/discopower/src',
        ),
        'SimpleSAML\\XML\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplesamlphp/xml-common/src',
        ),
        'SimpleSAML\\XMLSecurity\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplesamlphp/xml-security/src',
        ),
        'SimpleSAML\\Module\\statistics\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/statistics/src',
        ),
        'SimpleSAML\\Module\\sqlauth\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/sqlauth/src',
        ),
        'SimpleSAML\\Module\\saml\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/saml/src',
        ),
        'SimpleSAML\\Module\\radius\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/radius/src',
        ),
        'SimpleSAML\\Module\\multiauth\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/multiauth/src',
        ),
        'SimpleSAML\\Module\\metarefresh\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/metarefresh/src',
        ),
        'SimpleSAML\\Module\\ldap\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/ldap/src',
        ),
        'SimpleSAML\\Module\\exampleauth\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/exampleauth/src',
        ),
        'SimpleSAML\\Module\\cron\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/cron/src',
        ),
        'SimpleSAML\\Module\\core\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/core/src',
        ),
        'SimpleSAML\\Module\\consent\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/consent/src',
        ),
        'SimpleSAML\\Module\\consentAdmin\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/consentAdmin/src',
        ),
        'SimpleSAML\\Module\\authorize\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/authorize/src',
        ),
        'SimpleSAML\\Module\\admin\\' => 
        array (
            0 => __DIR__ . '/../..' . '/modules/admin/src',
        ),
        'SimpleSAML\\Composer\\XMLProvider\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplesamlphp/composer-xmlprovider-installer/src',
        ),
        'SimpleSAML\\Composer\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplesamlphp/composer-module-installer/src',
        ),
        'SimpleSAML\\Assert\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplesamlphp/assert/src',
        ),
        'SimpleSAML\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src/SimpleSAML',
        ),
        'SAML2\\' => 
        array (
            0 => __DIR__ . '/..' . '/simplesamlphp/saml2/src/SAML2',
        ),
        'RobRichards\\XMLSecLibs\\' => 
        array (
            0 => __DIR__ . '/..' . '/robrichards/xmlseclibs/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-factory/src',
            1 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\EventDispatcher\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/event-dispatcher/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'Psr\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/cache/src',
        ),
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'League\\Uri\\' => 
        array (
            0 => __DIR__ . '/..' . '/league/uri-interfaces',
        ),
        'Gettext\\Languages\\' => 
        array (
            0 => __DIR__ . '/..' . '/gettext/languages/src',
        ),
        'Gettext\\' => 
        array (
            0 => __DIR__ . '/..' . '/gettext/gettext/src',
            1 => __DIR__ . '/..' . '/gettext/translator/src',
        ),
        'Dapphp\\Radius\\' => 
        array (
            0 => __DIR__ . '/..' . '/dapphp/radius/src',
        ),
    );

    public static $prefixesPsr0 = array (
        'C' => 
        array (
            'Crypt_CHAP_' => 
            array (
                0 => __DIR__ . '/..' . '/dapphp/radius/lib',
            ),
        ),
    );

    public static $classMap = array (
        'CURLStringFile' => __DIR__ . '/..' . '/symfony/polyfill-php81/Resources/stubs/CURLStringFile.php',
        'Collator' => __DIR__ . '/..' . '/symfony/polyfill-intl-icu/Resources/stubs/Collator.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'DateError' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateError.php',
        'DateException' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateException.php',
        'DateInvalidOperationException' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateInvalidOperationException.php',
        'DateInvalidTimeZoneException' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateInvalidTimeZoneException.php',
        'DateMalformedIntervalStringException' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateMalformedIntervalStringException.php',
        'DateMalformedPeriodStringException' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateMalformedPeriodStringException.php',
        'DateMalformedStringException' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateMalformedStringException.php',
        'DateObjectError' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateObjectError.php',
        'DateRangeError' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/DateRangeError.php',
        'IntlDateFormatter' => __DIR__ . '/..' . '/symfony/polyfill-intl-icu/Resources/stubs/IntlDateFormatter.php',
        'Locale' => __DIR__ . '/..' . '/symfony/polyfill-intl-icu/Resources/stubs/Locale.php',
        'Normalizer' => __DIR__ . '/..' . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
        'NumberFormatter' => __DIR__ . '/..' . '/symfony/polyfill-intl-icu/Resources/stubs/NumberFormatter.php',
        'Override' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/Override.php',
        'ReturnTypeWillChange' => __DIR__ . '/..' . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
        'SQLite3Exception' => __DIR__ . '/..' . '/symfony/polyfill-php83/Resources/stubs/SQLite3Exception.php',
        '�' => __DIR__ . '/..' . '/symfony/cache/Traits/ValueWrapper.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitd92254016c92c1549c1187a87107524d::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitd92254016c92c1549c1187a87107524d::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInitd92254016c92c1549c1187a87107524d::$prefixesPsr0;
            $loader->classMap = ComposerStaticInitd92254016c92c1549c1187a87107524d::$classMap;

        }, null, ClassLoader::class);
    }
}
