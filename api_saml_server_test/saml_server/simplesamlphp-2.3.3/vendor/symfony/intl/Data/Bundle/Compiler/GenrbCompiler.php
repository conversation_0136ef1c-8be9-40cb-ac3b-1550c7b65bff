<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Intl\Data\Bundle\Compiler;

use Symfony\Component\Intl\Exception\RuntimeException;

/**
 * Compiles .txt resource bundles to binary .res files.
 *
 * <AUTHOR> <bsch<PERSON><PERSON>@gmail.com>
 *
 * @internal
 */
class GenrbCompiler implements BundleCompilerInterface
{
    private string $genrb;

    /**
     * Creates a new compiler based on the "genrb" executable.
     *
     * @param string $genrb   Optional. The path to the "genrb" executable
     * @param string $envVars Optional. Environment variables to be loaded when running "genrb".
     *
     * @throws RuntimeException if the "genrb" cannot be found
     */
    public function __construct(string $genrb = 'genrb', string $envVars = '')
    {
        exec('which '.$genrb, $output, $status);

        if (0 !== $status) {
            throw new RuntimeException(sprintf('The command "%s" is not installed.', $genrb));
        }

        $this->genrb = ($envVars ? $envVars.' ' : '').$genrb;
    }

    public function compile(string $sourcePath, string $targetDir): void
    {
        if (is_dir($sourcePath)) {
            $sourcePath .= '/*.txt';
        }

        exec($this->genrb.' --quiet -e UTF-8 -d '.$targetDir.' '.$sourcePath, $output, $status);

        if (0 !== $status) {
            throw new RuntimeException(sprintf('genrb failed with status %d while compiling "%s" to "%s".', $status, $sourcePath, $targetDir));
        }
    }
}
