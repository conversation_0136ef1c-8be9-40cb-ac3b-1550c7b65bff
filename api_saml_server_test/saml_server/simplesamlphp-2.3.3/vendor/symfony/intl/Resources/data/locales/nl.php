<?php

return [
    'Names' => [
        'af' => 'Afrikaans',
        'af_NA' => 'Afrikaans (Namibië)',
        'af_ZA' => 'Afrikaans (Zuid-Afrika)',
        'ak' => 'A<PERSON>',
        'ak_GH' => '<PERSON><PERSON> (Ghana)',
        'am' => '<PERSON><PERSON><PERSON>',
        'am_ET' => '<PERSON><PERSON><PERSON> (Ethiopië)',
        'ar' => 'Arabisch',
        'ar_001' => 'Arabisch (wereld)',
        'ar_AE' => 'Arabisch (Verenigde Arabische Emiraten)',
        'ar_BH' => 'Arabisch (Bahrein)',
        'ar_DJ' => 'Arabisch (Djibouti)',
        'ar_DZ' => 'Arabisch (Algerije)',
        'ar_EG' => 'Arabisch (Egypte)',
        'ar_EH' => 'Arabisch (Westelijke Sahara)',
        'ar_ER' => 'Arabisch (Eritrea)',
        'ar_IL' => 'Arabisch (Israël)',
        'ar_IQ' => 'Arabisch (Irak)',
        'ar_JO' => 'Arabisch (Jordanië)',
        'ar_KM' => 'Arabisch (Comoren)',
        'ar_KW' => 'Arabisch (Koeweit)',
        'ar_LB' => 'Arabisch (Libanon)',
        'ar_LY' => 'Arabisch (Libië)',
        'ar_MA' => 'Arabisch (Marokko)',
        'ar_MR' => 'Arabisch (Mauritanië)',
        'ar_OM' => 'Arabisch (Oman)',
        'ar_PS' => 'Arabisch (Palestijnse gebieden)',
        'ar_QA' => 'Arabisch (Qatar)',
        'ar_SA' => 'Arabisch (Saoedi-Arabië)',
        'ar_SD' => 'Arabisch (Soedan)',
        'ar_SO' => 'Arabisch (Somalië)',
        'ar_SS' => 'Arabisch (Zuid-Soedan)',
        'ar_SY' => 'Arabisch (Syrië)',
        'ar_TD' => 'Arabisch (Tsjaad)',
        'ar_TN' => 'Arabisch (Tunesië)',
        'ar_YE' => 'Arabisch (Jemen)',
        'as' => 'Assamees',
        'as_IN' => 'Assamees (India)',
        'az' => 'Azerbeidzjaans',
        'az_AZ' => 'Azerbeidzjaans (Azerbeidzjan)',
        'az_Cyrl' => 'Azerbeidzjaans (Cyrillisch)',
        'az_Cyrl_AZ' => 'Azerbeidzjaans (Cyrillisch, Azerbeidzjan)',
        'az_Latn' => 'Azerbeidzjaans (Latijns)',
        'az_Latn_AZ' => 'Azerbeidzjaans (Latijns, Azerbeidzjan)',
        'be' => 'Belarussisch',
        'be_BY' => 'Belarussisch (Belarus)',
        'bg' => 'Bulgaars',
        'bg_BG' => 'Bulgaars (Bulgarije)',
        'bm' => 'Bambara',
        'bm_ML' => 'Bambara (Mali)',
        'bn' => 'Bengaals',
        'bn_BD' => 'Bengaals (Bangladesh)',
        'bn_IN' => 'Bengaals (India)',
        'bo' => 'Tibetaans',
        'bo_CN' => 'Tibetaans (China)',
        'bo_IN' => 'Tibetaans (India)',
        'br' => 'Bretons',
        'br_FR' => 'Bretons (Frankrijk)',
        'bs' => 'Bosnisch',
        'bs_BA' => 'Bosnisch (Bosnië en Herzegovina)',
        'bs_Cyrl' => 'Bosnisch (Cyrillisch)',
        'bs_Cyrl_BA' => 'Bosnisch (Cyrillisch, Bosnië en Herzegovina)',
        'bs_Latn' => 'Bosnisch (Latijns)',
        'bs_Latn_BA' => 'Bosnisch (Latijns, Bosnië en Herzegovina)',
        'ca' => 'Catalaans',
        'ca_AD' => 'Catalaans (Andorra)',
        'ca_ES' => 'Catalaans (Spanje)',
        'ca_FR' => 'Catalaans (Frankrijk)',
        'ca_IT' => 'Catalaans (Italië)',
        'ce' => 'Tsjetsjeens',
        'ce_RU' => 'Tsjetsjeens (Rusland)',
        'cs' => 'Tsjechisch',
        'cs_CZ' => 'Tsjechisch (Tsjechië)',
        'cv' => 'Tsjoevasjisch',
        'cv_RU' => 'Tsjoevasjisch (Rusland)',
        'cy' => 'Welsh',
        'cy_GB' => 'Welsh (Verenigd Koninkrijk)',
        'da' => 'Deens',
        'da_DK' => 'Deens (Denemarken)',
        'da_GL' => 'Deens (Groenland)',
        'de' => 'Duits',
        'de_AT' => 'Duits (Oostenrijk)',
        'de_BE' => 'Duits (België)',
        'de_CH' => 'Duits (Zwitserland)',
        'de_DE' => 'Duits (Duitsland)',
        'de_IT' => 'Duits (Italië)',
        'de_LI' => 'Duits (Liechtenstein)',
        'de_LU' => 'Duits (Luxemburg)',
        'dz' => 'Dzongkha',
        'dz_BT' => 'Dzongkha (Bhutan)',
        'ee' => 'Ewe',
        'ee_GH' => 'Ewe (Ghana)',
        'ee_TG' => 'Ewe (Togo)',
        'el' => 'Grieks',
        'el_CY' => 'Grieks (Cyprus)',
        'el_GR' => 'Grieks (Griekenland)',
        'en' => 'Engels',
        'en_001' => 'Engels (wereld)',
        'en_150' => 'Engels (Europa)',
        'en_AE' => 'Engels (Verenigde Arabische Emiraten)',
        'en_AG' => 'Engels (Antigua en Barbuda)',
        'en_AI' => 'Engels (Anguilla)',
        'en_AS' => 'Engels (Amerikaans-Samoa)',
        'en_AT' => 'Engels (Oostenrijk)',
        'en_AU' => 'Engels (Australië)',
        'en_BB' => 'Engels (Barbados)',
        'en_BE' => 'Engels (België)',
        'en_BI' => 'Engels (Burundi)',
        'en_BM' => 'Engels (Bermuda)',
        'en_BS' => 'Engels (Bahama’s)',
        'en_BW' => 'Engels (Botswana)',
        'en_BZ' => 'Engels (Belize)',
        'en_CA' => 'Engels (Canada)',
        'en_CC' => 'Engels (Cocoseilanden)',
        'en_CH' => 'Engels (Zwitserland)',
        'en_CK' => 'Engels (Cookeilanden)',
        'en_CM' => 'Engels (Kameroen)',
        'en_CX' => 'Engels (Christmaseiland)',
        'en_CY' => 'Engels (Cyprus)',
        'en_DE' => 'Engels (Duitsland)',
        'en_DK' => 'Engels (Denemarken)',
        'en_DM' => 'Engels (Dominica)',
        'en_ER' => 'Engels (Eritrea)',
        'en_FI' => 'Engels (Finland)',
        'en_FJ' => 'Engels (Fiji)',
        'en_FK' => 'Engels (Falklandeilanden)',
        'en_FM' => 'Engels (Micronesia)',
        'en_GB' => 'Engels (Verenigd Koninkrijk)',
        'en_GD' => 'Engels (Grenada)',
        'en_GG' => 'Engels (Guernsey)',
        'en_GH' => 'Engels (Ghana)',
        'en_GI' => 'Engels (Gibraltar)',
        'en_GM' => 'Engels (Gambia)',
        'en_GU' => 'Engels (Guam)',
        'en_GY' => 'Engels (Guyana)',
        'en_HK' => 'Engels (Hongkong SAR van China)',
        'en_ID' => 'Engels (Indonesië)',
        'en_IE' => 'Engels (Ierland)',
        'en_IL' => 'Engels (Israël)',
        'en_IM' => 'Engels (Isle of Man)',
        'en_IN' => 'Engels (India)',
        'en_IO' => 'Engels (Brits Indische Oceaanterritorium)',
        'en_JE' => 'Engels (Jersey)',
        'en_JM' => 'Engels (Jamaica)',
        'en_KE' => 'Engels (Kenia)',
        'en_KI' => 'Engels (Kiribati)',
        'en_KN' => 'Engels (Saint Kitts en Nevis)',
        'en_KY' => 'Engels (Kaaimaneilanden)',
        'en_LC' => 'Engels (Saint Lucia)',
        'en_LR' => 'Engels (Liberia)',
        'en_LS' => 'Engels (Lesotho)',
        'en_MG' => 'Engels (Madagaskar)',
        'en_MH' => 'Engels (Marshalleilanden)',
        'en_MO' => 'Engels (Macau SAR van China)',
        'en_MP' => 'Engels (Noordelijke Marianen)',
        'en_MS' => 'Engels (Montserrat)',
        'en_MT' => 'Engels (Malta)',
        'en_MU' => 'Engels (Mauritius)',
        'en_MV' => 'Engels (Maldiven)',
        'en_MW' => 'Engels (Malawi)',
        'en_MY' => 'Engels (Maleisië)',
        'en_NA' => 'Engels (Namibië)',
        'en_NF' => 'Engels (Norfolk)',
        'en_NG' => 'Engels (Nigeria)',
        'en_NL' => 'Engels (Nederland)',
        'en_NR' => 'Engels (Nauru)',
        'en_NU' => 'Engels (Niue)',
        'en_NZ' => 'Engels (Nieuw-Zeeland)',
        'en_PG' => 'Engels (Papoea-Nieuw-Guinea)',
        'en_PH' => 'Engels (Filipijnen)',
        'en_PK' => 'Engels (Pakistan)',
        'en_PN' => 'Engels (Pitcairneilanden)',
        'en_PR' => 'Engels (Puerto Rico)',
        'en_PW' => 'Engels (Palau)',
        'en_RW' => 'Engels (Rwanda)',
        'en_SB' => 'Engels (Salomonseilanden)',
        'en_SC' => 'Engels (Seychellen)',
        'en_SD' => 'Engels (Soedan)',
        'en_SE' => 'Engels (Zweden)',
        'en_SG' => 'Engels (Singapore)',
        'en_SH' => 'Engels (Sint-Helena)',
        'en_SI' => 'Engels (Slovenië)',
        'en_SL' => 'Engels (Sierra Leone)',
        'en_SS' => 'Engels (Zuid-Soedan)',
        'en_SX' => 'Engels (Sint-Maarten)',
        'en_SZ' => 'Engels (Eswatini)',
        'en_TC' => 'Engels (Turks- en Caicoseilanden)',
        'en_TK' => 'Engels (Tokelau)',
        'en_TO' => 'Engels (Tonga)',
        'en_TT' => 'Engels (Trinidad en Tobago)',
        'en_TV' => 'Engels (Tuvalu)',
        'en_TZ' => 'Engels (Tanzania)',
        'en_UG' => 'Engels (Oeganda)',
        'en_UM' => 'Engels (Kleine afgelegen eilanden van de Verenigde Staten)',
        'en_US' => 'Engels (Verenigde Staten)',
        'en_VC' => 'Engels (Saint Vincent en de Grenadines)',
        'en_VG' => 'Engels (Britse Maagdeneilanden)',
        'en_VI' => 'Engels (Amerikaanse Maagdeneilanden)',
        'en_VU' => 'Engels (Vanuatu)',
        'en_WS' => 'Engels (Samoa)',
        'en_ZA' => 'Engels (Zuid-Afrika)',
        'en_ZM' => 'Engels (Zambia)',
        'en_ZW' => 'Engels (Zimbabwe)',
        'eo' => 'Esperanto',
        'eo_001' => 'Esperanto (wereld)',
        'es' => 'Spaans',
        'es_419' => 'Spaans (Latijns-Amerika)',
        'es_AR' => 'Spaans (Argentinië)',
        'es_BO' => 'Spaans (Bolivia)',
        'es_BR' => 'Spaans (Brazilië)',
        'es_BZ' => 'Spaans (Belize)',
        'es_CL' => 'Spaans (Chili)',
        'es_CO' => 'Spaans (Colombia)',
        'es_CR' => 'Spaans (Costa Rica)',
        'es_CU' => 'Spaans (Cuba)',
        'es_DO' => 'Spaans (Dominicaanse Republiek)',
        'es_EC' => 'Spaans (Ecuador)',
        'es_ES' => 'Spaans (Spanje)',
        'es_GQ' => 'Spaans (Equatoriaal-Guinea)',
        'es_GT' => 'Spaans (Guatemala)',
        'es_HN' => 'Spaans (Honduras)',
        'es_MX' => 'Spaans (Mexico)',
        'es_NI' => 'Spaans (Nicaragua)',
        'es_PA' => 'Spaans (Panama)',
        'es_PE' => 'Spaans (Peru)',
        'es_PH' => 'Spaans (Filipijnen)',
        'es_PR' => 'Spaans (Puerto Rico)',
        'es_PY' => 'Spaans (Paraguay)',
        'es_SV' => 'Spaans (El Salvador)',
        'es_US' => 'Spaans (Verenigde Staten)',
        'es_UY' => 'Spaans (Uruguay)',
        'es_VE' => 'Spaans (Venezuela)',
        'et' => 'Estisch',
        'et_EE' => 'Estisch (Estland)',
        'eu' => 'Baskisch',
        'eu_ES' => 'Baskisch (Spanje)',
        'fa' => 'Perzisch',
        'fa_AF' => 'Perzisch (Afghanistan)',
        'fa_IR' => 'Perzisch (Iran)',
        'ff' => 'Fulah',
        'ff_Adlm' => 'Fulah (Adlam)',
        'ff_Adlm_BF' => 'Fulah (Adlam, Burkina Faso)',
        'ff_Adlm_CM' => 'Fulah (Adlam, Kameroen)',
        'ff_Adlm_GH' => 'Fulah (Adlam, Ghana)',
        'ff_Adlm_GM' => 'Fulah (Adlam, Gambia)',
        'ff_Adlm_GN' => 'Fulah (Adlam, Guinee)',
        'ff_Adlm_GW' => 'Fulah (Adlam, Guinee-Bissau)',
        'ff_Adlm_LR' => 'Fulah (Adlam, Liberia)',
        'ff_Adlm_MR' => 'Fulah (Adlam, Mauritanië)',
        'ff_Adlm_NE' => 'Fulah (Adlam, Niger)',
        'ff_Adlm_NG' => 'Fulah (Adlam, Nigeria)',
        'ff_Adlm_SL' => 'Fulah (Adlam, Sierra Leone)',
        'ff_Adlm_SN' => 'Fulah (Adlam, Senegal)',
        'ff_CM' => 'Fulah (Kameroen)',
        'ff_GN' => 'Fulah (Guinee)',
        'ff_Latn' => 'Fulah (Latijns)',
        'ff_Latn_BF' => 'Fulah (Latijns, Burkina Faso)',
        'ff_Latn_CM' => 'Fulah (Latijns, Kameroen)',
        'ff_Latn_GH' => 'Fulah (Latijns, Ghana)',
        'ff_Latn_GM' => 'Fulah (Latijns, Gambia)',
        'ff_Latn_GN' => 'Fulah (Latijns, Guinee)',
        'ff_Latn_GW' => 'Fulah (Latijns, Guinee-Bissau)',
        'ff_Latn_LR' => 'Fulah (Latijns, Liberia)',
        'ff_Latn_MR' => 'Fulah (Latijns, Mauritanië)',
        'ff_Latn_NE' => 'Fulah (Latijns, Niger)',
        'ff_Latn_NG' => 'Fulah (Latijns, Nigeria)',
        'ff_Latn_SL' => 'Fulah (Latijns, Sierra Leone)',
        'ff_Latn_SN' => 'Fulah (Latijns, Senegal)',
        'ff_MR' => 'Fulah (Mauritanië)',
        'ff_SN' => 'Fulah (Senegal)',
        'fi' => 'Fins',
        'fi_FI' => 'Fins (Finland)',
        'fo' => 'Faeröers',
        'fo_DK' => 'Faeröers (Denemarken)',
        'fo_FO' => 'Faeröers (Faeröer)',
        'fr' => 'Frans',
        'fr_BE' => 'Frans (België)',
        'fr_BF' => 'Frans (Burkina Faso)',
        'fr_BI' => 'Frans (Burundi)',
        'fr_BJ' => 'Frans (Benin)',
        'fr_BL' => 'Frans (Saint-Barthélemy)',
        'fr_CA' => 'Frans (Canada)',
        'fr_CD' => 'Frans (Congo-Kinshasa)',
        'fr_CF' => 'Frans (Centraal-Afrikaanse Republiek)',
        'fr_CG' => 'Frans (Congo-Brazzaville)',
        'fr_CH' => 'Frans (Zwitserland)',
        'fr_CI' => 'Frans (Ivoorkust)',
        'fr_CM' => 'Frans (Kameroen)',
        'fr_DJ' => 'Frans (Djibouti)',
        'fr_DZ' => 'Frans (Algerije)',
        'fr_FR' => 'Frans (Frankrijk)',
        'fr_GA' => 'Frans (Gabon)',
        'fr_GF' => 'Frans (Frans-Guyana)',
        'fr_GN' => 'Frans (Guinee)',
        'fr_GP' => 'Frans (Guadeloupe)',
        'fr_GQ' => 'Frans (Equatoriaal-Guinea)',
        'fr_HT' => 'Frans (Haïti)',
        'fr_KM' => 'Frans (Comoren)',
        'fr_LU' => 'Frans (Luxemburg)',
        'fr_MA' => 'Frans (Marokko)',
        'fr_MC' => 'Frans (Monaco)',
        'fr_MF' => 'Frans (Saint-Martin)',
        'fr_MG' => 'Frans (Madagaskar)',
        'fr_ML' => 'Frans (Mali)',
        'fr_MQ' => 'Frans (Martinique)',
        'fr_MR' => 'Frans (Mauritanië)',
        'fr_MU' => 'Frans (Mauritius)',
        'fr_NC' => 'Frans (Nieuw-Caledonië)',
        'fr_NE' => 'Frans (Niger)',
        'fr_PF' => 'Frans (Frans-Polynesië)',
        'fr_PM' => 'Frans (Saint-Pierre en Miquelon)',
        'fr_RE' => 'Frans (Réunion)',
        'fr_RW' => 'Frans (Rwanda)',
        'fr_SC' => 'Frans (Seychellen)',
        'fr_SN' => 'Frans (Senegal)',
        'fr_SY' => 'Frans (Syrië)',
        'fr_TD' => 'Frans (Tsjaad)',
        'fr_TG' => 'Frans (Togo)',
        'fr_TN' => 'Frans (Tunesië)',
        'fr_VU' => 'Frans (Vanuatu)',
        'fr_WF' => 'Frans (Wallis en Futuna)',
        'fr_YT' => 'Frans (Mayotte)',
        'fy' => 'Fries',
        'fy_NL' => 'Fries (Nederland)',
        'ga' => 'Iers',
        'ga_GB' => 'Iers (Verenigd Koninkrijk)',
        'ga_IE' => 'Iers (Ierland)',
        'gd' => 'Schots-Gaelisch',
        'gd_GB' => 'Schots-Gaelisch (Verenigd Koninkrijk)',
        'gl' => 'Galicisch',
        'gl_ES' => 'Galicisch (Spanje)',
        'gu' => 'Gujarati',
        'gu_IN' => 'Gujarati (India)',
        'gv' => 'Manx',
        'gv_IM' => 'Manx (Isle of Man)',
        'ha' => 'Hausa',
        'ha_GH' => 'Hausa (Ghana)',
        'ha_NE' => 'Hausa (Niger)',
        'ha_NG' => 'Hausa (Nigeria)',
        'he' => 'Hebreeuws',
        'he_IL' => 'Hebreeuws (Israël)',
        'hi' => 'Hindi',
        'hi_IN' => 'Hindi (India)',
        'hi_Latn' => 'Hindi (Latijns)',
        'hi_Latn_IN' => 'Hindi (Latijns, India)',
        'hr' => 'Kroatisch',
        'hr_BA' => 'Kroatisch (Bosnië en Herzegovina)',
        'hr_HR' => 'Kroatisch (Kroatië)',
        'hu' => 'Hongaars',
        'hu_HU' => 'Hongaars (Hongarije)',
        'hy' => 'Armeens',
        'hy_AM' => 'Armeens (Armenië)',
        'ia' => 'Interlingua',
        'ia_001' => 'Interlingua (wereld)',
        'id' => 'Indonesisch',
        'id_ID' => 'Indonesisch (Indonesië)',
        'ie' => 'Interlingue',
        'ie_EE' => 'Interlingue (Estland)',
        'ig' => 'Igbo',
        'ig_NG' => 'Igbo (Nigeria)',
        'ii' => 'Yi',
        'ii_CN' => 'Yi (China)',
        'is' => 'IJslands',
        'is_IS' => 'IJslands (IJsland)',
        'it' => 'Italiaans',
        'it_CH' => 'Italiaans (Zwitserland)',
        'it_IT' => 'Italiaans (Italië)',
        'it_SM' => 'Italiaans (San Marino)',
        'it_VA' => 'Italiaans (Vaticaanstad)',
        'ja' => 'Japans',
        'ja_JP' => 'Japans (Japan)',
        'jv' => 'Javaans',
        'jv_ID' => 'Javaans (Indonesië)',
        'ka' => 'Georgisch',
        'ka_GE' => 'Georgisch (Georgië)',
        'ki' => 'Gikuyu',
        'ki_KE' => 'Gikuyu (Kenia)',
        'kk' => 'Kazachs',
        'kk_Cyrl' => 'Kazachs (Cyrillisch)',
        'kk_Cyrl_KZ' => 'Kazachs (Cyrillisch, Kazachstan)',
        'kk_KZ' => 'Kazachs (Kazachstan)',
        'kl' => 'Groenlands',
        'kl_GL' => 'Groenlands (Groenland)',
        'km' => 'Khmer',
        'km_KH' => 'Khmer (Cambodja)',
        'kn' => 'Kannada',
        'kn_IN' => 'Kannada (India)',
        'ko' => 'Koreaans',
        'ko_CN' => 'Koreaans (China)',
        'ko_KP' => 'Koreaans (Noord-Korea)',
        'ko_KR' => 'Koreaans (Zuid-Korea)',
        'ks' => 'Kasjmiri',
        'ks_Arab' => 'Kasjmiri (Arabisch)',
        'ks_Arab_IN' => 'Kasjmiri (Arabisch, India)',
        'ks_Deva' => 'Kasjmiri (Devanagari)',
        'ks_Deva_IN' => 'Kasjmiri (Devanagari, India)',
        'ks_IN' => 'Kasjmiri (India)',
        'ku' => 'Koerdisch',
        'ku_TR' => 'Koerdisch (Turkije)',
        'kw' => 'Cornish',
        'kw_GB' => 'Cornish (Verenigd Koninkrijk)',
        'ky' => 'Kirgizisch',
        'ky_KG' => 'Kirgizisch (Kirgizië)',
        'lb' => 'Luxemburgs',
        'lb_LU' => 'Luxemburgs (Luxemburg)',
        'lg' => 'Luganda',
        'lg_UG' => 'Luganda (Oeganda)',
        'ln' => 'Lingala',
        'ln_AO' => 'Lingala (Angola)',
        'ln_CD' => 'Lingala (Congo-Kinshasa)',
        'ln_CF' => 'Lingala (Centraal-Afrikaanse Republiek)',
        'ln_CG' => 'Lingala (Congo-Brazzaville)',
        'lo' => 'Laotiaans',
        'lo_LA' => 'Laotiaans (Laos)',
        'lt' => 'Litouws',
        'lt_LT' => 'Litouws (Litouwen)',
        'lu' => 'Luba-Katanga',
        'lu_CD' => 'Luba-Katanga (Congo-Kinshasa)',
        'lv' => 'Lets',
        'lv_LV' => 'Lets (Letland)',
        'mg' => 'Malagassisch',
        'mg_MG' => 'Malagassisch (Madagaskar)',
        'mi' => 'Maori',
        'mi_NZ' => 'Maori (Nieuw-Zeeland)',
        'mk' => 'Macedonisch',
        'mk_MK' => 'Macedonisch (Noord-Macedonië)',
        'ml' => 'Malayalam',
        'ml_IN' => 'Malayalam (India)',
        'mn' => 'Mongools',
        'mn_MN' => 'Mongools (Mongolië)',
        'mr' => 'Marathi',
        'mr_IN' => 'Marathi (India)',
        'ms' => 'Maleis',
        'ms_BN' => 'Maleis (Brunei)',
        'ms_ID' => 'Maleis (Indonesië)',
        'ms_MY' => 'Maleis (Maleisië)',
        'ms_SG' => 'Maleis (Singapore)',
        'mt' => 'Maltees',
        'mt_MT' => 'Maltees (Malta)',
        'my' => 'Birmaans',
        'my_MM' => 'Birmaans (Myanmar [Birma])',
        'nb' => 'Noors - Bokmål',
        'nb_NO' => 'Noors - Bokmål (Noorwegen)',
        'nb_SJ' => 'Noors - Bokmål (Spitsbergen en Jan Mayen)',
        'nd' => 'Noord-Ndebele',
        'nd_ZW' => 'Noord-Ndebele (Zimbabwe)',
        'ne' => 'Nepalees',
        'ne_IN' => 'Nepalees (India)',
        'ne_NP' => 'Nepalees (Nepal)',
        'nl' => 'Nederlands',
        'nl_AW' => 'Nederlands (Aruba)',
        'nl_BE' => 'Nederlands (België)',
        'nl_BQ' => 'Nederlands (Caribisch Nederland)',
        'nl_CW' => 'Nederlands (Curaçao)',
        'nl_NL' => 'Nederlands (Nederland)',
        'nl_SR' => 'Nederlands (Suriname)',
        'nl_SX' => 'Nederlands (Sint-Maarten)',
        'nn' => 'Noors - Nynorsk',
        'nn_NO' => 'Noors - Nynorsk (Noorwegen)',
        'no' => 'Noors',
        'no_NO' => 'Noors (Noorwegen)',
        'oc' => 'Occitaans',
        'oc_ES' => 'Occitaans (Spanje)',
        'oc_FR' => 'Occitaans (Frankrijk)',
        'om' => 'Afaan Oromo',
        'om_ET' => 'Afaan Oromo (Ethiopië)',
        'om_KE' => 'Afaan Oromo (Kenia)',
        'or' => 'Odia',
        'or_IN' => 'Odia (India)',
        'os' => 'Ossetisch',
        'os_GE' => 'Ossetisch (Georgië)',
        'os_RU' => 'Ossetisch (Rusland)',
        'pa' => 'Punjabi',
        'pa_Arab' => 'Punjabi (Arabisch)',
        'pa_Arab_PK' => 'Punjabi (Arabisch, Pakistan)',
        'pa_Guru' => 'Punjabi (Gurmukhi)',
        'pa_Guru_IN' => 'Punjabi (Gurmukhi, India)',
        'pa_IN' => 'Punjabi (India)',
        'pa_PK' => 'Punjabi (Pakistan)',
        'pl' => 'Pools',
        'pl_PL' => 'Pools (Polen)',
        'ps' => 'Pasjtoe',
        'ps_AF' => 'Pasjtoe (Afghanistan)',
        'ps_PK' => 'Pasjtoe (Pakistan)',
        'pt' => 'Portugees',
        'pt_AO' => 'Portugees (Angola)',
        'pt_BR' => 'Portugees (Brazilië)',
        'pt_CH' => 'Portugees (Zwitserland)',
        'pt_CV' => 'Portugees (Kaapverdië)',
        'pt_GQ' => 'Portugees (Equatoriaal-Guinea)',
        'pt_GW' => 'Portugees (Guinee-Bissau)',
        'pt_LU' => 'Portugees (Luxemburg)',
        'pt_MO' => 'Portugees (Macau SAR van China)',
        'pt_MZ' => 'Portugees (Mozambique)',
        'pt_PT' => 'Portugees (Portugal)',
        'pt_ST' => 'Portugees (Sao Tomé en Principe)',
        'pt_TL' => 'Portugees (Oost-Timor)',
        'qu' => 'Quechua',
        'qu_BO' => 'Quechua (Bolivia)',
        'qu_EC' => 'Quechua (Ecuador)',
        'qu_PE' => 'Quechua (Peru)',
        'rm' => 'Reto-Romaans',
        'rm_CH' => 'Reto-Romaans (Zwitserland)',
        'rn' => 'Kirundi',
        'rn_BI' => 'Kirundi (Burundi)',
        'ro' => 'Roemeens',
        'ro_MD' => 'Roemeens (Moldavië)',
        'ro_RO' => 'Roemeens (Roemenië)',
        'ru' => 'Russisch',
        'ru_BY' => 'Russisch (Belarus)',
        'ru_KG' => 'Russisch (Kirgizië)',
        'ru_KZ' => 'Russisch (Kazachstan)',
        'ru_MD' => 'Russisch (Moldavië)',
        'ru_RU' => 'Russisch (Rusland)',
        'ru_UA' => 'Russisch (Oekraïne)',
        'rw' => 'Kinyarwanda',
        'rw_RW' => 'Kinyarwanda (Rwanda)',
        'sa' => 'Sanskriet',
        'sa_IN' => 'Sanskriet (India)',
        'sc' => 'Sardijns',
        'sc_IT' => 'Sardijns (Italië)',
        'sd' => 'Sindhi',
        'sd_Arab' => 'Sindhi (Arabisch)',
        'sd_Arab_PK' => 'Sindhi (Arabisch, Pakistan)',
        'sd_Deva' => 'Sindhi (Devanagari)',
        'sd_Deva_IN' => 'Sindhi (Devanagari, India)',
        'sd_IN' => 'Sindhi (India)',
        'sd_PK' => 'Sindhi (Pakistan)',
        'se' => 'Noord-Samisch',
        'se_FI' => 'Noord-Samisch (Finland)',
        'se_NO' => 'Noord-Samisch (Noorwegen)',
        'se_SE' => 'Noord-Samisch (Zweden)',
        'sg' => 'Sango',
        'sg_CF' => 'Sango (Centraal-Afrikaanse Republiek)',
        'sh' => 'Servo-Kroatisch',
        'sh_BA' => 'Servo-Kroatisch (Bosnië en Herzegovina)',
        'si' => 'Singalees',
        'si_LK' => 'Singalees (Sri Lanka)',
        'sk' => 'Slowaaks',
        'sk_SK' => 'Slowaaks (Slowakije)',
        'sl' => 'Sloveens',
        'sl_SI' => 'Sloveens (Slovenië)',
        'sn' => 'Shona',
        'sn_ZW' => 'Shona (Zimbabwe)',
        'so' => 'Somalisch',
        'so_DJ' => 'Somalisch (Djibouti)',
        'so_ET' => 'Somalisch (Ethiopië)',
        'so_KE' => 'Somalisch (Kenia)',
        'so_SO' => 'Somalisch (Somalië)',
        'sq' => 'Albanees',
        'sq_AL' => 'Albanees (Albanië)',
        'sq_MK' => 'Albanees (Noord-Macedonië)',
        'sr' => 'Servisch',
        'sr_BA' => 'Servisch (Bosnië en Herzegovina)',
        'sr_Cyrl' => 'Servisch (Cyrillisch)',
        'sr_Cyrl_BA' => 'Servisch (Cyrillisch, Bosnië en Herzegovina)',
        'sr_Cyrl_ME' => 'Servisch (Cyrillisch, Montenegro)',
        'sr_Cyrl_RS' => 'Servisch (Cyrillisch, Servië)',
        'sr_Latn' => 'Servisch (Latijns)',
        'sr_Latn_BA' => 'Servisch (Latijns, Bosnië en Herzegovina)',
        'sr_Latn_ME' => 'Servisch (Latijns, Montenegro)',
        'sr_Latn_RS' => 'Servisch (Latijns, Servië)',
        'sr_ME' => 'Servisch (Montenegro)',
        'sr_RS' => 'Servisch (Servië)',
        'st' => 'Zuid-Sotho',
        'st_LS' => 'Zuid-Sotho (Lesotho)',
        'st_ZA' => 'Zuid-Sotho (Zuid-Afrika)',
        'su' => 'Soendanees',
        'su_ID' => 'Soendanees (Indonesië)',
        'su_Latn' => 'Soendanees (Latijns)',
        'su_Latn_ID' => 'Soendanees (Latijns, Indonesië)',
        'sv' => 'Zweeds',
        'sv_AX' => 'Zweeds (Åland)',
        'sv_FI' => 'Zweeds (Finland)',
        'sv_SE' => 'Zweeds (Zweden)',
        'sw' => 'Swahili',
        'sw_CD' => 'Swahili (Congo-Kinshasa)',
        'sw_KE' => 'Swahili (Kenia)',
        'sw_TZ' => 'Swahili (Tanzania)',
        'sw_UG' => 'Swahili (Oeganda)',
        'ta' => 'Tamil',
        'ta_IN' => 'Tamil (India)',
        'ta_LK' => 'Tamil (Sri Lanka)',
        'ta_MY' => 'Tamil (Maleisië)',
        'ta_SG' => 'Tamil (Singapore)',
        'te' => 'Telugu',
        'te_IN' => 'Telugu (India)',
        'tg' => 'Tadzjieks',
        'tg_TJ' => 'Tadzjieks (Tadzjikistan)',
        'th' => 'Thai',
        'th_TH' => 'Thai (Thailand)',
        'ti' => 'Tigrinya',
        'ti_ER' => 'Tigrinya (Eritrea)',
        'ti_ET' => 'Tigrinya (Ethiopië)',
        'tk' => 'Turkmeens',
        'tk_TM' => 'Turkmeens (Turkmenistan)',
        'tl' => 'Tagalog',
        'tl_PH' => 'Tagalog (Filipijnen)',
        'tn' => 'Tswana',
        'tn_BW' => 'Tswana (Botswana)',
        'tn_ZA' => 'Tswana (Zuid-Afrika)',
        'to' => 'Tongaans',
        'to_TO' => 'Tongaans (Tonga)',
        'tr' => 'Turks',
        'tr_CY' => 'Turks (Cyprus)',
        'tr_TR' => 'Turks (Turkije)',
        'tt' => 'Tataars',
        'tt_RU' => 'Tataars (Rusland)',
        'ug' => 'Oeigoers',
        'ug_CN' => 'Oeigoers (China)',
        'uk' => 'Oekraïens',
        'uk_UA' => 'Oekraïens (Oekraïne)',
        'ur' => 'Urdu',
        'ur_IN' => 'Urdu (India)',
        'ur_PK' => 'Urdu (Pakistan)',
        'uz' => 'Oezbeeks',
        'uz_AF' => 'Oezbeeks (Afghanistan)',
        'uz_Arab' => 'Oezbeeks (Arabisch)',
        'uz_Arab_AF' => 'Oezbeeks (Arabisch, Afghanistan)',
        'uz_Cyrl' => 'Oezbeeks (Cyrillisch)',
        'uz_Cyrl_UZ' => 'Oezbeeks (Cyrillisch, Oezbekistan)',
        'uz_Latn' => 'Oezbeeks (Latijns)',
        'uz_Latn_UZ' => 'Oezbeeks (Latijns, Oezbekistan)',
        'uz_UZ' => 'Oezbeeks (Oezbekistan)',
        'vi' => 'Vietnamees',
        'vi_VN' => 'Vietnamees (Vietnam)',
        'wo' => 'Wolof',
        'wo_SN' => 'Wolof (Senegal)',
        'xh' => 'Xhosa',
        'xh_ZA' => 'Xhosa (Zuid-Afrika)',
        'yi' => 'Jiddisch',
        'yi_UA' => 'Jiddisch (Oekraïne)',
        'yo' => 'Yoruba',
        'yo_BJ' => 'Yoruba (Benin)',
        'yo_NG' => 'Yoruba (Nigeria)',
        'za' => 'Zhuang',
        'za_CN' => 'Zhuang (China)',
        'zh' => 'Chinees',
        'zh_CN' => 'Chinees (China)',
        'zh_HK' => 'Chinees (Hongkong SAR van China)',
        'zh_Hans' => 'Chinees (vereenvoudigd)',
        'zh_Hans_CN' => 'Chinees (vereenvoudigd, China)',
        'zh_Hans_HK' => 'Chinees (vereenvoudigd, Hongkong SAR van China)',
        'zh_Hans_MO' => 'Chinees (vereenvoudigd, Macau SAR van China)',
        'zh_Hans_MY' => 'Chinees (vereenvoudigd, Maleisië)',
        'zh_Hans_SG' => 'Chinees (vereenvoudigd, Singapore)',
        'zh_Hant' => 'Chinees (traditioneel)',
        'zh_Hant_HK' => 'Chinees (traditioneel, Hongkong SAR van China)',
        'zh_Hant_MO' => 'Chinees (traditioneel, Macau SAR van China)',
        'zh_Hant_MY' => 'Chinees (traditioneel, Maleisië)',
        'zh_Hant_TW' => 'Chinees (traditioneel, Taiwan)',
        'zh_MO' => 'Chinees (Macau SAR van China)',
        'zh_SG' => 'Chinees (Singapore)',
        'zh_TW' => 'Chinees (Taiwan)',
        'zu' => 'Zoeloe',
        'zu_ZA' => 'Zoeloe (Zuid-Afrika)',
    ],
];
