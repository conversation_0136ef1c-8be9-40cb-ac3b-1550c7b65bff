<?php

return [
    'Names' => [
        'Adlm' => 'tohinima fakaʻatilami',
        'Afak' => 'tohinima fakaʻafaka',
        'Aghb' => 'tohinima fakaʻalapēnia-kaukasia',
        'Arab' => 'tohinima fakaʻalepea',
        'Aran' => 'tohinima fakanasatalīki',
        'Armi' => 'tohinima fakaʻalāmiti-ʻemipaea',
        'Armn' => 'tohinima fakaʻāmenia',
        'Avst' => 'tohinima fakaʻavesitani',
        'Bali' => 'tohinima fakapali',
        'Bamu' => 'tohinima fakapamumi',
        'Bass' => 'tohinima fakapasa-vā',
        'Batk' => 'tohinima fakapātaki',
        'Beng' => 'tohinima fakapāngilā',
        'Blis' => 'tohinima fakaʻilonga-pilisi',
        'Bo<PERSON>' => 'tohinima fakapopomofo',
        'Brah' => 'tohinima fakapalāmī',
        'Brai' => 'tohinima laukonga ki he kui',
        'Bugi' => 'tohinima fakapukisi',
        'Buhd' => 'tohinima fakapuhiti',
        'Cakm' => 'tohinima fakasakimā',
        'Cans' => 'tohinima fakatupuʻi-kānata-fakatahataha',
        'Cari' => 'tohinima fakakali',
        'Cham' => 'tohinima fakasami',
        'Cher' => 'tohinima fakaselokī',
        'Cirt' => 'tohinima fakakīliti',
        'Copt' => 'tohinima fakakopitika',
        'Cprt' => 'tohinima fakasaipalesi',
        'Cyrl' => 'tohinima fakalūsia',
        'Cyrs' => 'tohinima fakalūsia-lotu-motuʻa',
        'Deva' => 'tohinima fakaʻinitia-tevanākalī',
        'Dsrt' => 'tohinima fakateseleti',
        'Dupl' => 'tohinimanounou fakatupoloiē',
        'Egyd' => 'tohinima temotika-fakaʻisipite',
        'Egyh' => 'tohinima hielatika-fakaʻisipite',
        'Egyp' => 'tohinima tongitapu-fakaʻisipite',
        'Elba' => 'tohinima fakaʻelepasani',
        'Ethi' => 'tohinima fakaʻītiōpia',
        'Geok' => 'tohinima fakakutusuli-seōsia',
        'Geor' => 'tohinima fakaseōsia',
        'Glag' => 'tohinima fakakalakoliti',
        'Goth' => 'tohinima fakakotika',
        'Gran' => 'tohinima fakasilanitā',
        'Grek' => 'tohinima fakakalisi',
        'Gujr' => 'tohinima fakaʻinitia-kutalati',
        'Guru' => 'tohinima fakakūmuki',
        'Hanb' => 'tohinima fakahānipi',
        'Hang' => 'tohinima fakakōlea-hāngūlu',
        'Hani' => 'tohinima fakasiaina',
        'Hano' => 'tohinima fakahanunōʻo',
        'Hans' => 'fakafaingofua',
        'Hant' => 'tukufakaholo',
        'Hebr' => 'tohinima fakahepelū',
        'Hira' => 'tohinima fakasiapani-hilakana',
        'Hluw' => 'tohinima tongitapu-fakaʻanatolia',
        'Hmng' => 'tohinima fakapahaumongi',
        'Hrkt' => 'tohinima fakasilapa-siapani',
        'Hung' => 'tohinima fakahungakalia-motuʻa',
        'Inds' => 'tohinima fakaʻinitusi',
        'Ital' => 'tohinima fakaʻītali-motuʻa',
        'Jamo' => 'tohinima fakasamo',
        'Java' => 'tohinima fakasava',
        'Jpan' => 'tohinima fakasiapani',
        'Jurc' => 'tohinima fakaiūkeni',
        'Kali' => 'tohinima fakakaialī',
        'Kana' => 'tohinima fakasiapani-katakana',
        'Khar' => 'tohinima fakakalositī',
        'Khmr' => 'tohinima fakakamipōtia',
        'Khoj' => 'tohinima fakakosikī',
        'Knda' => 'tohinima fakaʻinitia-kanata',
        'Kore' => 'tohinima fakakōlea',
        'Kpel' => 'tohinima fakakepele',
        'Kthi' => 'tohinima fakakaiatī',
        'Lana' => 'tohinima fakalana',
        'Laoo' => 'tohinima fakalau',
        'Latf' => 'tohinima fakalatina-falakituli',
        'Latg' => 'tohinima fakalatina-kaeliki',
        'Latn' => 'tohinima fakalatina',
        'Lepc' => 'tohinima fakalepasā',
        'Limb' => 'tohinima fakalimipū',
        'Lina' => 'tohinima fakalinea-A',
        'Linb' => 'tohinima fakalinea-P',
        'Lisu' => 'tohinima fakafalāse',
        'Loma' => 'tohinima fakaloma',
        'Lyci' => 'tohinima fakalīsia',
        'Lydi' => 'tohinima fakalītia',
        'Mahj' => 'tohinima fakamahasani',
        'Mand' => 'tohinima fakamanitaea',
        'Mani' => 'tohinima fakamanikaea',
        'Maya' => 'tohinima tongitapu fakamaia',
        'Mend' => 'tohinima fakamēniti',
        'Merc' => 'tohinima fakameloue-heihei',
        'Mero' => 'tohinima fakameloue',
        'Mlym' => 'tohinima fakaʻinitia-malāialami',
        'Modi' => 'tohinima fakamotī',
        'Mong' => 'tohinima fakamongokōlia',
        'Moon' => 'tohinima laukonga ki he kui-māhina',
        'Mroo' => 'tohinima fakamolō',
        'Mtei' => 'tohinima fakametei-maieki',
        'Mymr' => 'tohinima fakapema',
        'Narb' => 'tohinima fakaʻalepea-tokelau-motuʻa',
        'Nbat' => 'tohinima fakanapatea',
        'Nkgb' => 'tohinima fakanati-sepa',
        'Nkoo' => 'tohinima fakanikō',
        'Nshu' => 'tohinima fakanasiū',
        'Ogam' => 'tohinima fakaʻokami',
        'Olck' => 'tohinima fakaʻolisiki',
        'Orkh' => 'tohinima fakaʻolikoni',
        'Orya' => 'tohinima fakaʻotia',
        'Osma' => 'tohinima fakaʻosimānia',
        'Palm' => 'tohinima fakapalamilene',
        'Pauc' => 'tohinima fakapausinihau',
        'Perm' => 'tohinima fakapēmi-motuʻa',
        'Phag' => 'tohinima fakapākisipā',
        'Phli' => 'tohinima fakapālavi-tongi',
        'Phlp' => 'tohinima fakapālavi-saame',
        'Phlv' => 'tohinima fakapālavi-tohi',
        'Phnx' => 'tohinima fakafoinikia',
        'Plrd' => 'tohinima fakafonētiki-polāti',
        'Prti' => 'tohinima fakapātia-tongi',
        'Rjng' => 'tohinima fakalesiangi',
        'Rohg' => 'tohinima fakahanifi-lohingia',
        'Roro' => 'tohinima fakalongolongo',
        'Runr' => 'tohinima fakaluniki',
        'Samr' => 'tohinima fakasamalitane',
        'Sara' => 'tohinima fakasalati',
        'Sarb' => 'tohinima fakaʻalepea-tonga-motuʻa',
        'Saur' => 'tohinima fakasaulasitā',
        'Sgnw' => 'tohinima fakaʻilonga-tohi',
        'Shaw' => 'tohinima fakasiavi',
        'Shrd' => 'tohinima fakasiālatā',
        'Sidd' => 'tohinima fakasititami',
        'Sind' => 'tohinima fakakutauāti',
        'Sinh' => 'tohinima fakasingihala',
        'Sora' => 'tohinima fakasolasomipengi',
        'Sund' => 'tohinima fakasunitā',
        'Sylo' => 'tohinima fakasailoti-nakili',
        'Syrc' => 'tohinima fakasuliāiā',
        'Syre' => 'tohinima fakasuliāiā-ʻesitelangelo',
        'Syrj' => 'tohinima fakasuliāiā-hihifo',
        'Syrn' => 'tohinima fakasuliāiā-hahake',
        'Tagb' => 'tohinima fakatakipaneuā',
        'Takr' => 'tohinima fakatakili',
        'Tale' => 'tohinima fakatai-lue',
        'Talu' => 'tohinima fakatai-lue-foʻou',
        'Taml' => 'tohinima fakatamili',
        'Tang' => 'tohinima fakatanguti',
        'Tavt' => 'tohinima fakatai-vieti',
        'Telu' => 'tohinima fakaʻinitia-teluku',
        'Teng' => 'tohinima fakatengiuali',
        'Tfng' => 'tohinima fakatifināki',
        'Tglg' => 'tohinima fakatakaloka',
        'Thaa' => 'tohinima fakatāna',
        'Thai' => 'tohinima fakatailani',
        'Tibt' => 'tohinima fakataipeti',
        'Tirh' => 'tohinima fakatīhuta',
        'Ugar' => 'tohinima fakaʻūkaliti',
        'Vaii' => 'tohinima fakavai',
        'Visp' => 'tohinima fakafonētiki-hāmai',
        'Wara' => 'tohinima fakavalangi-kisitī',
        'Wole' => 'tohinima fakauoleai',
        'Xpeo' => 'tohinima fakapēsiamuʻa',
        'Xsux' => 'tohinima fakamataʻingahau-sumelo-akatia',
        'Yiii' => 'tohinima fakaīī',
        'Zinh' => 'tohinima hokosi',
        'Zmth' => 'tohinima fakamatematika',
        'Zsye' => 'tohinima fakatātā',
        'Zsym' => 'tohinima fakaʻilonga',
        'Zxxx' => 'tohinima taʻetohitohiʻi',
        'Zyyy' => 'tohinima fakatatau',
    ],
];
