<?php

return [
    'Names' => [
        'aa' => 'afari',
        'ab' => 'abhaasi',
        'ace' => 'atšehi',
        'ach' => 'atšoli',
        'ada' => 'adangme',
        'ady' => 'adõgee',
        'ae' => 'avesta',
        'aeb' => 'Tuneesia araabia',
        'af' => 'afrikaani',
        'afh' => 'afrihili',
        'agq' => 'aghemi',
        'ain' => 'ainu',
        'ak' => 'akani',
        'akk' => 'akadi',
        'akz' => 'alabama',
        'ale' => 'aleuudi',
        'aln' => 'geegi',
        'alt' => 'altai',
        'am' => 'amhara',
        'an' => 'aragoni',
        'ang' => 'vanainglise',
        'ann' => 'obolo',
        'anp' => 'angika',
        'apc' => 'Levandi araabia',
        'ar' => 'araabia',
        'arc' => 'aramea',
        'arn' => 'mapudunguni',
        'aro' => 'araona',
        'arp' => 'arapaho',
        'arq' => 'Alžeeria araabia',
        'ars' => 'Najdi araabia',
        'arw' => 'aravaki',
        'ary' => 'Maroko araabia',
        'arz' => 'Egiptuse araabia',
        'as' => 'assami',
        'asa' => 'asu',
        'ase' => 'Ameerika viipekeel',
        'ast' => 'astuuria',
        'atj' => 'atikameki',
        'av' => 'avaari',
        'awa' => 'avadhi',
        'ay' => 'aimara',
        'az' => 'aserbaidžaani',
        'ba' => 'baškiiri',
        'bal' => 'belutši',
        'ban' => 'bali',
        'bar' => 'baieri',
        'bas' => 'basaa',
        'bax' => 'bamuni',
        'bbc' => 'bataki',
        'bbj' => 'ghomala',
        'be' => 'valgevene',
        'bej' => 'bedža',
        'bem' => 'bemba',
        'bew' => 'betavi',
        'bez' => 'bena',
        'bfd' => 'bafuti',
        'bfq' => 'badaga',
        'bg' => 'bulgaaria',
        'bgc' => 'harjaanvi',
        'bgn' => 'läänebelutši',
        'bho' => 'bhodžpuri',
        'bi' => 'bislama',
        'bik' => 'bikoli',
        'bin' => 'edo',
        'bjn' => 'bandžari',
        'bkm' => 'komi (Aafrika)',
        'bla' => 'mustjalaindiaani',
        'blo' => 'anii',
        'blt' => 'tai-dami',
        'bm' => 'bambara',
        'bn' => 'bengali',
        'bo' => 'tiibeti',
        'bpy' => 'bišnuprija',
        'bqi' => 'bahtiari',
        'br' => 'bretooni',
        'bra' => 'bradži',
        'brh' => 'brahui',
        'brx' => 'bodo',
        'bs' => 'bosnia',
        'bss' => 'akoose',
        'bua' => 'burjaadi',
        'bug' => 'bugi',
        'bum' => 'bulu',
        'byn' => 'bilini',
        'byv' => 'medumba',
        'ca' => 'katalaani',
        'cad' => 'kado',
        'car' => 'kariibi',
        'cay' => 'kajuka',
        'cch' => 'aitšami',
        'ccp' => 'tšaakma',
        'ce' => 'tšetšeeni',
        'ceb' => 'sebu',
        'cgg' => 'tšiga',
        'ch' => 'tšamorro',
        'chb' => 'tšibtša',
        'chg' => 'tšagatai',
        'chk' => 'tšuugi',
        'chm' => 'mari',
        'chn' => 'tšinuki žargoon',
        'cho' => 'tšokto',
        'chp' => 'tšipevai',
        'chr' => 'tšerokii',
        'chy' => 'šaieeni',
        'cic' => 'tšikasoo',
        'ckb' => 'sorani',
        'clc' => 'tšilkotini',
        'co' => 'korsika',
        'cop' => 'kopti',
        'cps' => 'kapisnoni',
        'cr' => 'krii',
        'crg' => 'michifi',
        'crh' => 'krimmitatari',
        'crj' => 'lõuna-idakrii',
        'crk' => 'tasandikukrii',
        'crl' => 'põhja-idakrii',
        'crm' => 'põdrakrii',
        'crr' => 'Carolina algonkini',
        'crs' => 'seišelli',
        'cs' => 'tšehhi',
        'csb' => 'kašuubi',
        'csw' => 'sookrii',
        'cu' => 'kirikuslaavi',
        'cv' => 'tšuvaši',
        'cy' => 'kõmri',
        'da' => 'taani',
        'dak' => 'siuu',
        'dar' => 'dargi',
        'dav' => 'davida',
        'de' => 'saksa',
        'del' => 'delavari',
        'den' => 'sleivi',
        'dgr' => 'dogribi',
        'din' => 'dinka',
        'dje' => 'zarma',
        'doi' => 'dogri',
        'dsb' => 'alamsorbi',
        'dtp' => 'keskdusuni',
        'dua' => 'duala',
        'dum' => 'keskhollandi',
        'dv' => 'maldiivi',
        'dyo' => 'fonji',
        'dyu' => 'djula',
        'dz' => 'dzongkha',
        'dzg' => 'daza',
        'ebu' => 'embu',
        'ee' => 'eve',
        'efi' => 'efiki',
        'egl' => 'emiilia',
        'egy' => 'egiptuse',
        'eka' => 'ekadžuki',
        'el' => 'kreeka',
        'elx' => 'eelami',
        'en' => 'inglise',
        'enm' => 'keskinglise',
        'eo' => 'esperanto',
        'es' => 'hispaania',
        'esu' => 'keskjupiki',
        'et' => 'eesti',
        'eu' => 'baski',
        'ewo' => 'evondo',
        'ext' => 'estremenju',
        'fa' => 'pärsia',
        'fan' => 'fangi',
        'fat' => 'fanti',
        'ff' => 'fula',
        'fi' => 'soome',
        'fil' => 'filipiini',
        'fit' => 'meä',
        'fj' => 'fidži',
        'fo' => 'fääri',
        'fon' => 'foni',
        'fr' => 'prantsuse',
        'frc' => 'cajun’i',
        'frm' => 'keskprantsuse',
        'fro' => 'vanaprantsuse',
        'frp' => 'frankoprovansi',
        'frr' => 'põhjafriisi',
        'frs' => 'idafriisi',
        'fur' => 'friuuli',
        'fy' => 'läänefriisi',
        'ga' => 'iiri',
        'gag' => 'gagauusi',
        'gan' => 'kani',
        'gay' => 'gajo',
        'gba' => 'gbaja',
        'gd' => 'gaeli',
        'gez' => 'etioopia',
        'gil' => 'kiribati',
        'gl' => 'galeegi',
        'glk' => 'gilaki',
        'gmh' => 'keskülemsaksa',
        'gn' => 'guaranii',
        'goh' => 'vanaülemsaksa',
        'gon' => 'gondi',
        'gor' => 'gorontalo',
        'got' => 'gooti',
        'grb' => 'grebo',
        'grc' => 'vanakreeka',
        'gsw' => 'šveitsisaksa',
        'gu' => 'gudžarati',
        'guc' => 'vajuu',
        'gur' => 'farefare',
        'guz' => 'gusii',
        'gv' => 'mänksi',
        'gwi' => 'gvitšini',
        'ha' => 'hausa',
        'hai' => 'haida',
        'hak' => 'hakka',
        'haw' => 'havai',
        'hax' => 'lõunahaida',
        'he' => 'heebrea',
        'hi' => 'hindi',
        'hif' => 'Fidži hindi',
        'hil' => 'hiligainoni',
        'hit' => 'heti',
        'hmn' => 'hmongi',
        'ho' => 'hirimotu',
        'hr' => 'horvaadi',
        'hsb' => 'ülemsorbi',
        'hsn' => 'sjangi',
        'ht' => 'haiti',
        'hu' => 'ungari',
        'hup' => 'hupa',
        'hur' => 'halkomelemi',
        'hy' => 'armeenia',
        'hz' => 'herero',
        'ia' => 'interlingua',
        'iba' => 'ibani',
        'ibb' => 'ibibio',
        'id' => 'indoneesia',
        'ie' => 'interlingue',
        'ig' => 'ibo',
        'ii' => 'nuosu',
        'ik' => 'injupiaki',
        'ikt' => 'Lääne-Kanada inuktituti',
        'ilo' => 'iloko',
        'inh' => 'inguši',
        'io' => 'ido',
        'is' => 'islandi',
        'it' => 'itaalia',
        'iu' => 'inuktituti',
        'izh' => 'isuri',
        'ja' => 'jaapani',
        'jam' => 'Jamaica kreoolkeel',
        'jbo' => 'ložban',
        'jgo' => 'ngomba',
        'jmc' => 'matšame',
        'jpr' => 'juudipärsia',
        'jrb' => 'juudiaraabia',
        'jut' => 'jüüti',
        'jv' => 'jaava',
        'ka' => 'gruusia',
        'kaa' => 'karakalpaki',
        'kab' => 'kabiili',
        'kac' => 'katšini',
        'kaj' => 'jju',
        'kam' => 'kamba',
        'kaw' => 'kaavi',
        'kbd' => 'kabardi-tšerkessi',
        'kbl' => 'kanembu',
        'kcg' => 'tjapi',
        'kde' => 'makonde',
        'kea' => 'kabuverdianu',
        'kfo' => 'koro',
        'kg' => 'kongo',
        'kgp' => 'kaingangi',
        'kha' => 'khasi',
        'kho' => 'saka',
        'khq' => 'koyra chiini',
        'khw' => 'khovari',
        'ki' => 'kikuju',
        'kiu' => 'kõrmandžki',
        'kj' => 'kvanjama',
        'kk' => 'kasahhi',
        'kkj' => 'kako',
        'kl' => 'grööni',
        'kln' => 'kalendžini',
        'km' => 'khmeeri',
        'kmb' => 'mbundu',
        'kn' => 'kannada',
        'ko' => 'korea',
        'koi' => 'permikomi',
        'kok' => 'konkani',
        'kos' => 'kosrae',
        'kpe' => 'kpelle',
        'kr' => 'kanuri',
        'krc' => 'karatšai-balkaari',
        'kri' => 'krio',
        'krj' => 'kinaraia',
        'krl' => 'karjala',
        'kru' => 'kuruhhi',
        'ks' => 'kašmiiri',
        'ksb' => 'šambala',
        'ksf' => 'bafia',
        'ksh' => 'kölni',
        'ku' => 'kurdi',
        'kum' => 'kumõki',
        'kut' => 'kutenai',
        'kv' => 'komi',
        'kw' => 'korni',
        'kwk' => 'kvakvala',
        'kxv' => 'kuvi',
        'ky' => 'kirgiisi',
        'la' => 'ladina',
        'lad' => 'ladiino',
        'lag' => 'langi',
        'lah' => 'lahnda',
        'lam' => 'lamba',
        'lb' => 'letseburgi',
        'lez' => 'lesgi',
        'lg' => 'ganda',
        'li' => 'limburgi',
        'lij' => 'liguuri',
        'lil' => 'lillueti',
        'liv' => 'liivi',
        'lkt' => 'lakota',
        'lld' => 'ladiini',
        'lmo' => 'lombardi',
        'ln' => 'lingala',
        'lo' => 'lao',
        'lol' => 'mongo',
        'lou' => 'Louisiana kreoolkeel',
        'loz' => 'lozi',
        'lrc' => 'põhjaluri',
        'lsm' => 'samia',
        'lt' => 'leedu',
        'ltg' => 'latgali',
        'lu' => 'Katanga luba',
        'lua' => 'lulua',
        'lui' => 'luisenjo',
        'lun' => 'lunda',
        'lus' => 'lušei',
        'luy' => 'luhja',
        'lv' => 'läti',
        'lzh' => 'klassikaline hiina',
        'lzz' => 'lazi',
        'mad' => 'madura',
        'maf' => 'mafa',
        'mag' => 'magahi',
        'mai' => 'maithili',
        'mak' => 'makassari',
        'man' => 'malinke',
        'mas' => 'masai',
        'mde' => 'maba',
        'mdf' => 'mokša',
        'mdr' => 'mandari',
        'men' => 'mende',
        'mer' => 'meru',
        'mfe' => 'Mauritiuse kreoolkeel',
        'mg' => 'malagassi',
        'mga' => 'keskiiri',
        'mgh' => 'makhuwa-meetto',
        'mgo' => 'meta',
        'mh' => 'maršalli',
        'mhn' => 'mohheni',
        'mi' => 'maoori',
        'mic' => 'mikmaki',
        'min' => 'minangkabau',
        'mk' => 'makedoonia',
        'ml' => 'malajalami',
        'mn' => 'mongoli',
        'mnc' => 'mandžu',
        'mni' => 'manipuri',
        'moe' => 'innu',
        'moh' => 'mohoogi',
        'mos' => 'more',
        'mr' => 'marathi',
        'mrj' => 'mäemari',
        'ms' => 'malai',
        'mt' => 'malta',
        'mua' => 'mundangi',
        'mus' => 'maskogi',
        'mwl' => 'miranda',
        'mwr' => 'marvari',
        'mwv' => 'mentavei',
        'my' => 'birma',
        'mye' => 'mjene',
        'myv' => 'ersa',
        'mzn' => 'mazandaraani',
        'na' => 'nauru',
        'nan' => 'lõunamini',
        'nap' => 'napoli',
        'naq' => 'nama',
        'nb' => 'norra bokmål',
        'nd' => 'põhjandebele',
        'nds' => 'alamsaksa',
        'ne' => 'nepali',
        'new' => 'nevari',
        'ng' => 'ndonga',
        'nia' => 'niasi',
        'niu' => 'niue',
        'njo' => 'ao',
        'nl' => 'hollandi',
        'nmg' => 'kwasio',
        'nn' => 'uusnorra',
        'nnh' => 'ngiembooni',
        'no' => 'norra',
        'nog' => 'nogai',
        'non' => 'vanapõhja',
        'nov' => 'noviaal',
        'nqo' => 'nkoo',
        'nr' => 'lõunandebele',
        'nso' => 'põhjasotho',
        'nus' => 'nueri',
        'nv' => 'navaho',
        'nwc' => 'vananevari',
        'ny' => 'njandža',
        'nym' => 'njamvesi',
        'nyn' => 'njankole',
        'nyo' => 'njoro',
        'nzi' => 'nzima',
        'oc' => 'oksitaani',
        'oj' => 'odžibvei',
        'ojb' => 'loodeodžibvei',
        'ojc' => 'keskodžibvei',
        'ojs' => 'Severni odžibvei',
        'ojw' => 'lääneodžibvei',
        'oka' => 'okanagani',
        'om' => 'oromo',
        'or' => 'oria',
        'os' => 'osseedi',
        'osa' => 'oseidži',
        'ota' => 'osmanitürgi',
        'pa' => 'pandžabi',
        'pag' => 'pangasinani',
        'pal' => 'pahlavi',
        'pam' => 'pampanga',
        'pap' => 'papiamento',
        'pau' => 'belau',
        'pcd' => 'pikardi',
        'pcm' => 'Nigeeria pidžinkeel',
        'pdc' => 'Pennsylvania saksa',
        'pdt' => 'mennoniidisaksa',
        'peo' => 'vanapärsia',
        'pfl' => 'Pfalzi',
        'phn' => 'foiniikia',
        'pi' => 'paali',
        'pis' => 'pijini',
        'pl' => 'poola',
        'pms' => 'piemonte',
        'pnt' => 'pontose',
        'pon' => 'poonpei',
        'pqm' => 'passamakodi',
        'prg' => 'preisi',
        'pro' => 'vanaprovansi',
        'ps' => 'puštu',
        'pt' => 'portugali',
        'qu' => 'ketšua',
        'quc' => 'kitše',
        'raj' => 'radžastani',
        'rap' => 'rapanui',
        'rar' => 'rarotonga',
        'rgn' => 'romanja',
        'rhg' => 'rohingja',
        'rif' => 'riifi',
        'rm' => 'romanši',
        'rn' => 'rundi',
        'ro' => 'rumeenia',
        'rof' => 'rombo',
        'rom' => 'mustlaskeel',
        'rtm' => 'rotuma',
        'ru' => 'vene',
        'rue' => 'russiini',
        'rug' => 'roviana',
        'rup' => 'aromuuni',
        'rw' => 'ruanda',
        'rwk' => 'rvaa',
        'sa' => 'sanskriti',
        'sad' => 'sandave',
        'sah' => 'jakuudi',
        'sam' => 'Samaaria aramea',
        'saq' => 'samburu',
        'sas' => 'sasaki',
        'sat' => 'santali',
        'saz' => 'sauraštra',
        'sba' => 'ngambai',
        'sbp' => 'sangu',
        'sc' => 'sardi',
        'scn' => 'sitsiilia',
        'sco' => 'šoti',
        'sd' => 'sindhi',
        'sdh' => 'lõunakurdi',
        'se' => 'põhjasaami',
        'see' => 'seneka',
        'seh' => 'sena',
        'sei' => 'seri',
        'sel' => 'sölkupi',
        'ses' => 'koyraboro senni',
        'sg' => 'sango',
        'sga' => 'vanaiiri',
        'sgs' => 'žemaidi',
        'sh' => 'serbia-horvaadi',
        'shi' => 'šilha',
        'shn' => 'šani',
        'shu' => 'Tšaadi araabia',
        'si' => 'singali',
        'sid' => 'sidamo',
        'sk' => 'slovaki',
        'skr' => 'seraiki',
        'sl' => 'sloveeni',
        'slh' => 'Lõuna-Puget-Soundi sališi',
        'sli' => 'alamsileesia',
        'sly' => 'selajari',
        'sm' => 'samoa',
        'sma' => 'lõunasaami',
        'smj' => 'Lule saami',
        'smn' => 'Inari saami',
        'sms' => 'koltasaami',
        'sn' => 'šona',
        'snk' => 'soninke',
        'so' => 'somaali',
        'sog' => 'sogdi',
        'sq' => 'albaania',
        'sr' => 'serbia',
        'srn' => 'sranani',
        'srr' => 'sereri',
        'ss' => 'svaasi',
        'ssy' => 'saho',
        'st' => 'lõunasotho',
        'stq' => 'saterfriisi',
        'str' => 'väinasališi',
        'su' => 'sunda',
        'suk' => 'sukuma',
        'sus' => 'susu',
        'sux' => 'sumeri',
        'sv' => 'rootsi',
        'sw' => 'suahiili',
        'swb' => 'komoori',
        'syc' => 'vanasüüria',
        'syr' => 'süüria',
        'szl' => 'sileesia',
        'ta' => 'tamili',
        'tce' => 'lõunatutšoni',
        'tcy' => 'tulu',
        'te' => 'telugu',
        'tem' => 'temne',
        'teo' => 'teso',
        'ter' => 'tereno',
        'tet' => 'tetumi',
        'tg' => 'tadžiki',
        'tgx' => 'tagishi',
        'th' => 'tai',
        'tht' => 'tahltani',
        'ti' => 'tigrinja',
        'tig' => 'tigree',
        'tiv' => 'tivi',
        'tk' => 'türkmeeni',
        'tkl' => 'tokelau',
        'tkr' => 'tsahhi',
        'tl' => 'tagalogi',
        'tlh' => 'klingoni',
        'tli' => 'tlingiti',
        'tly' => 'talõši',
        'tmh' => 'tamašeki',
        'tn' => 'tsvana',
        'to' => 'tonga',
        'tog' => 'tšitonga',
        'tok' => 'toki pona',
        'tpi' => 'uusmelaneesia',
        'tr' => 'türgi',
        'tru' => 'turojo',
        'trv' => 'taroko',
        'trw' => 'torvali',
        'ts' => 'tsonga',
        'tsd' => 'tsakoonia',
        'tsi' => 'tsimši',
        'tt' => 'tatari',
        'ttm' => 'põhjatutšoni',
        'ttt' => 'lõunataadi',
        'tum' => 'tumbuka',
        'tvl' => 'tuvalu',
        'tw' => 'tvii',
        'twq' => 'taswaqi',
        'ty' => 'tahiti',
        'tyv' => 'tõva',
        'tzm' => 'tamasikti',
        'udm' => 'udmurdi',
        'ug' => 'uiguuri',
        'uga' => 'ugariti',
        'uk' => 'ukraina',
        'umb' => 'umbundu',
        'ur' => 'urdu',
        'uz' => 'usbeki',
        've' => 'venda',
        'vec' => 'veneti',
        'vep' => 'vepsa',
        'vi' => 'vietnami',
        'vls' => 'lääneflaami',
        'vmf' => 'Maini frangi',
        'vmw' => 'makua',
        'vo' => 'volapüki',
        'vot' => 'vadja',
        'vro' => 'võru',
        'vun' => 'vundžo',
        'wa' => 'vallooni',
        'wae' => 'valsi',
        'wal' => 'volaita',
        'war' => 'varai',
        'was' => 'vašo',
        'wbp' => 'varlpiri',
        'wo' => 'volofi',
        'wuu' => 'uu',
        'xal' => 'kalmõki',
        'xh' => 'koosa',
        'xmf' => 'megreli',
        'xnr' => 'kangri',
        'xog' => 'soga',
        'yao' => 'jao',
        'yap' => 'japi',
        'yav' => 'yangbeni',
        'ybb' => 'jemba',
        'yi' => 'jidiši',
        'yo' => 'joruba',
        'yrl' => 'njengatu',
        'yue' => 'kantoni',
        'za' => 'tšuangi',
        'zap' => 'sapoteegi',
        'zbl' => 'Blissi sümbolid',
        'zea' => 'zeelandi',
        'zen' => 'zenaga',
        'zgh' => 'tamasikti (Maroko)',
        'zh' => 'hiina',
        'zu' => 'suulu',
        'zun' => 'sunji',
        'zza' => 'zaza',
    ],
    'LocalizedNames' => [
        'ar_001' => 'tänapäeva araabia kirjakeel',
        'de_AT' => 'Austria saksa',
        'de_CH' => 'Šveitsi ülemsaksa',
        'en_AU' => 'Austraalia inglise',
        'en_CA' => 'Kanada inglise',
        'en_GB' => 'Briti inglise',
        'en_US' => 'Ameerika inglise',
        'es_419' => 'Ladina-Ameerika hispaania',
        'es_ES' => 'Euroopa hispaania',
        'es_MX' => 'Mehhiko hispaania',
        'fa_AF' => 'dari',
        'fr_CA' => 'Kanada prantsuse',
        'fr_CH' => 'Šveitsi prantsuse',
        'nds_NL' => 'Hollandi alamsaksa',
        'nl_BE' => 'flaami',
        'pt_BR' => 'Brasiilia portugali',
        'pt_PT' => 'Euroopa portugali',
        'ro_MD' => 'moldova',
        'sw_CD' => 'Kongo suahiili',
        'zh_Hans' => 'lihtsustatud hiina',
        'zh_Hant' => 'traditsiooniline hiina',
    ],
];
