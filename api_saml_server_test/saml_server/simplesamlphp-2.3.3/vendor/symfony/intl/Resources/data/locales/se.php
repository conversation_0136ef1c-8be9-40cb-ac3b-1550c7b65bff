<?php

return [
    'Names' => [
        'af' => 'afrik<PERSON><PERSON><PERSON><PERSON>',
        'af_NA' => 'af<PERSON><PERSON><PERSON><PERSON><PERSON> (Namibia)',
        'af_ZA' => 'a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Mátta-Afrihká)',
        'ar' => 'ar<PERSON>bag<PERSON>la',
        'ar_001' => 'ar<PERSON><PERSON><PERSON><PERSON> (máilbmi)',
        'ar_AE' => 'ar<PERSON><PERSON><PERSON><PERSON> (Ovttastuvvan <PERSON>)',
        'ar_BH' => 'arábagiella (Bahrain)',
        'ar_DJ' => 'arábagiella (Djibouti)',
        'ar_DZ' => 'arábagiella (Algeria)',
        'ar_EG' => 'arábagiella (Egypta)',
        'ar_EH' => 'arábagiella (Oarje-Sahára)',
        'ar_ER' => 'arábagiella (Eritrea)',
        'ar_IL' => 'ar<PERSON>bagiella (Israel)',
        'ar_IQ' => 'ar<PERSON><PERSON>iella (Irak)',
        'ar_JO' => 'ar<PERSON><PERSON><PERSON><PERSON> (Jordánia)',
        'ar_KM' => 'ar<PERSON><PERSON>iel<PERSON> (Komoros)',
        'ar_KW' => 'arábagiella (Kuwait)',
        'ar_LB' => 'arábagiella (Libanon)',
        'ar_LY' => 'arábagiella (Libya)',
        'ar_MA' => 'arábagiella (Marokko)',
        'ar_MR' => 'arábagiella (Mauretánia)',
        'ar_OM' => 'arábagiella (Oman)',
        'ar_PS' => 'arábagiella (Palestina)',
        'ar_QA' => 'arábagiella (Qatar)',
        'ar_SA' => 'arábagiella (Saudi-Arábia)',
        'ar_SD' => 'arábagiella (Davvisudan)',
        'ar_SO' => 'arábagiella (Somália)',
        'ar_SS' => 'arábagiella (Máttasudan)',
        'ar_SY' => 'arábagiella (Syria)',
        'ar_TD' => 'arábagiella (Tčad)',
        'ar_TN' => 'arábagiella (Tunisia)',
        'ar_YE' => 'arábagiella (Jemen)',
        'be' => 'vilges-ruoššagiella',
        'be_BY' => 'vilges-ruoššagiella (Vilges-Ruošša)',
        'bg' => 'bulgáriagiella',
        'bg_BG' => 'bulgáriagiella (Bulgária)',
        'bn' => 'bengalgiella',
        'bn_BD' => 'bengalgiella (Bangladesh)',
        'bn_IN' => 'bengalgiella (India)',
        'bo' => 'tibetagiella',
        'bo_CN' => 'tibetagiella (Kiinná)',
        'bo_IN' => 'tibetagiella (India)',
        'br' => 'bretonagiella',
        'br_FR' => 'bretonagiella (Frankriika)',
        'bs' => 'bosniagiella',
        'bs_BA' => 'bosniagiella (Bosnia-Hercegovina)',
        'bs_Cyrl' => 'bosniagiella (kyrillalaš)',
        'bs_Cyrl_BA' => 'bosniagiella (kyrillalaš, Bosnia-Hercegovina)',
        'bs_Latn' => 'bosniagiella (láhtenaš)',
        'bs_Latn_BA' => 'bosniagiella (láhtenaš, Bosnia-Hercegovina)',
        'ca' => 'katalánagiella',
        'ca_AD' => 'katalánagiella (Andorra)',
        'ca_ES' => 'katalánagiella (Spánia)',
        'ca_FR' => 'katalánagiella (Frankriika)',
        'ca_IT' => 'katalánagiella (Itália)',
        'cs' => 'čeahkagiella',
        'cs_CZ' => 'čeahkagiella (Čeahkka)',
        'cy' => 'kymragiella',
        'cy_GB' => 'kymragiella (Stuorra-Británnia)',
        'da' => 'dánskkagiella',
        'da_DK' => 'dánskkagiella (Dánmárku)',
        'da_GL' => 'dánskkagiella (Kalaallit Nunaat)',
        'de' => 'duiskkagiella',
        'de_AT' => 'duiskkagiella (Nuortariika)',
        'de_BE' => 'duiskkagiella (Belgia)',
        'de_CH' => 'duiskkagiella (Šveica)',
        'de_DE' => 'duiskkagiella (Duiska)',
        'de_IT' => 'duiskkagiella (Itália)',
        'de_LI' => 'duiskkagiella (Liechtenstein)',
        'de_LU' => 'duiskkagiella (Luxembourg)',
        'dz' => 'dzongkhagiella',
        'dz_BT' => 'dzongkhagiella (Bhutan)',
        'el' => 'greikkagiella',
        'el_CY' => 'greikkagiella (Kypros)',
        'el_GR' => 'greikkagiella (Greika)',
        'en' => 'eaŋgalsgiella',
        'en_001' => 'eaŋgalsgiella (máilbmi)',
        'en_150' => 'eaŋgalsgiella (Eurohpá)',
        'en_AE' => 'eaŋgalsgiella (Ovttastuvvan Arábaemiráhtat)',
        'en_AG' => 'eaŋgalsgiella (Antigua ja Barbuda)',
        'en_AI' => 'eaŋgalsgiella (Anguilla)',
        'en_AS' => 'eaŋgalsgiella (Amerihká Samoa)',
        'en_AT' => 'eaŋgalsgiella (Nuortariika)',
        'en_AU' => 'eaŋgalsgiella (Austrália)',
        'en_BB' => 'eaŋgalsgiella (Barbados)',
        'en_BE' => 'eaŋgalsgiella (Belgia)',
        'en_BI' => 'eaŋgalsgiella (Burundi)',
        'en_BM' => 'eaŋgalsgiella (Bermuda)',
        'en_BS' => 'eaŋgalsgiella (Bahamas)',
        'en_BW' => 'eaŋgalsgiella (Botswana)',
        'en_BZ' => 'eaŋgalsgiella (Belize)',
        'en_CA' => 'eaŋgalsgiella (Kanáda)',
        'en_CC' => 'eaŋgalsgiella (Cocos-sullot)',
        'en_CH' => 'eaŋgalsgiella (Šveica)',
        'en_CK' => 'eaŋgalsgiella (Cook-sullot)',
        'en_CM' => 'eaŋgalsgiella (Kamerun)',
        'en_CX' => 'eaŋgalsgiella (Juovllat-sullot)',
        'en_CY' => 'eaŋgalsgiella (Kypros)',
        'en_DE' => 'eaŋgalsgiella (Duiska)',
        'en_DK' => 'eaŋgalsgiella (Dánmárku)',
        'en_DM' => 'eaŋgalsgiella (Dominica)',
        'en_ER' => 'eaŋgalsgiella (Eritrea)',
        'en_FI' => 'eaŋgalsgiella (Suopma)',
        'en_FJ' => 'eaŋgalsgiella (Fijisullot)',
        'en_FK' => 'eaŋgalsgiella (Falklandsullot)',
        'en_FM' => 'eaŋgalsgiella (Mikronesia)',
        'en_GB' => 'eaŋgalsgiella (Stuorra-Británnia)',
        'en_GD' => 'eaŋgalsgiella (Grenada)',
        'en_GG' => 'eaŋgalsgiella (Guernsey)',
        'en_GH' => 'eaŋgalsgiella (Ghana)',
        'en_GI' => 'eaŋgalsgiella (Gibraltar)',
        'en_GM' => 'eaŋgalsgiella (Gámbia)',
        'en_GU' => 'eaŋgalsgiella (Guam)',
        'en_GY' => 'eaŋgalsgiella (Guyana)',
        'en_HK' => 'eaŋgalsgiella (Hongkong)',
        'en_ID' => 'eaŋgalsgiella (Indonesia)',
        'en_IE' => 'eaŋgalsgiella (Irlánda)',
        'en_IL' => 'eaŋgalsgiella (Israel)',
        'en_IM' => 'eaŋgalsgiella (Mann-sullot)',
        'en_IN' => 'eaŋgalsgiella (India)',
        'en_JE' => 'eaŋgalsgiella (Jersey)',
        'en_JM' => 'eaŋgalsgiella (Jamaica)',
        'en_KE' => 'eaŋgalsgiella (Kenia)',
        'en_KI' => 'eaŋgalsgiella (Kiribati)',
        'en_KN' => 'eaŋgalsgiella (Saint Kitts ja Nevis)',
        'en_KY' => 'eaŋgalsgiella (Cayman-sullot)',
        'en_LC' => 'eaŋgalsgiella (Saint Lucia)',
        'en_LR' => 'eaŋgalsgiella (Liberia)',
        'en_LS' => 'eaŋgalsgiella (Lesotho)',
        'en_MG' => 'eaŋgalsgiella (Madagaskar)',
        'en_MH' => 'eaŋgalsgiella (Marshallsullot)',
        'en_MO' => 'eaŋgalsgiella (Makáo)',
        'en_MP' => 'eaŋgalsgiella (Davvi-Mariánat)',
        'en_MS' => 'eaŋgalsgiella (Montserrat)',
        'en_MT' => 'eaŋgalsgiella (Málta)',
        'en_MU' => 'eaŋgalsgiella (Mauritius)',
        'en_MV' => 'eaŋgalsgiella (Malediivvat)',
        'en_MW' => 'eaŋgalsgiella (Malawi)',
        'en_MY' => 'eaŋgalsgiella (Malesia)',
        'en_NA' => 'eaŋgalsgiella (Namibia)',
        'en_NF' => 'eaŋgalsgiella (Norfolksullot)',
        'en_NG' => 'eaŋgalsgiella (Nigeria)',
        'en_NL' => 'eaŋgalsgiella (Vuolleeatnamat)',
        'en_NR' => 'eaŋgalsgiella (Nauru)',
        'en_NU' => 'eaŋgalsgiella (Niue)',
        'en_NZ' => 'eaŋgalsgiella (Ođđa-Selánda)',
        'en_PG' => 'eaŋgalsgiella (Papua-Ođđa-Guinea)',
        'en_PH' => 'eaŋgalsgiella (Filippiinnat)',
        'en_PK' => 'eaŋgalsgiella (Pakistan)',
        'en_PN' => 'eaŋgalsgiella (Pitcairn)',
        'en_PR' => 'eaŋgalsgiella (Puerto Rico)',
        'en_PW' => 'eaŋgalsgiella (Palau)',
        'en_RW' => 'eaŋgalsgiella (Rwanda)',
        'en_SB' => 'eaŋgalsgiella (Salomon-sullot)',
        'en_SC' => 'eaŋgalsgiella (Seychellsullot)',
        'en_SD' => 'eaŋgalsgiella (Davvisudan)',
        'en_SE' => 'eaŋgalsgiella (Ruoŧŧa)',
        'en_SG' => 'eaŋgalsgiella (Singapore)',
        'en_SH' => 'eaŋgalsgiella (Saint Helena)',
        'en_SI' => 'eaŋgalsgiella (Slovenia)',
        'en_SL' => 'eaŋgalsgiella (Sierra Leone)',
        'en_SS' => 'eaŋgalsgiella (Máttasudan)',
        'en_SX' => 'eaŋgalsgiella (Vuolleeatnamat Saint Martin)',
        'en_SZ' => 'eaŋgalsgiella (Svazieana)',
        'en_TC' => 'eaŋgalsgiella (Turks ja Caicos-sullot)',
        'en_TK' => 'eaŋgalsgiella (Tokelau)',
        'en_TO' => 'eaŋgalsgiella (Tonga)',
        'en_TT' => 'eaŋgalsgiella (Trinidad ja Tobago)',
        'en_TV' => 'eaŋgalsgiella (Tuvalu)',
        'en_TZ' => 'eaŋgalsgiella (Tanzánia)',
        'en_UG' => 'eaŋgalsgiella (Uganda)',
        'en_US' => 'eaŋgalsgiella (Amerihká ovttastuvvan stáhtat)',
        'en_VC' => 'eaŋgalsgiella (Saint Vincent ja Grenadine)',
        'en_VG' => 'eaŋgalsgiella (Brittania Virgin-sullot)',
        'en_VI' => 'eaŋgalsgiella (AOS Virgin-sullot)',
        'en_VU' => 'eaŋgalsgiella (Vanuatu)',
        'en_WS' => 'eaŋgalsgiella (Samoa)',
        'en_ZA' => 'eaŋgalsgiella (Mátta-Afrihká)',
        'en_ZM' => 'eaŋgalsgiella (Zambia)',
        'en_ZW' => 'eaŋgalsgiella (Zimbabwe)',
        'es' => 'spánskkagiella',
        'es_419' => 'spánskkagiella (lulli-Amerihkká)',
        'es_AR' => 'spánskkagiella (Argentina)',
        'es_BO' => 'spánskkagiella (Bolivia)',
        'es_BR' => 'spánskkagiella (Brasil)',
        'es_BZ' => 'spánskkagiella (Belize)',
        'es_CL' => 'spánskkagiella (Čiile)',
        'es_CO' => 'spánskkagiella (Kolombia)',
        'es_CR' => 'spánskkagiella (Costa Rica)',
        'es_CU' => 'spánskkagiella (Kuba)',
        'es_DO' => 'spánskkagiella (Dominikána dásseváldi)',
        'es_EC' => 'spánskkagiella (Ecuador)',
        'es_ES' => 'spánskkagiella (Spánia)',
        'es_GQ' => 'spánskkagiella (Ekvatoriála Guinea)',
        'es_GT' => 'spánskkagiella (Guatemala)',
        'es_HN' => 'spánskkagiella (Honduras)',
        'es_MX' => 'spánskkagiella (Meksiko)',
        'es_NI' => 'spánskkagiella (Nicaragua)',
        'es_PA' => 'spánskkagiella (Panama)',
        'es_PE' => 'spánskkagiella (Peru)',
        'es_PH' => 'spánskkagiella (Filippiinnat)',
        'es_PR' => 'spánskkagiella (Puerto Rico)',
        'es_PY' => 'spánskkagiella (Paraguay)',
        'es_SV' => 'spánskkagiella (El Salvador)',
        'es_US' => 'spánskkagiella (Amerihká ovttastuvvan stáhtat)',
        'es_UY' => 'spánskkagiella (Uruguay)',
        'es_VE' => 'spánskkagiella (Venezuela)',
        'et' => 'esttegiella',
        'et_EE' => 'esttegiella (Estlánda)',
        'fa' => 'persijagiella',
        'fa_AF' => 'persijagiella (Afghanistan)',
        'fa_IR' => 'persijagiella (Iran)',
        'fi' => 'suomagiella',
        'fi_FI' => 'suomagiella (Suopma)',
        'fo' => 'fearagiella',
        'fo_DK' => 'fearagiella (Dánmárku)',
        'fo_FO' => 'fearagiella (Fearsullot)',
        'fr' => 'fránskkagiella',
        'fr_BE' => 'fránskkagiella (Belgia)',
        'fr_BF' => 'fránskkagiella (Burkina Faso)',
        'fr_BI' => 'fránskkagiella (Burundi)',
        'fr_BJ' => 'fránskkagiella (Benin)',
        'fr_BL' => 'fránskkagiella (Saint Barthélemy)',
        'fr_CA' => 'fránskkagiella (Kanáda)',
        'fr_CD' => 'fránskkagiella (Kongo-Kinshasa)',
        'fr_CF' => 'fránskkagiella (Gaska-Afrihká dásseváldi)',
        'fr_CG' => 'fránskkagiella (Kongo-Brazzaville)',
        'fr_CH' => 'fránskkagiella (Šveica)',
        'fr_CI' => 'fránskkagiella (Elfenbenariddu)',
        'fr_CM' => 'fránskkagiella (Kamerun)',
        'fr_DJ' => 'fránskkagiella (Djibouti)',
        'fr_DZ' => 'fránskkagiella (Algeria)',
        'fr_FR' => 'fránskkagiella (Frankriika)',
        'fr_GA' => 'fránskkagiella (Gabon)',
        'fr_GF' => 'fránskkagiella (Frankriikka Guayana)',
        'fr_GN' => 'fránskkagiella (Guinea)',
        'fr_GP' => 'fránskkagiella (Guadeloupe)',
        'fr_GQ' => 'fránskkagiella (Ekvatoriála Guinea)',
        'fr_HT' => 'fránskkagiella (Haiti)',
        'fr_KM' => 'fránskkagiella (Komoros)',
        'fr_LU' => 'fránskkagiella (Luxembourg)',
        'fr_MA' => 'fránskkagiella (Marokko)',
        'fr_MC' => 'fránskkagiella (Monaco)',
        'fr_MF' => 'fránskkagiella (Frankriikka Saint Martin)',
        'fr_MG' => 'fránskkagiella (Madagaskar)',
        'fr_ML' => 'fránskkagiella (Mali)',
        'fr_MQ' => 'fránskkagiella (Martinique)',
        'fr_MR' => 'fránskkagiella (Mauretánia)',
        'fr_MU' => 'fránskkagiella (Mauritius)',
        'fr_NC' => 'fránskkagiella (Ođđa-Kaledonia)',
        'fr_NE' => 'fránskkagiella (Niger)',
        'fr_PF' => 'fránskkagiella (Frankriikka Polynesia)',
        'fr_PM' => 'fránskkagiella (Saint Pierre ja Miquelon)',
        'fr_RE' => 'fránskkagiella (Réunion)',
        'fr_RW' => 'fránskkagiella (Rwanda)',
        'fr_SC' => 'fránskkagiella (Seychellsullot)',
        'fr_SN' => 'fránskkagiella (Senegal)',
        'fr_SY' => 'fránskkagiella (Syria)',
        'fr_TD' => 'fránskkagiella (Tčad)',
        'fr_TG' => 'fránskkagiella (Togo)',
        'fr_TN' => 'fránskkagiella (Tunisia)',
        'fr_VU' => 'fránskkagiella (Vanuatu)',
        'fr_WF' => 'fránskkagiella (Wallis ja Futuna)',
        'fr_YT' => 'fránskkagiella (Mayotte)',
        'fy' => 'oarjifriisagiella',
        'fy_NL' => 'oarjifriisagiella (Vuolleeatnamat)',
        'ga' => 'iirragiella',
        'ga_GB' => 'iirragiella (Stuorra-Británnia)',
        'ga_IE' => 'iirragiella (Irlánda)',
        'gu' => 'gujaratagiella',
        'gu_IN' => 'gujaratagiella (India)',
        'gv' => 'manksgiella',
        'gv_IM' => 'manksgiella (Mann-sullot)',
        'ha' => 'haussagiella',
        'ha_GH' => 'haussagiella (Ghana)',
        'ha_NE' => 'haussagiella (Niger)',
        'ha_NG' => 'haussagiella (Nigeria)',
        'hi' => 'hindigiella',
        'hi_IN' => 'hindigiella (India)',
        'hi_Latn' => 'hindigiella (láhtenaš)',
        'hi_Latn_IN' => 'hindigiella (láhtenaš, India)',
        'hr' => 'kroátiagiella',
        'hr_BA' => 'kroátiagiella (Bosnia-Hercegovina)',
        'hr_HR' => 'kroátiagiella (Kroátia)',
        'hu' => 'ungárgiella',
        'hu_HU' => 'ungárgiella (Ungár)',
        'hy' => 'armeenagiella',
        'hy_AM' => 'armeenagiella (Armenia)',
        'id' => 'indonesiagiella',
        'id_ID' => 'indonesiagiella (Indonesia)',
        'is' => 'islánddagiella',
        'is_IS' => 'islánddagiella (Islánda)',
        'it' => 'itáliagiella',
        'it_CH' => 'itáliagiella (Šveica)',
        'it_IT' => 'itáliagiella (Itália)',
        'it_SM' => 'itáliagiella (San Marino)',
        'it_VA' => 'itáliagiella (Vatikána)',
        'ja' => 'japánagiella',
        'ja_JP' => 'japánagiella (Japána)',
        'jv' => 'javagiella',
        'jv_ID' => 'javagiella (Indonesia)',
        'ka' => 'georgiagiella',
        'ka_GE' => 'georgiagiella (Georgia)',
        'kk' => 'kazakgiella',
        'kk_Cyrl' => 'kazakgiella (kyrillalaš)',
        'kk_Cyrl_KZ' => 'kazakgiella (kyrillalaš, Kasakstan)',
        'kk_KZ' => 'kazakgiella (Kasakstan)',
        'km' => 'kambodiagiella',
        'km_KH' => 'kambodiagiella (Kambodža)',
        'ko' => 'koreagiella',
        'ko_CN' => 'koreagiella (Kiinná)',
        'ko_KP' => 'koreagiella (Davvi-Korea)',
        'ko_KR' => 'koreagiella (Mátta-Korea)',
        'ku' => 'kurdigiella',
        'ku_TR' => 'kurdigiella (Durka)',
        'kw' => 'kornagiella',
        'kw_GB' => 'kornagiella (Stuorra-Británnia)',
        'lb' => 'luxemburggagiella',
        'lb_LU' => 'luxemburggagiella (Luxembourg)',
        'lo' => 'laogiella',
        'lo_LA' => 'laogiella (Laos)',
        'lt' => 'liettuvagiella',
        'lt_LT' => 'liettuvagiella (Lietuva)',
        'lv' => 'látviagiella',
        'lv_LV' => 'látviagiella (Látvia)',
        'mi' => 'maorigiella',
        'mi_NZ' => 'maorigiella (Ođđa-Selánda)',
        'mk' => 'makedoniagiella',
        'mn' => 'mongoliagiella',
        'mn_MN' => 'mongoliagiella (Mongolia)',
        'mt' => 'maltagiella',
        'mt_MT' => 'maltagiella (Málta)',
        'my' => 'burmagiella',
        'my_MM' => 'burmagiella (Burma)',
        'nb' => 'girjedárogiella',
        'nb_NO' => 'girjedárogiella (Norga)',
        'nb_SJ' => 'girjedárogiella (Svalbárda ja Jan Mayen)',
        'ne' => 'nepaligiella',
        'ne_IN' => 'nepaligiella (India)',
        'ne_NP' => 'nepaligiella (Nepal)',
        'nl' => 'hollánddagiella',
        'nl_AW' => 'hollánddagiella (Aruba)',
        'nl_BE' => 'hollánddagiella (Belgia)',
        'nl_CW' => 'hollánddagiella (Curaçao)',
        'nl_NL' => 'hollánddagiella (Vuolleeatnamat)',
        'nl_SR' => 'hollánddagiella (Surinam)',
        'nl_SX' => 'hollánddagiella (Vuolleeatnamat Saint Martin)',
        'nn' => 'ođđadárogiella',
        'nn_NO' => 'ođđadárogiella (Norga)',
        'no' => 'dárogiella',
        'no_NO' => 'dárogiella (Norga)',
        'oc' => 'oksitánagiella',
        'oc_ES' => 'oksitánagiella (Spánia)',
        'oc_FR' => 'oksitánagiella (Frankriika)',
        'pa' => 'panjabigiella',
        'pa_Arab' => 'panjabigiella (arába)',
        'pa_Arab_PK' => 'panjabigiella (arába, Pakistan)',
        'pa_IN' => 'panjabigiella (India)',
        'pa_PK' => 'panjabigiella (Pakistan)',
        'pl' => 'polskkagiella',
        'pl_PL' => 'polskkagiella (Polen)',
        'pt' => 'portugálagiella',
        'pt_AO' => 'portugálagiella (Angola)',
        'pt_BR' => 'portugálagiella (Brasil)',
        'pt_CH' => 'portugálagiella (Šveica)',
        'pt_CV' => 'portugálagiella (Kap Verde)',
        'pt_GQ' => 'portugálagiella (Ekvatoriála Guinea)',
        'pt_GW' => 'portugálagiella (Guinea-Bissau)',
        'pt_LU' => 'portugálagiella (Luxembourg)',
        'pt_MO' => 'portugálagiella (Makáo)',
        'pt_MZ' => 'portugálagiella (Mosambik)',
        'pt_PT' => 'portugálagiella (Portugála)',
        'pt_ST' => 'portugálagiella (São Tomé ja Príncipe)',
        'pt_TL' => 'portugálagiella (Nuorta-Timor)',
        'rm' => 'romanšgiella',
        'rm_CH' => 'romanšgiella (Šveica)',
        'ro' => 'romániagiella',
        'ro_MD' => 'romániagiella (Moldávia)',
        'ro_RO' => 'romániagiella (Románia)',
        'ru' => 'ruoššagiella',
        'ru_BY' => 'ruoššagiella (Vilges-Ruošša)',
        'ru_KG' => 'ruoššagiella (Kirgisistan)',
        'ru_KZ' => 'ruoššagiella (Kasakstan)',
        'ru_MD' => 'ruoššagiella (Moldávia)',
        'ru_RU' => 'ruoššagiella (Ruošša)',
        'ru_UA' => 'ruoššagiella (Ukraina)',
        'sc' => 'sardigiella',
        'sc_IT' => 'sardigiella (Itália)',
        'se' => 'davvisámegiella',
        'se_FI' => 'davvisámegiella (Suopma)',
        'se_NO' => 'davvisámegiella (Norga)',
        'se_SE' => 'davvisámegiella (Ruoŧŧa)',
        'sh' => 'serbokroatiagiella',
        'sh_BA' => 'serbokroatiagiella (Bosnia-Hercegovina)',
        'sk' => 'slovákiagiella',
        'sk_SK' => 'slovákiagiella (Slovákia)',
        'sl' => 'slovenagiella',
        'sl_SI' => 'slovenagiella (Slovenia)',
        'sq' => 'albánagiella',
        'sq_AL' => 'albánagiella (Albánia)',
        'sr' => 'serbiagiella',
        'sr_BA' => 'serbiagiella (Bosnia-Hercegovina)',
        'sr_Cyrl' => 'serbiagiella (kyrillalaš)',
        'sr_Cyrl_BA' => 'serbiagiella (kyrillalaš, Bosnia-Hercegovina)',
        'sr_Cyrl_ME' => 'serbiagiella (kyrillalaš, Montenegro)',
        'sr_Cyrl_RS' => 'serbiagiella (kyrillalaš, Serbia)',
        'sr_Latn' => 'serbiagiella (láhtenaš)',
        'sr_Latn_BA' => 'serbiagiella (láhtenaš, Bosnia-Hercegovina)',
        'sr_Latn_ME' => 'serbiagiella (láhtenaš, Montenegro)',
        'sr_Latn_RS' => 'serbiagiella (láhtenaš, Serbia)',
        'sr_ME' => 'serbiagiella (Montenegro)',
        'sr_RS' => 'serbiagiella (Serbia)',
        'sv' => 'ruoŧagiella',
        'sv_AX' => 'ruoŧagiella (Ålánda)',
        'sv_FI' => 'ruoŧagiella (Suopma)',
        'sv_SE' => 'ruoŧagiella (Ruoŧŧa)',
        'th' => 'ŧaigiella',
        'th_TH' => 'ŧaigiella (Thaieana)',
        'tr' => 'durkagiella',
        'tr_CY' => 'durkagiella (Kypros)',
        'tr_TR' => 'durkagiella (Durka)',
        'uk' => 'ukrainagiella',
        'uk_UA' => 'ukrainagiella (Ukraina)',
        'ur' => 'urdugiella',
        'ur_IN' => 'urdugiella (India)',
        'ur_PK' => 'urdugiella (Pakistan)',
        'vi' => 'vietnamgiella',
        'vi_VN' => 'vietnamgiella (Vietnam)',
        'zh' => 'kiinnágiella',
        'zh_CN' => 'kiinnágiella (Kiinná)',
        'zh_HK' => 'kiinnágiella (Hongkong)',
        'zh_Hans' => 'kiinnágiella (álki)',
        'zh_Hans_CN' => 'kiinnágiella (álki, Kiinná)',
        'zh_Hans_HK' => 'kiinnágiella (álki, Hongkong)',
        'zh_Hans_MO' => 'kiinnágiella (álki, Makáo)',
        'zh_Hans_MY' => 'kiinnágiella (álki, Malesia)',
        'zh_Hans_SG' => 'kiinnágiella (álki, Singapore)',
        'zh_Hant' => 'kiinnágiella (árbevirolaš)',
        'zh_Hant_HK' => 'kiinnágiella (árbevirolaš, Hongkong)',
        'zh_Hant_MO' => 'kiinnágiella (árbevirolaš, Makáo)',
        'zh_Hant_MY' => 'kiinnágiella (árbevirolaš, Malesia)',
        'zh_Hant_TW' => 'kiinnágiella (árbevirolaš, Taiwan)',
        'zh_MO' => 'kiinnágiella (Makáo)',
        'zh_SG' => 'kiinnágiella (Singapore)',
        'zh_TW' => 'kiinnágiella (Taiwan)',
    ],
];
