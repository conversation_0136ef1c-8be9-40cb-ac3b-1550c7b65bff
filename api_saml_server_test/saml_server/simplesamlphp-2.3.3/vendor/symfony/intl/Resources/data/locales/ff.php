<?php

return [
    'Names' => [
        'ak' => '<PERSON><PERSON><PERSON>',
        'ak_GH' => '<PERSON><PERSON><PERSON> (Ganaa)',
        'am' => '<PERSON><PERSON>',
        'am_ET' => '<PERSON><PERSON> (Ecoppi)',
        'ar' => 'A<PERSON>beere',
        'ar_AE' => '<PERSON><PERSON><PERSON><PERSON> (Emiraat Araab Denntuɗe)',
        'ar_BH' => '<PERSON><PERSON><PERSON><PERSON> (Bahreyn)',
        'ar_DJ' => '<PERSON><PERSON><PERSON><PERSON> (Jibutii)',
        'ar_DZ' => '<PERSON><PERSON><PERSON><PERSON> (Alaseri)',
        'ar_EG' => '<PERSON><PERSON>beere (Ejipt)',
        'ar_ER' => '<PERSON><PERSON><PERSON><PERSON> (Eriteree)',
        'ar_IL' => '<PERSON><PERSON><PERSON>re (Israa’iila)',
        'ar_IQ' => '<PERSON><PERSON><PERSON><PERSON> (Iraak)',
        'ar_JO' => '<PERSON><PERSON><PERSON><PERSON> (Jordani)',
        'ar_KM' => 'Aarabeere (Komoor)',
        'ar_KW' => '<PERSON><PERSON><PERSON><PERSON> (Kuweyti)',
        'ar_LB' => '<PERSON><PERSON><PERSON><PERSON> (Libaa)',
        'ar_LY' => '<PERSON><PERSON><PERSON><PERSON> (Libi)',
        'ar_<PERSON>' => '<PERSON><PERSON><PERSON><PERSON> (Maruk)',
        'ar_<PERSON>' => 'Aarabeere (Muritani)',
        'ar_OM' => 'Aarabeere (Omaan)',
        'ar_PS' => 'Aarabeere (Palestiin Sisjordani e Gaasaa)',
        'ar_QA' => 'Aarabeere (Kataar)',
        'ar_SA' => 'Aarabeere (Arabii Sawdit)',
        'ar_SD' => 'Aarabeere (Sudaan)',
        'ar_SO' => 'Aarabeere (Somalii)',
        'ar_SY' => 'Aarabeere (Sirii)',
        'ar_TD' => 'Aarabeere (Caad)',
        'ar_TN' => 'Aarabeere (Tunisii)',
        'ar_YE' => 'Aarabeere (Yemen)',
        'be' => 'Belaruuse',
        'be_BY' => 'Belaruuse (Belaruus)',
        'bg' => 'Bulgariire',
        'bg_BG' => 'Bulgariire (Bulgarii)',
        'bn' => 'Bengali',
        'bn_BD' => 'Bengali (Banglaadees)',
        'bn_IN' => 'Bengali (Enndo)',
        'cs' => 'Cekkere',
        'cs_CZ' => 'Cekkere (Ndenndaandi Cek)',
        'de' => 'Docceere',
        'de_AT' => 'Docceere (Otiriis)',
        'de_BE' => 'Docceere (Beljik)',
        'de_CH' => 'Docceere (Suwiis)',
        'de_DE' => 'Docceere (Almaañ)',
        'de_IT' => 'Docceere (Itali)',
        'de_LI' => 'Docceere (Lincenstayn)',
        'de_LU' => 'Docceere (Liksembuur)',
        'el' => 'Gerke',
        'el_CY' => 'Gerke (Siipar)',
        'el_GR' => 'Gerke (Gerees)',
        'en' => 'Engeleere',
        'en_AE' => 'Engeleere (Emiraat Araab Denntuɗe)',
        'en_AG' => 'Engeleere (Antiguwaa e Barbudaa)',
        'en_AI' => 'Engeleere (Anngiyaa)',
        'en_AS' => 'Engeleere (Samowa Amerik)',
        'en_AT' => 'Engeleere (Otiriis)',
        'en_AU' => 'Engeleere (Ostaraalii)',
        'en_BB' => 'Engeleere (Barbadoos)',
        'en_BE' => 'Engeleere (Beljik)',
        'en_BI' => 'Engeleere (Burunndi)',
        'en_BM' => 'Engeleere (Bermudaa)',
        'en_BS' => 'Engeleere (Bahamaas)',
        'en_BW' => 'Engeleere (Botswaana)',
        'en_BZ' => 'Engeleere (Beliise)',
        'en_CA' => 'Engeleere (Kanadaa)',
        'en_CH' => 'Engeleere (Suwiis)',
        'en_CK' => 'Engeleere (Duuɗe Kuuk)',
        'en_CM' => 'Engeleere (Kameruun)',
        'en_CY' => 'Engeleere (Siipar)',
        'en_DE' => 'Engeleere (Almaañ)',
        'en_DK' => 'Engeleere (Danmark)',
        'en_DM' => 'Engeleere (Dominika)',
        'en_ER' => 'Engeleere (Eriteree)',
        'en_FI' => 'Engeleere (Fenland)',
        'en_FJ' => 'Engeleere (Fijji)',
        'en_FK' => 'Engeleere (Duuɗe Falkland)',
        'en_FM' => 'Engeleere (Mikoronesii)',
        'en_GB' => 'Engeleere (Laamateeri Rentundi)',
        'en_GD' => 'Engeleere (Garnaad)',
        'en_GH' => 'Engeleere (Ganaa)',
        'en_GI' => 'Engeleere (Jibraltaar)',
        'en_GM' => 'Engeleere (Gammbi)',
        'en_GU' => 'Engeleere (Guwam)',
        'en_GY' => 'Engeleere (Giyaan)',
        'en_ID' => 'Engeleere (Enndonesii)',
        'en_IE' => 'Engeleere (Irlannda)',
        'en_IL' => 'Engeleere (Israa’iila)',
        'en_IN' => 'Engeleere (Enndo)',
        'en_JM' => 'Engeleere (Jamayka)',
        'en_KE' => 'Engeleere (Keñaa)',
        'en_KI' => 'Engeleere (Kiribari)',
        'en_KN' => 'Engeleere (Sent Kits e Newis)',
        'en_KY' => 'Engeleere (Duuɗe Kaymaa)',
        'en_LC' => 'Engeleere (Sent Lusiyaa)',
        'en_LR' => 'Engeleere (Liberiyaa)',
        'en_LS' => 'Engeleere (Lesoto)',
        'en_MG' => 'Engeleere (Madagaskaar)',
        'en_MH' => 'Engeleere (Duuɗe Marsaal)',
        'en_MP' => 'Engeleere (Duuɗe Mariyaana Rewo)',
        'en_MS' => 'Engeleere (Monseraat)',
        'en_MT' => 'Engeleere (Malte)',
        'en_MU' => 'Engeleere (Moriis)',
        'en_MV' => 'Engeleere (Maldiiwe)',
        'en_MW' => 'Engeleere (Malaawi)',
        'en_MY' => 'Engeleere (Malesii)',
        'en_NA' => 'Engeleere (Namibii)',
        'en_NF' => 'Engeleere (Duuɗe Norfolk)',
        'en_NG' => 'Engeleere (Nijeriyaa)',
        'en_NL' => 'Engeleere (Nederlannda)',
        'en_NR' => 'Engeleere (Nawuru)',
        'en_NU' => 'Engeleere (Niuwe)',
        'en_NZ' => 'Engeleere (Nuwel Selannda)',
        'en_PG' => 'Engeleere (Papuwaa Nuwel Gine)',
        'en_PH' => 'Engeleere (Filipiin)',
        'en_PK' => 'Engeleere (Pakistaan)',
        'en_PN' => 'Engeleere (Pitkern)',
        'en_PR' => 'Engeleere (Porto Rikoo)',
        'en_PW' => 'Engeleere (Palawu)',
        'en_RW' => 'Engeleere (Ruwanndaa)',
        'en_SB' => 'Engeleere (Duuɗe Solomon)',
        'en_SC' => 'Engeleere (Seysel)',
        'en_SD' => 'Engeleere (Sudaan)',
        'en_SE' => 'Engeleere (Suweed)',
        'en_SG' => 'Engeleere (Sinngapuur)',
        'en_SH' => 'Engeleere (Sent Helen)',
        'en_SI' => 'Engeleere (Slowenii)',
        'en_SL' => 'Engeleere (Seraa liyon)',
        'en_SZ' => 'Engeleere (Swaasilannda)',
        'en_TC' => 'Engeleere (Duuɗe Turke e Keikoos)',
        'en_TK' => 'Engeleere (Tokelaaw)',
        'en_TO' => 'Engeleere (Tonngaa)',
        'en_TT' => 'Engeleere (Tirnidaad e Tobaago)',
        'en_TV' => 'Engeleere (Tuwaluu)',
        'en_TZ' => 'Engeleere (Tansanii)',
        'en_UG' => 'Engeleere (Unganndaa)',
        'en_US' => 'Engeleere (Dowlaaji Dentuɗi Amerik)',
        'en_VC' => 'Engeleere (See Weesaa e Garnadiin)',
        'en_VG' => 'Engeleere (duuɗe kecce britanii)',
        'en_VI' => 'Engeleere (Duuɗe Kecce Amerik)',
        'en_VU' => 'Engeleere (Wanuwaatuu)',
        'en_WS' => 'Engeleere (Samowaa)',
        'en_ZA' => 'Engeleere (Afrik bŋ Worgo)',
        'en_ZM' => 'Engeleere (Sammbi)',
        'en_ZW' => 'Engeleere (Simbaabuwe)',
        'es' => 'Español',
        'es_AR' => 'Español (Arjantiin)',
        'es_BO' => 'Español (Boliwii)',
        'es_BR' => 'Español (Beresiil)',
        'es_BZ' => 'Español (Beliise)',
        'es_CL' => 'Español (Cilii)',
        'es_CO' => 'Español (Kolombiya)',
        'es_CR' => 'Español (Kosta Rikaa)',
        'es_CU' => 'Español (Kubaa)',
        'es_DO' => 'Español (Ndenndanndi Dominika)',
        'es_EC' => 'Español (Ekuwatoor)',
        'es_ES' => 'Español (Espaañ)',
        'es_GQ' => 'Español (Ginee Ekuwaatoriyaal)',
        'es_GT' => 'Español (Gwaatemalaa)',
        'es_HN' => 'Español (Onnduraas)',
        'es_MX' => 'Español (Meksik)',
        'es_NI' => 'Español (Nikaraguwaa)',
        'es_PA' => 'Español (Panamaa)',
        'es_PE' => 'Español (Peru)',
        'es_PH' => 'Español (Filipiin)',
        'es_PR' => 'Español (Porto Rikoo)',
        'es_PY' => 'Español (Paraguwaay)',
        'es_SV' => 'Español (El Salwador)',
        'es_US' => 'Español (Dowlaaji Dentuɗi Amerik)',
        'es_UY' => 'Español (Uruguwaay)',
        'es_VE' => 'Español (Wenesuwelaa)',
        'fa' => 'Perseere',
        'fa_AF' => 'Perseere (Afganistaan)',
        'fa_IR' => 'Perseere (Iraan)',
        'ff' => 'Pulaar',
        'ff_CM' => 'Pulaar (Kameruun)',
        'ff_GN' => 'Pulaar (Gine)',
        'ff_MR' => 'Pulaar (Muritani)',
        'ff_SN' => 'Pulaar (Senegaal)',
        'fr' => 'Farayseere',
        'fr_BE' => 'Farayseere (Beljik)',
        'fr_BF' => 'Farayseere (Burkibaa Faaso)',
        'fr_BI' => 'Farayseere (Burunndi)',
        'fr_BJ' => 'Farayseere (Benee)',
        'fr_CA' => 'Farayseere (Kanadaa)',
        'fr_CD' => 'Farayseere (Ndenndaandi Demokaraasiire Konngo)',
        'fr_CF' => 'Farayseere (Ndenndaandi Santarafrik)',
        'fr_CG' => 'Farayseere (Konngo)',
        'fr_CH' => 'Farayseere (Suwiis)',
        'fr_CI' => 'Farayseere (Kodduwaar)',
        'fr_CM' => 'Farayseere (Kameruun)',
        'fr_DJ' => 'Farayseere (Jibutii)',
        'fr_DZ' => 'Farayseere (Alaseri)',
        'fr_FR' => 'Farayseere (Farayse)',
        'fr_GA' => 'Farayseere (Gaboo)',
        'fr_GF' => 'Farayseere (Giyaan Farayse)',
        'fr_GN' => 'Farayseere (Gine)',
        'fr_GP' => 'Farayseere (Gwaadalup)',
        'fr_GQ' => 'Farayseere (Ginee Ekuwaatoriyaal)',
        'fr_HT' => 'Farayseere (Haytii)',
        'fr_KM' => 'Farayseere (Komoor)',
        'fr_LU' => 'Farayseere (Liksembuur)',
        'fr_MA' => 'Farayseere (Maruk)',
        'fr_MC' => 'Farayseere (Monaakoo)',
        'fr_MG' => 'Farayseere (Madagaskaar)',
        'fr_ML' => 'Farayseere (Maali)',
        'fr_MQ' => 'Farayseere (Martinik)',
        'fr_MR' => 'Farayseere (Muritani)',
        'fr_MU' => 'Farayseere (Moriis)',
        'fr_NC' => 'Farayseere (Nuwel Kaledonii)',
        'fr_NE' => 'Farayseere (Nijeer)',
        'fr_PF' => 'Farayseere (Polinesii Farayse)',
        'fr_PM' => 'Farayseere (See Piyeer e Mikeloo)',
        'fr_RE' => 'Farayseere (Rewiñoo)',
        'fr_RW' => 'Farayseere (Ruwanndaa)',
        'fr_SC' => 'Farayseere (Seysel)',
        'fr_SN' => 'Farayseere (Senegaal)',
        'fr_SY' => 'Farayseere (Sirii)',
        'fr_TD' => 'Farayseere (Caad)',
        'fr_TG' => 'Farayseere (Togoo)',
        'fr_TN' => 'Farayseere (Tunisii)',
        'fr_VU' => 'Farayseere (Wanuwaatuu)',
        'fr_WF' => 'Farayseere (Walis e Futuna)',
        'fr_YT' => 'Farayseere (Mayoot)',
        'ha' => 'Hawsaŋkoore',
        'ha_GH' => 'Hawsaŋkoore (Ganaa)',
        'ha_NE' => 'Hawsaŋkoore (Nijeer)',
        'ha_NG' => 'Hawsaŋkoore (Nijeriyaa)',
        'hi' => 'Hinndi',
        'hi_IN' => 'Hinndi (Enndo)',
        'hu' => 'Hongariire',
        'hu_HU' => 'Hongariire (Onngiri)',
        'id' => 'Endonesiire',
        'id_ID' => 'Endonesiire (Enndonesii)',
        'ig' => 'Igiboore',
        'ig_NG' => 'Igiboore (Nijeriyaa)',
        'it' => 'Italiyeere',
        'it_CH' => 'Italiyeere (Suwiis)',
        'it_IT' => 'Italiyeere (Itali)',
        'it_SM' => 'Italiyeere (See Maree)',
        'it_VA' => 'Italiyeere (Dowla Waticaan)',
        'ja' => 'Saponeere',
        'ja_JP' => 'Saponeere (Sapoo)',
        'jv' => 'Sawaneere',
        'jv_ID' => 'Sawaneere (Enndonesii)',
        'km' => 'Kemeere',
        'km_KH' => 'Kemeere (Kambodso)',
        'ko' => 'Koreere',
        'ko_CN' => 'Koreere (Siin)',
        'ko_KP' => 'Koreere (Koree Rewo)',
        'ko_KR' => 'Koreere (Koree Worgo)',
        'ms' => 'Malayeere',
        'ms_BN' => 'Malayeere (Burnaay)',
        'ms_ID' => 'Malayeere (Enndonesii)',
        'ms_MY' => 'Malayeere (Malesii)',
        'ms_SG' => 'Malayeere (Sinngapuur)',
        'my' => 'Burmeese',
        'my_MM' => 'Burmeese (Miyamaar)',
        'ne' => 'Nepaaleere',
        'ne_IN' => 'Nepaaleere (Enndo)',
        'ne_NP' => 'Nepaaleere (Nepaal)',
        'nl' => 'Dacceere',
        'nl_AW' => 'Dacceere (Aruuba)',
        'nl_BE' => 'Dacceere (Beljik)',
        'nl_NL' => 'Dacceere (Nederlannda)',
        'nl_SR' => 'Dacceere (Surinaam)',
        'pa' => 'Punjabeere',
        'pa_IN' => 'Punjabeere (Enndo)',
        'pa_PK' => 'Punjabeere (Pakistaan)',
        'pl' => 'Poloneere',
        'pl_PL' => 'Poloneere (Poloñ)',
        'pt' => 'Purtugeere',
        'pt_AO' => 'Purtugeere (Anngolaa)',
        'pt_BR' => 'Purtugeere (Beresiil)',
        'pt_CH' => 'Purtugeere (Suwiis)',
        'pt_CV' => 'Purtugeere (Duuɗe Kap Weer)',
        'pt_GQ' => 'Purtugeere (Ginee Ekuwaatoriyaal)',
        'pt_GW' => 'Purtugeere (Gine-Bisaawo)',
        'pt_LU' => 'Purtugeere (Liksembuur)',
        'pt_MZ' => 'Purtugeere (Mosammbik)',
        'pt_PT' => 'Purtugeere (Purtugaal)',
        'pt_ST' => 'Purtugeere (Sawo Tome e Perensipe)',
        'pt_TL' => 'Purtugeere (Timoor Fuɗnaange)',
        'ro' => 'Romaneere',
        'ro_MD' => 'Romaneere (Moldawii)',
        'ro_RO' => 'Romaneere (Rumanii)',
        'ru' => 'Riis',
        'ru_BY' => 'Riis (Belaruus)',
        'ru_KG' => 'Riis (Kirgistaan)',
        'ru_KZ' => 'Riis (Kasakstaan)',
        'ru_MD' => 'Riis (Moldawii)',
        'ru_RU' => 'Riis (Riisii)',
        'ru_UA' => 'Riis (Ukereen)',
        'rw' => 'Ruwaanndeere',
        'rw_RW' => 'Ruwaanndeere (Ruwanndaa)',
        'so' => 'Somalii',
        'so_DJ' => 'Somalii (Jibutii)',
        'so_ET' => 'Somalii (Ecoppi)',
        'so_KE' => 'Somalii (Keñaa)',
        'so_SO' => 'Somalii (Somalii)',
        'sv' => 'Sweedeere',
        'sv_FI' => 'Sweedeere (Fenland)',
        'sv_SE' => 'Sweedeere (Suweed)',
        'ta' => 'Tamil',
        'ta_IN' => 'Tamil (Enndo)',
        'ta_LK' => 'Tamil (Siri Lanka)',
        'ta_MY' => 'Tamil (Malesii)',
        'ta_SG' => 'Tamil (Sinngapuur)',
        'th' => 'Taay',
        'th_TH' => 'Taay (Taylannda)',
        'tr' => 'Turkeere',
        'tr_CY' => 'Turkeere (Siipar)',
        'tr_TR' => 'Turkeere (Turkii)',
        'uk' => 'Ukereneere',
        'uk_UA' => 'Ukereneere (Ukereen)',
        'ur' => 'Urdu',
        'ur_IN' => 'Urdu (Enndo)',
        'ur_PK' => 'Urdu (Pakistaan)',
        'vi' => 'Wiyetnameere',
        'vi_VN' => 'Wiyetnameere (Wiyetnaam)',
        'yo' => 'Yorrubaa',
        'yo_BJ' => 'Yorrubaa (Benee)',
        'yo_NG' => 'Yorrubaa (Nijeriyaa)',
        'zh' => 'Sinuwaare',
        'zh_CN' => 'Sinuwaare (Siin)',
        'zh_SG' => 'Sinuwaare (Sinngapuur)',
        'zh_TW' => 'Sinuwaare (Taywaan)',
        'zu' => 'Suluŋkoore',
        'zu_ZA' => 'Suluŋkoore (Afrik bŋ Worgo)',
    ],
];
