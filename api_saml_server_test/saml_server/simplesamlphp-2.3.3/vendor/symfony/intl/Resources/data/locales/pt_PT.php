<?php

return [
    'Names' => [
        'af' => 'africanês',
        'af_NA' => 'africanês (Namíbia)',
        'af_ZA' => 'africanês (África do Sul)',
        'ar_BH' => 'árabe (Barém)',
        'ar_DJ' => 'árabe (Jibuti)',
        'ar_EH' => 'árabe (Sara Ocidental)',
        'ar_KW' => 'árabe (Koweit)',
        'ar_PS' => 'árabe (Territórios palestinianos)',
        'ar_YE' => 'árabe (Iémen)',
        'bn' => 'bengalês',
        'bn_BD' => 'bengalês (Bangladeche)',
        'bn_IN' => 'bengalês (Índia)',
        'cs' => 'checo',
        'cs_CZ' => 'checo (Chéquia)',
        'cv' => 'chuvash',
        'cv_RU' => 'chuvash (Rússia)',
        'da_G<PERSON>' => 'din<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Gronelândia)',
        'de_LI' => 'ale<PERSON><PERSON> (Listenstaine)',
        'en_BS' => 'ingl<PERSON><PERSON> (Baamas)',
        'en_CC' => 'inglês (Ilhas dos Cocos [Keeling])',
        'en_CX' => 'inglês (Ilha do Natal)',
        'en_DM' => 'inglês (Domínica)',
        'en_FK' => 'inglês (Ilhas Falkland)',
        'en_GG' => 'inglês (Guernesey)',
        'en_GU' => 'inglês (Guame)',
        'en_KE' => 'inglês (Quénia)',
        'en_KI' => 'inglês (Quiribáti)',
        'en_KN' => 'inglês (São Cristóvão e Neves)',
        'en_KY' => 'inglês (Ilhas Caimão)',
        'en_MG' => 'inglês (Madagáscar)',
        'en_MS' => 'inglês (Monserrate)',
        'en_MU' => 'inglês (Maurícia)',
        'en_MW' => 'inglês (Maláui)',
        'en_NU' => 'inglês (Niuê)',
        'en_SI' => 'inglês (Eslovénia)',
        'en_SX' => 'inglês (São Martinho [Sint Maarten])',
        'en_TK' => 'inglês (Toquelau)',
        'en_TT' => 'inglês (Trindade e Tobago)',
        'en_UM' => 'inglês (Ilhas Menores Afastadas dos EUA)',
        'en_VI' => 'inglês (Ilhas Virgens dos EUA)',
        'en_ZW' => 'inglês (Zimbabué)',
        'es_SV' => 'espanhol (Salvador)',
        'et' => 'estónio',
        'et_EE' => 'estónio (Estónia)',
        'fa_IR' => 'persa (Irão)',
        'fr_BJ' => 'francês (Benim)',
        'fr_CD' => 'francês (Congo-Kinshasa)',
        'fr_CG' => 'francês (Congo-Brazzaville)',
        'fr_CI' => 'francês (Côte d’Ivoire [Costa do Marfim])',
        'fr_DJ' => 'francês (Jibuti)',
        'fr_MC' => 'francês (Mónaco)',
        'fr_MF' => 'francês (São Martinho [Saint-Martin])',
        'fr_MG' => 'francês (Madagáscar)',
        'fr_MU' => 'francês (Maurícia)',
        'fr_NC' => 'francês (Nova Caledónia)',
        'fr_YT' => 'francês (Maiote)',
        'fy' => 'frísico ocidental',
        'fy_NL' => 'frísico ocidental (Países Baixos)',
        'ha' => 'haúça',
        'ha_GH' => 'haúça (Gana)',
        'ha_NE' => 'haúça (Níger)',
        'ha_NG' => 'haúça (Nigéria)',
        'hi' => 'hindi',
        'hi_IN' => 'hindi (Índia)',
        'hi_Latn' => 'hindi (latim)',
        'hi_Latn_IN' => 'hindi (latim, Índia)',
        'hy' => 'arménio',
        'hy_AM' => 'arménio (Arménia)',
        'ie_EE' => 'interlingue (Estónia)',
        'it_SM' => 'italiano (São Marinho)',
        'ki_KE' => 'quicuio (Quénia)',
        'kl' => 'gronelandês',
        'kl_GL' => 'gronelandês (Gronelândia)',
        'lg' => 'ganda',
        'lg_UG' => 'ganda (Uganda)',
        'ln_CD' => 'lingala (Congo-Kinshasa)',
        'ln_CG' => 'lingala (Congo-Brazzaville)',
        'lu_CD' => 'luba-catanga (Congo-Kinshasa)',
        'lv_LV' => 'letão (Letónia)',
        'mg_MG' => 'malgaxe (Madagáscar)',
        'mk' => 'macedónio',
        'mk_MK' => 'macedónio (Macedónia do Norte)',
        'mr' => 'marata',
        'mr_IN' => 'marata (Índia)',
        'nb' => 'norueguês bokmål',
        'nb_NO' => 'norueguês bokmål (Noruega)',
        'nb_SJ' => 'norueguês bokmål (Svalbard e Jan Mayen)',
        'nd_ZW' => 'ndebele do norte (Zimbabué)',
        'nl' => 'neerlandês',
        'nl_AW' => 'neerlandês (Aruba)',
        'nl_BE' => 'neerlandês (Bélgica)',
        'nl_BQ' => 'neerlandês (Países Baixos Caribenhos)',
        'nl_CW' => 'neerlandês (Curaçau)',
        'nl_NL' => 'neerlandês (Países Baixos)',
        'nl_SR' => 'neerlandês (Suriname)',
        'nl_SX' => 'neerlandês (São Martinho [Sint Maarten])',
        'nn' => 'norueguês nynorsk',
        'nn_NO' => 'norueguês nynorsk (Noruega)',
        'oc' => 'occitano',
        'oc_ES' => 'occitano (Espanha)',
        'oc_FR' => 'occitano (França)',
        'om_KE' => 'oromo (Quénia)',
        'os' => 'ossético',
        'os_GE' => 'ossético (Geórgia)',
        'os_RU' => 'ossético (Rússia)',
        'pl' => 'polaco',
        'pl_PL' => 'polaco (Polónia)',
        'ps' => 'pastó',
        'ps_AF' => 'pastó (Afeganistão)',
        'ps_PK' => 'pastó (Paquistão)',
        'ro_RO' => 'romeno (Roménia)',
        'se' => 'sami do norte',
        'se_FI' => 'sami do norte (Finlândia)',
        'se_NO' => 'sami do norte (Noruega)',
        'se_SE' => 'sami do norte (Suécia)',
        'si_LK' => 'cingalês (Sri Lanca)',
        'sl_SI' => 'esloveno (Eslovénia)',
        'sn' => 'shona',
        'sn_ZW' => 'shona (Zimbabué)',
        'so_DJ' => 'somali (Jibuti)',
        'so_KE' => 'somali (Quénia)',
        'sq_MK' => 'albanês (Macedónia do Norte)',
        'st' => 'sesoto',
        'st_LS' => 'sesoto (Lesoto)',
        'st_ZA' => 'sesoto (África do Sul)',
        'sv_AX' => 'sueco (Alanda)',
        'sw_CD' => 'suaíli (Congo-Kinshasa)',
        'sw_KE' => 'suaíli (Quénia)',
        'ta_LK' => 'tâmil (Sri Lanca)',
        'te' => 'telugu',
        'te_IN' => 'telugu (Índia)',
        'tg' => 'tajique',
        'tg_TJ' => 'tajique (Tajiquistão)',
        'tk' => 'turcomano',
        'tk_TM' => 'turcomano (Turquemenistão)',
        'to' => 'tonga',
        'to_TO' => 'tonga (Tonga)',
        'tt' => 'tatar',
        'tt_RU' => 'tatar (Rússia)',
        'uz' => 'usbeque',
        'uz_AF' => 'usbeque (Afeganistão)',
        'uz_Arab' => 'usbeque (árabe)',
        'uz_Arab_AF' => 'usbeque (árabe, Afeganistão)',
        'uz_Cyrl' => 'usbeque (cirílico)',
        'uz_Cyrl_UZ' => 'usbeque (cirílico, Usbequistão)',
        'uz_Latn' => 'usbeque (latim)',
        'uz_Latn_UZ' => 'usbeque (latim, Usbequistão)',
        'uz_UZ' => 'usbeque (Usbequistão)',
        'vi_VN' => 'vietnamita (Vietname)',
        'wo' => 'uólofe',
        'wo_SN' => 'uólofe (Senegal)',
        'xh' => 'xosa',
        'xh_ZA' => 'xosa (África do Sul)',
        'yo' => 'ioruba',
        'yo_BJ' => 'ioruba (Benim)',
        'yo_NG' => 'ioruba (Nigéria)',
    ],
];
