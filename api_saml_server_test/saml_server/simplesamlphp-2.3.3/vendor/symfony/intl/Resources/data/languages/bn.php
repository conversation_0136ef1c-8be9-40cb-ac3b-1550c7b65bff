<?php

return [
    'Names' => [
        'aa' => 'আফার',
        'ab' => 'আবখাজিয়ান',
        'ace' => 'অ্যাচাইনিজ',
        'ach' => 'আকোলি',
        'ada' => 'অদাগ্মে',
        'ady' => 'আদেগে',
        'ae' => 'আবেস্তীয়',
        'af' => 'আফ্রিকান',
        'afh' => 'আফ্রিহিলি',
        'agq' => 'এঘেম',
        'ain' => 'আইনু',
        'ak' => 'আকান',
        'akk' => 'আক্কাদিয়ান',
        'ale' => 'আলেউত',
        'alt' => 'দক্ষিন আলতাই',
        'am' => 'আমহারিক',
        'an' => 'আর্গোনিজ',
        'ang' => 'প্রাচীন ইংরেজী',
        'ann' => 'ওবোলো',
        'anp' => 'আঙ্গিকা',
        'ar' => 'আরবি',
        'arc' => 'আরামাইক',
        'arn' => 'মাপুচে',
        'arp' => 'আরাপাহো',
        'ars' => 'নজদি আরবি',
        'arw' => 'আরাওয়াক',
        'as' => 'অসমীয়া',
        'asa' => 'আসু',
        'ast' => 'আস্তুরিয়',
        'atj' => 'আটিকামেকিউ',
        'av' => 'আভেরিক',
        'awa' => 'আওয়াধি',
        'ay' => 'আয়মারা',
        'az' => 'আজারবাইজানী',
        'ba' => 'বাশকির',
        'bal' => 'বেলুচী',
        'ban' => 'বালিনীয়',
        'bas' => 'বাসা',
        'be' => 'বেলারুশিয়',
        'bej' => 'বেজা',
        'bem' => 'বেম্বা',
        'bez' => 'বেনা',
        'bg' => 'বুলগেরিয়',
        'bgc' => 'হরিয়ানভি',
        'bgn' => 'পশ্চিম বালোচি',
        'bho' => 'ভোজপুরি',
        'bi' => 'বিসলামা',
        'bik' => 'বিকোল',
        'bin' => 'বিনি',
        'bla' => 'সিকসিকা',
        'blo' => 'অ্যানি',
        'bm' => 'বামবারা',
        'bn' => 'বাংলা',
        'bo' => 'তিব্বতি',
        'br' => 'ব্রেটন',
        'bra' => 'ব্রাজ',
        'brx' => 'বোড়ো',
        'bs' => 'বসনীয়',
        'bua' => 'বুরিয়াত',
        'bug' => 'বুগিনিজ',
        'byn' => 'ব্লিন',
        'ca' => 'কাতালান',
        'cad' => 'ক্যাডো',
        'car' => 'ক্যারিব',
        'cay' => 'কায়ুগা',
        'cch' => 'আত্সাম',
        'ccp' => 'চাকমা',
        'ce' => 'চেচেন',
        'ceb' => 'চেবুয়ানো',
        'cgg' => 'চিগা',
        'ch' => 'চামোরো',
        'chb' => 'চিবচা',
        'chg' => 'চাগাতাই',
        'chk' => 'চুকিজ',
        'chm' => 'মারি',
        'chn' => 'চিনুক জার্গন',
        'cho' => 'চকটাও',
        'chp' => 'চিপেওয়ান',
        'chr' => 'চেরোকী',
        'chy' => 'চেইয়েন',
        'ckb' => 'মধ্য কুর্দিশ',
        'clc' => 'চিলকোটিন',
        'co' => 'কর্সিকান',
        'cop' => 'কপটিক',
        'cr' => 'ক্রি',
        'crg' => 'মিচিফ',
        'crh' => 'ক্রিমিয়ান তুর্কি',
        'crj' => 'দক্ষিণ পূর্ব ক্রী',
        'crk' => 'প্লেনস ক্রী',
        'crl' => 'উত্তর পূর্ব ক্রী',
        'crm' => 'মুস ক্রী',
        'crr' => 'ক্যারোলিনা অ্যাল্গঙ্কুইয়ান',
        'crs' => 'সেসেলওয়া ক্রেওল ফ্রেঞ্চ',
        'cs' => 'চেক',
        'csb' => 'কাশুবিয়ান',
        'csw' => 'সোয়াম্পি ক্রী',
        'cu' => 'চার্চ স্লাভিক',
        'cv' => 'চুবাস',
        'cy' => 'ওয়েলশ',
        'da' => 'ডেনিশ',
        'dak' => 'ডাকোটা',
        'dar' => 'দার্গওয়া',
        'dav' => 'তাইতা',
        'de' => 'জার্মান',
        'del' => 'ডেলাওয়ের',
        'den' => 'স্ল্যাভ',
        'dgr' => 'দোগ্রীব',
        'din' => 'ডিংকা',
        'dje' => 'জার্মা',
        'doi' => 'ডোগরি',
        'dsb' => 'নিম্নতর সোর্বিয়ান',
        'dua' => 'দুয়ালা',
        'dum' => 'মধ্য ডাচ',
        'dv' => 'দিবেহি',
        'dyo' => 'জোলা-ফনী',
        'dyu' => 'ডিউলা',
        'dz' => 'জোংখা',
        'dzg' => 'দাজাগা',
        'ebu' => 'এম্বু',
        'ee' => 'ইউয়ি',
        'efi' => 'এফিক',
        'egy' => 'প্রাচীন মিশরীয়',
        'eka' => 'ইকাজুক',
        'el' => 'গ্রিক',
        'elx' => 'এলামাইট',
        'en' => 'ইংরেজি',
        'enm' => 'মধ্য ইংরেজি',
        'eo' => 'এস্পেরান্তো',
        'es' => 'স্প্যানিশ',
        'et' => 'এস্তোনীয়',
        'eu' => 'বাস্ক',
        'ewo' => 'ইওন্ডো',
        'fa' => 'ফার্সি',
        'fan' => 'ফ্যাঙ্গ',
        'fat' => 'ফান্তি',
        'ff' => 'ফুলাহ্',
        'fi' => 'ফিনিশ',
        'fil' => 'ফিলিপিনো',
        'fj' => 'ফিজিয়ান',
        'fo' => 'ফেরোইস',
        'fon' => 'ফন',
        'fr' => 'ফরাসি',
        'frc' => 'কাজুন ফরাসি',
        'frm' => 'মধ্য ফরাসি',
        'fro' => 'প্রাচীন ফরাসি',
        'frr' => 'উত্তরাঞ্চলীয় ফ্রিসিয়ান',
        'frs' => 'পূর্ব ফ্রিসিয়',
        'fur' => 'ফ্রিউলিও',
        'fy' => 'পশ্চিম ফ্রিসিয়ান',
        'ga' => 'আইরিশ',
        'gaa' => 'গা',
        'gag' => 'গাগাউজ',
        'gan' => 'gan',
        'gay' => 'গায়ো',
        'gba' => 'বায়া',
        'gd' => 'স্কটিশ-গ্যেলিক',
        'gez' => 'গীজ',
        'gil' => 'গিলবার্টিজ',
        'gl' => 'গ্যালিশিয়',
        'gmh' => 'মধ্য-উচ্চ জার্মানি',
        'gn' => 'গুয়ারানি',
        'goh' => 'প্রাচীন উচ্চ জার্মানি',
        'gon' => 'গোন্ডি',
        'gor' => 'গোরোন্তালো',
        'got' => 'গথিক',
        'grb' => 'গ্রেবো',
        'grc' => 'প্রাচীন গ্রীক',
        'gsw' => 'সুইস জার্মান',
        'gu' => 'গুজরাটি',
        'guz' => 'গুসী',
        'gv' => 'ম্যাঙ্কস',
        'gwi' => 'গওইচ্’ইন',
        'ha' => 'হাউসা',
        'hai' => 'হাইডা',
        'haw' => 'হাওয়াইয়ান',
        'hax' => 'দক্ষিণী হায়দা',
        'he' => 'হিব্রু',
        'hi' => 'হিন্দি',
        'hil' => 'হিলিগ্যায়নোন',
        'hit' => 'হিট্টিট',
        'hmn' => 'হ্‌মোঙ',
        'ho' => 'হিরি মোতু',
        'hr' => 'ক্রোয়েশীয়',
        'hsb' => 'উচ্চ সোর্বিয়ান',
        'hsn' => 'Xiang চীনা',
        'ht' => 'হাইতিয়ান ক্রেওল',
        'hu' => 'হাঙ্গেরীয়',
        'hup' => 'হুপা',
        'hur' => 'হাল্কোমেলেম',
        'hy' => 'আর্মেনিয়',
        'hz' => 'হেরেরো',
        'ia' => 'ইন্টারলিঙ্গুয়া',
        'iba' => 'ইবান',
        'ibb' => 'ইবিবিও',
        'id' => 'ইন্দোনেশীয়',
        'ie' => 'ইন্টারলিঙ্গ',
        'ig' => 'ইগ্‌বো',
        'ii' => 'সিচুয়ান য়ি',
        'ik' => 'ইনুপিয়াক',
        'ikt' => 'পশ্চিম কানাডিয় ইনুক্টিটুট',
        'ilo' => 'ইলোকো',
        'inh' => 'ইঙ্গুশ',
        'io' => 'ইডো',
        'is' => 'আইসল্যান্ডীয়',
        'it' => 'ইতালিয়',
        'iu' => 'ইনুক্টিটুট',
        'ja' => 'জাপানি',
        'jbo' => 'লোজবান',
        'jgo' => 'গোম্বা',
        'jmc' => 'মাকামে',
        'jpr' => 'জুদেও ফার্সি',
        'jrb' => 'জুদেও আরবি',
        'jv' => 'জাভানিজ',
        'ka' => 'জর্জিয়ান',
        'kaa' => 'কারা-কাল্পাক',
        'kab' => 'কাবাইলে',
        'kac' => 'কাচিন',
        'kaj' => 'জজু',
        'kam' => 'কাম্বা',
        'kaw' => 'কাউই',
        'kbd' => 'কাবার্ডিয়ান',
        'kcg' => 'টিয়াপ',
        'kde' => 'মাকোন্দে',
        'kea' => 'কাবুভারদিয়ানু',
        'kfo' => 'কোরো',
        'kg' => 'কঙ্গো',
        'kgp' => 'কেইনগ্যাং',
        'kha' => 'খাশি',
        'kho' => 'খোটানিজ',
        'khq' => 'কোয়রা চীনি',
        'ki' => 'কিকুয়ু',
        'kj' => 'কোয়ানিয়ামা',
        'kk' => 'কাজাখ',
        'kkj' => 'কাকো',
        'kl' => 'কালাল্লিসুট',
        'kln' => 'কালেনজিন',
        'km' => 'খমের',
        'kmb' => 'কিম্বুন্দু',
        'kn' => 'কন্নড়',
        'ko' => 'কোরিয়ান',
        'koi' => 'কমি-পারমিআক',
        'kok' => 'কোঙ্কানি',
        'kos' => 'কোস্রাইন',
        'kpe' => 'ক্‌পেল্লে',
        'kr' => 'কানুরি',
        'krc' => 'কারচে-বাল্কার',
        'krl' => 'কারেলিয়ান',
        'kru' => 'কুরুখ',
        'ks' => 'কাশ্মীরি',
        'ksb' => 'শাম্বালা',
        'ksf' => 'বাফিয়া',
        'ksh' => 'কলোগনিয়ান',
        'ku' => 'কুর্দিশ',
        'kum' => 'কুমিয়াক',
        'kut' => 'কুটেনাই',
        'kv' => 'কোমি',
        'kw' => 'কর্ণিশ',
        'kwk' => 'কোয়াক’ওয়ালা',
        'kxv' => 'কুভি',
        'ky' => 'কির্গিজ',
        'la' => 'লাতিন',
        'lad' => 'লাদিনো',
        'lag' => 'লাঙ্গি',
        'lah' => 'লান্ডা',
        'lam' => 'লাম্বা',
        'lb' => 'লুক্সেমবার্গীয়',
        'lez' => 'লেজঘিয়ান',
        'lg' => 'গান্ডা',
        'li' => 'লিম্বুর্গিশ',
        'lij' => 'লিগুরিয়ান',
        'lil' => 'লিল্লুয়েট',
        'lkt' => 'লাকোটা',
        'lmo' => 'লম্বার্ড',
        'ln' => 'লিঙ্গালা',
        'lo' => 'লাও',
        'lol' => 'মোঙ্গো',
        'lou' => 'লুইসিয়ানা ক্রেওল',
        'loz' => 'লোজি',
        'lrc' => 'উত্তরাঞ্চলীয় লুরি',
        'lsm' => 'সামিয়া',
        'lt' => 'লিথুয়েনীয়',
        'lu' => 'লুবা-কাটাঙ্গা',
        'lua' => 'লুবা-লুলুয়া',
        'lui' => 'লুইসেনো',
        'lun' => 'লুন্ডা',
        'luo' => 'লুয়ো',
        'lus' => 'মিজো',
        'luy' => 'লুইয়া',
        'lv' => 'লাত্‌ভীয়',
        'mad' => 'মাদুরেজ',
        'mag' => 'মাগাহি',
        'mai' => 'মৈথিলি',
        'mak' => 'ম্যাকাসার',
        'man' => 'ম্যান্ডিঙ্গো',
        'mas' => 'মাসাই',
        'mdf' => 'মোকশা',
        'mdr' => 'ম্যাণ্ডার',
        'men' => 'মেন্ডে',
        'mer' => 'মেরু',
        'mfe' => 'মরিসিয়েন',
        'mg' => 'মালাগাসি',
        'mga' => 'মধ্য আইরিশ',
        'mgh' => 'মাখুয়া-মেত্তো',
        'mgo' => 'মেটা',
        'mh' => 'মার্শালিজ',
        'mi' => 'মাওরি',
        'mic' => 'মিকম্যাক',
        'min' => 'মিনাংকাবাউ',
        'mk' => 'ম্যাসিডোনীয়',
        'ml' => 'মালায়ালাম',
        'mn' => 'মঙ্গোলিয়',
        'mnc' => 'মাঞ্চু',
        'mni' => 'মণিপুরী',
        'moe' => 'ইন্নু-এমুন',
        'moh' => 'মোহাওক',
        'mos' => 'মসি',
        'mr' => 'মারাঠি',
        'ms' => 'মালয়',
        'mt' => 'মল্টিয়',
        'mua' => 'মুদাঙ্গ',
        'mus' => 'মুস্কোগী',
        'mwl' => 'মিরান্ডিজ',
        'mwr' => 'মারোয়ারি',
        'my' => 'বর্মি',
        'myv' => 'এরজিয়া',
        'mzn' => 'মাজানদেরানি',
        'na' => 'নাউরু',
        'nap' => 'নেয়াপোলিটান',
        'naq' => 'নামা',
        'nb' => 'নরওয়েজিয়ান বোকমাল',
        'nd' => 'উত্তর এন্দেবেলে',
        'nds' => 'নিম্ন জার্মানি',
        'ne' => 'নেপালী',
        'new' => 'নেওয়ারি',
        'ng' => 'এন্দোঙ্গা',
        'nia' => 'নিয়াস',
        'niu' => 'নিউয়ান',
        'nl' => 'ওলন্দাজ',
        'nmg' => 'কোয়াসিও',
        'nn' => 'নরওয়েজিয়ান নিনর্স্ক',
        'nnh' => 'নগিয়েম্বুন',
        'no' => 'নরওয়েজীয়',
        'nog' => 'নোগাই',
        'non' => 'প্রাচীন নর্স',
        'nqo' => 'এন’কো',
        'nr' => 'দক্ষিণ এনডেবেলে',
        'nso' => 'উত্তরাঞ্চলীয় সোথো',
        'nus' => 'নুয়ার',
        'nv' => 'নাভাজো',
        'nwc' => 'প্রাচীন নেওয়ারী',
        'ny' => 'নায়াঞ্জা',
        'nym' => 'ন্যায়ামওয়েজি',
        'nyn' => 'ন্যায়াঙ্কোলে',
        'nyo' => 'ন্যোরো',
        'nzi' => 'এনজিমা',
        'oc' => 'অক্সিটান',
        'oj' => 'ওজিবওয়া',
        'ojb' => 'উত্তর পশ্চিম ওজিবোয়া',
        'ojc' => 'মধ্য ওজিবুয়া',
        'ojs' => 'ওজি-ক্রী',
        'ojw' => 'পশ্চিম ওজিবোয়া',
        'oka' => 'ওকানাগান',
        'om' => 'অরোমো',
        'or' => 'ওড়িয়া',
        'os' => 'ওসেটিক',
        'osa' => 'ওসেজ',
        'ota' => 'অটোমান তুর্কি',
        'pa' => 'পাঞ্জাবী',
        'pag' => 'পাঙ্গাসিনান',
        'pal' => 'পাহ্লাভি',
        'pam' => 'পাম্পাঙ্গা',
        'pap' => 'পাপিয়ামেন্টো',
        'pau' => 'পালায়ুয়ান',
        'pcm' => 'নাইজেরিয় পিজিন',
        'peo' => 'প্রাচীন ফার্সি',
        'phn' => 'ফোনিশীয়ান',
        'pi' => 'পালি',
        'pis' => 'পিজিন',
        'pl' => 'পোলিশ',
        'pon' => 'পোহ্নপেইয়ান',
        'pqm' => 'মালিসেট-পাসামাকুয়োড্ডি',
        'prg' => 'প্রুশিয়ান',
        'pro' => 'প্রাচীন প্রোভেনসাল',
        'ps' => 'পাশতু',
        'pt' => 'পর্তুগীজ',
        'qu' => 'কেচুয়া',
        'quc' => 'কি‘চে',
        'raj' => 'রাজস্থানী',
        'rap' => 'রাপানুই',
        'rar' => 'রারোটোংগান',
        'rhg' => 'রোহিঙ্গা',
        'rm' => 'রোমান্স',
        'rn' => 'রুন্দি',
        'ro' => 'রোমানীয়',
        'rof' => 'রম্বো',
        'rom' => 'রোমানি',
        'ru' => 'রুশ',
        'rup' => 'আরোমেনিয়',
        'rw' => 'কিনয়ারোয়ান্ডা',
        'rwk' => 'রাওয়া',
        'sa' => 'সংস্কৃত',
        'sad' => 'সান্দাওয়ে',
        'sah' => 'শাখা',
        'sam' => 'সামারিটান আরামিক',
        'saq' => 'সামবুরু',
        'sas' => 'সাসাক',
        'sat' => 'সাঁওতালি',
        'sba' => 'গাম্বে',
        'sbp' => 'সাঙ্গু',
        'sc' => 'সার্ডিনিয়ান',
        'scn' => 'সিসিলিয়ান',
        'sco' => 'স্কটস',
        'sd' => 'সিন্ধি',
        'sdh' => 'দক্ষিণ কুর্দিশ',
        'se' => 'উত্তরাঞ্চলীয় সামি',
        'seh' => 'সেনা',
        'sel' => 'সেল্কুপ',
        'ses' => 'কোয়রাবোরো সেন্নি',
        'sg' => 'সাঙ্গো',
        'sga' => 'প্রাচীন আইরিশ',
        'sh' => 'সার্বো-ক্রোয়েশিয়',
        'shi' => 'তাচেলহিত',
        'shn' => 'শান',
        'si' => 'সিংহলী',
        'sid' => 'সিডামো',
        'sk' => 'স্লোভাক',
        'sl' => 'স্লোভেনীয়',
        'slh' => 'দক্ষিণী লুশুটসীড',
        'sm' => 'সামোয়ান',
        'sma' => 'দক্ষিণাঞ্চলীয় সামি',
        'smj' => 'লুলে সামি',
        'smn' => 'ইনারি সামি',
        'sms' => 'স্কোল্ট সামি',
        'sn' => 'শোনা',
        'snk' => 'সোনিঙ্কে',
        'so' => 'সোমালি',
        'sog' => 'সোগডিয়ান',
        'sq' => 'আলবেনীয়',
        'sr' => 'সার্বীয়',
        'srn' => 'স্রানান টোঙ্গো',
        'srr' => 'সেরের',
        'ss' => 'সোয়াতি',
        'ssy' => 'সাহো',
        'st' => 'দক্ষিন সোথো',
        'str' => 'স্ট্রেটস সালিস',
        'su' => 'সুদানী',
        'suk' => 'সুকুমা',
        'sus' => 'সুসু',
        'sux' => 'সুমেরীয়',
        'sv' => 'সুইডিশ',
        'sw' => 'সোয়াহিলি',
        'swb' => 'কমোরিয়ান',
        'syc' => 'প্রাচীন সিরিও',
        'syr' => 'সিরিয়াক',
        'szl' => 'সিলেশিয়ান',
        'ta' => 'তামিল',
        'tce' => 'দক্ষিণী টুচোন',
        'te' => 'তেলুগু',
        'tem' => 'টাইম্নে',
        'teo' => 'তেসো',
        'ter' => 'তেরেনো',
        'tet' => 'তেতুম',
        'tg' => 'তাজিক',
        'tgx' => 'তাগিশ',
        'th' => 'থাই',
        'tht' => 'তাহ্লতান',
        'ti' => 'তিগরিনিয়া',
        'tig' => 'টাইগ্রে',
        'tiv' => 'টিভ',
        'tk' => 'তুর্কমেনী',
        'tkl' => 'টোকেলাউ',
        'tl' => 'তাগালগ',
        'tlh' => 'ক্লিঙ্গন',
        'tli' => 'ত্লিঙ্গিট',
        'tmh' => 'তামাশেক',
        'tn' => 'সোয়ানা',
        'to' => 'টোঙ্গান',
        'tog' => 'নায়াসা টোঙ্গা',
        'tok' => 'টোকি পোনা',
        'tpi' => 'টোক পিসিন',
        'tr' => 'তুর্কী',
        'trv' => 'তারোকো',
        'ts' => 'সঙ্গা',
        'tsi' => 'সিমশিয়ান',
        'tt' => 'তাতার',
        'ttm' => 'উত্তরাঞ্চলীয় টুচোন',
        'tum' => 'তুম্বুকা',
        'tvl' => 'টুভালু',
        'tw' => 'টোয়াই',
        'twq' => 'তাসাওয়াক',
        'ty' => 'তাহিতিয়ান',
        'tyv' => 'টুভিনিয়ান',
        'tzm' => 'সেন্ট্রাল আটলাস তামাজিগাত',
        'udm' => 'উডমুর্ট',
        'ug' => 'উইঘুর',
        'uga' => 'উগারিটিক',
        'uk' => 'ইউক্রেনীয়',
        'umb' => 'উম্বুন্দু',
        'ur' => 'উর্দু',
        'uz' => 'উজবেক',
        'vai' => 'ভাই',
        've' => 'ভেন্ডা',
        'vec' => 'ভেনেশিয়ান',
        'vi' => 'ভিয়েতনামী',
        'vmw' => 'মাখুওয়া',
        'vo' => 'ভোলাপুক',
        'vot' => 'ভোটিক',
        'vun' => 'ভুঞ্জো',
        'wa' => 'ওয়ালুন',
        'wae' => 'ওয়ালসার',
        'wal' => 'ওলায়ট্টা',
        'war' => 'ওয়ারে',
        'was' => 'ওয়াশো',
        'wbp' => 'ওয়ার্লপিরি',
        'wo' => 'ওলোফ',
        'wuu' => 'উ চীনা',
        'xal' => 'কাল্মাইক',
        'xh' => 'জোসা',
        'xnr' => 'কাংরি',
        'xog' => 'সোগা',
        'yao' => 'ইয়াও',
        'yap' => 'ইয়াপেসে',
        'yav' => 'ইয়াঙ্গবেন',
        'ybb' => 'ইয়েম্বা',
        'yi' => 'ইদ্দিশ',
        'yo' => 'ইওরুবা',
        'yrl' => 'নহিংগাটু',
        'yue' => 'ক্যান্টোনিজ',
        'za' => 'ঝু্য়াঙ',
        'zap' => 'জাপোটেক',
        'zbl' => 'চিত্র ভাষা',
        'zen' => 'জেনাগা',
        'zgh' => 'আদর্শ মরক্কোন তামাজিগাত',
        'zh' => 'চীনা',
        'zu' => 'জুলু',
        'zun' => 'জুনি',
        'zza' => 'জাজা',
    ],
    'LocalizedNames' => [
        'ar_001' => 'আধুনিক আদর্শ আরবি',
        'en_US' => 'ইংরেজি (আমেরিকা)',
        'es_ES' => 'স্প্যানিশ (ইউরোপ)',
        'fa_AF' => 'দারি',
        'nds_NL' => 'লো স্যাক্সন',
        'nl_BE' => 'ফ্লেমিশ',
        'pt_PT' => 'পর্তুগীজ (ইউরোপ)',
        'ro_MD' => 'মলদাভিয়',
        'sw_CD' => 'কঙ্গো সোয়াহিলি',
    ],
];
