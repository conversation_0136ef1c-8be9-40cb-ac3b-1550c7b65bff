<?php

return [
    'Names' => [
        'AD' => '<PERSON><PERSON>',
        'AE' => 'United Arab Emirates',
        'AF' => 'Afuganistani',
        'AG' => 'Antigua ne Barbuda',
        'AI' => 'Anguila',
        'AL' => 'Albania',
        'AM' => 'Armenia',
        'AO' => 'Angola',
        'AR' => 'Ajentina',
        'AS' => 'Samoa ye Amerika',
        'AT' => 'Austria',
        'AU' => 'Australia',
        'AW' => 'Arubha',
        'AZ' => 'Azabajani',
        'BA' => 'Boznia ne Herzegovina',
        'BB' => 'Barbados',
        'BD' => 'Bangladeshi',
        'BE' => 'Beljium',
        'BF' => 'Bukinafaso',
        'BG' => 'Bulgaria',
        'BH' => 'Bahareni',
        'BI' => 'Burundi',
        'BJ' => 'Benini',
        'BM' => 'Bermuda',
        'BN' => 'Burunei',
        'BO' => 'Bolivia',
        'BR' => 'Brazil',
        'BS' => 'Bahama',
        'BT' => 'Bhutani',
        'BW' => 'Botswana',
        'BY' => 'Belarusi',
        'BZ' => 'Belize',
        'CA' => 'Kanada',
        'CD' => 'Democratic Republic of the Congo',
        'CF' => 'Central African Republic',
        'CG' => 'Kongo',
        'CH' => 'Switzerland',
        'CI' => 'Ivory Coast',
        'CK' => 'Zvitsuwa zveCook',
        'CL' => 'Chile',
        'CM' => 'Kameruni',
        'CN' => 'China',
        'CO' => 'Kolombia',
        'CR' => 'Kostarika',
        'CU' => 'Cuba',
        'CV' => 'Zvitsuwa zveCape Verde',
        'CY' => 'Cyprus',
        'CZ' => 'Czech Republic',
        'DE' => 'Germany',
        'DJ' => 'Djibouti',
        'DK' => 'Denmark',
        'DM' => 'Dominica',
        'DO' => 'Dominican Republic',
        'DZ' => 'Aljeria',
        'EC' => 'Ecuador',
        'EE' => 'Estonia',
        'EG' => 'Egypt',
        'ER' => 'Eritrea',
        'ES' => 'Spain',
        'ET' => 'Etiopia',
        'FI' => 'Finland',
        'FJ' => 'Fiji',
        'FK' => 'Zvitsuwa zveFalklands',
        'FM' => 'Micronesia',
        'FR' => 'France',
        'GA' => 'Gabon',
        'GB' => 'United Kingdom',
        'GD' => 'Grenada',
        'GE' => 'Georgia',
        'GF' => 'French Guiana',
        'GH' => 'Ghana',
        'GI' => 'Gibraltar',
        'GL' => 'Greenland',
        'GM' => 'Gambia',
        'GN' => 'Guinea',
        'GP' => 'Guadeloupe',
        'GQ' => 'Equatorial Guinea',
        'GR' => 'Greece',
        'GT' => 'Guatemala',
        'GU' => 'Guam',
        'GW' => 'Guinea-Bissau',
        'GY' => 'Guyana',
        'HN' => 'Honduras',
        'HR' => 'Korasia',
        'HT' => 'Haiti',
        'HU' => 'Hungary',
        'ID' => 'Indonesia',
        'IE' => 'Ireland',
        'IL' => 'Izuraeri',
        'IN' => 'India',
        'IQ' => 'Iraq',
        'IR' => 'Iran',
        'IS' => 'Iceland',
        'IT' => 'Italy',
        'JM' => 'Jamaica',
        'JO' => 'Jordan',
        'JP' => 'Japan',
        'KE' => 'Kenya',
        'KG' => 'Kyrgyzstan',
        'KH' => 'Kambodia',
        'KI' => 'Kiribati',
        'KM' => 'Komoro',
        'KN' => 'Saint Kitts and Nevis',
        'KP' => 'Korea, North',
        'KR' => 'Korea, South',
        'KW' => 'Kuwait',
        'KY' => 'Zvitsuwa zveCayman',
        'KZ' => 'Kazakhstan',
        'LA' => 'Laos',
        'LB' => 'Lebanon',
        'LC' => 'Saint Lucia',
        'LI' => 'Liechtenstein',
        'LK' => 'Sri Lanka',
        'LR' => 'Liberia',
        'LS' => 'Lesotho',
        'LT' => 'Lithuania',
        'LU' => 'Luxembourg',
        'LV' => 'Latvia',
        'LY' => 'Libya',
        'MA' => 'Morocco',
        'MC' => 'Monaco',
        'MD' => 'Moldova',
        'MG' => 'Madagascar',
        'MH' => 'Zvitsuwa zveMarshall',
        'ML' => 'Mali',
        'MM' => 'Myanmar',
        'MN' => 'Mongolia',
        'MP' => 'Zvitsuwa zvekumaodzanyemba eMariana',
        'MQ' => 'Martinique',
        'MR' => 'Mauritania',
        'MS' => 'Montserrat',
        'MT' => 'Malta',
        'MU' => 'Mauritius',
        'MV' => 'Maldives',
        'MW' => 'Malawi',
        'MX' => 'Mexico',
        'MY' => 'Malaysia',
        'MZ' => 'Mozambique',
        'NA' => 'Namibia',
        'NC' => 'New Caledonia',
        'NE' => 'Niger',
        'NF' => 'Chitsuwa cheNorfolk',
        'NG' => 'Nigeria',
        'NI' => 'Nicaragua',
        'NL' => 'Netherlands',
        'NO' => 'Norway',
        'NP' => 'Nepal',
        'NR' => 'Nauru',
        'NU' => 'Niue',
        'NZ' => 'New Zealand',
        'OM' => 'Oman',
        'PA' => 'Panama',
        'PE' => 'Peru',
        'PF' => 'French Polynesia',
        'PG' => 'Papua New Guinea',
        'PH' => 'Philippines',
        'PK' => 'Pakistan',
        'PL' => 'Poland',
        'PM' => 'Saint Pierre and Miquelon',
        'PN' => 'Pitcairn',
        'PR' => 'Puerto Rico',
        'PT' => 'Portugal',
        'PW' => 'Palau',
        'PY' => 'Paraguay',
        'QA' => 'Qatar',
        'RE' => 'Réunion',
        'RO' => 'Romania',
        'RU' => 'Russia',
        'RW' => 'Rwanda',
        'SA' => 'Saudi Arabia',
        'SB' => 'Zvitsuwa zvaSolomon',
        'SC' => 'Seychelles',
        'SD' => 'Sudan',
        'SE' => 'Sweden',
        'SG' => 'Singapore',
        'SH' => 'Saint Helena',
        'SI' => 'Slovenia',
        'SK' => 'Slovakia',
        'SL' => 'Sierra Leone',
        'SM' => 'San Marino',
        'SN' => 'Senegal',
        'SO' => 'Somalia',
        'SR' => 'Suriname',
        'ST' => 'São Tomé and Príncipe',
        'SV' => 'El Salvador',
        'SY' => 'Syria',
        'SZ' => 'Swaziland',
        'TC' => 'Zvitsuwa zveTurk neCaico',
        'TD' => 'Chadi',
        'TG' => 'Togo',
        'TH' => 'Thailand',
        'TJ' => 'Tajikistan',
        'TK' => 'Tokelau',
        'TL' => 'East Timor',
        'TM' => 'Turkmenistan',
        'TN' => 'Tunisia',
        'TO' => 'Tonga',
        'TR' => 'Turkey',
        'TT' => 'Trinidad and Tobago',
        'TV' => 'Tuvalu',
        'TW' => 'Taiwan',
        'TZ' => 'Tanzania',
        'UA' => 'Ukraine',
        'UG' => 'Uganda',
        'US' => 'Amerika',
        'UY' => 'Uruguay',
        'UZ' => 'Uzbekistan',
        'VA' => 'Vatican State',
        'VC' => 'Saint Vincent and the Grenadines',
        'VE' => 'Venezuela',
        'VG' => 'Zvitsuwa zveHingirandi',
        'VI' => 'Zvitsuwa zveAmerika',
        'VN' => 'Vietnam',
        'VU' => 'Vanuatu',
        'WF' => 'Wallis and Futuna',
        'WS' => 'Samoa',
        'YE' => 'Yemen',
        'YT' => 'Mayotte',
        'ZA' => 'South Africa',
        'ZM' => 'Zambia',
        'ZW' => 'Zimbabwe',
    ],
];
