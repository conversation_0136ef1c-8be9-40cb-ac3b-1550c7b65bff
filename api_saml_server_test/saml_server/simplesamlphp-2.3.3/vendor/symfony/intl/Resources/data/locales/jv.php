<?php

return [
    'Names' => [
        'af' => 'Afrika',
        'af_NA' => 'Afrika (Namibia)',
        'af_ZA' => 'Afrika (Afrika Kidul)',
        'ak' => 'A<PERSON>',
        'ak_GH' => '<PERSON><PERSON> (Ghana)',
        'am' => 'Amharik',
        'am_ET' => 'Amharik (Étiopia)',
        'ar' => 'Arab',
        'ar_001' => 'Arab (Donya)',
        'ar_AE' => 'Arab (Uni Émirat Arab)',
        'ar_BH' => 'Arab (Bahrain)',
        'ar_DJ' => 'Arab (Jibuti)',
        'ar_DZ' => 'Arab (Aljasair)',
        'ar_EG' => 'Arab (Mesir)',
        'ar_EH' => 'Arab (Sahara Kulon)',
        'ar_ER' => 'Arab (Éritréa)',
        'ar_IL' => 'Arab (Israèl)',
        'ar_IQ' => 'Arab (Irak)',
        'ar_JO' => 'Arab (Yordania)',
        'ar_KM' => 'Arab (Komoro)',
        'ar_KW' => 'Arab (Kuwait)',
        'ar_LB' => 'Arab (Libanon)',
        'ar_LY' => 'Arab (Libya)',
        'ar_MA' => 'Arab (Maroko)',
        'ar_MR' => 'Arab (Mauritania)',
        'ar_OM' => 'Arab (Oman)',
        'ar_PS' => 'Arab (Tlatah Palèstina)',
        'ar_QA' => 'Arab (Katar)',
        'ar_SA' => 'Arab (Arab Saudi)',
        'ar_SD' => 'Arab (Sudan)',
        'ar_SO' => 'Arab (Somalia)',
        'ar_SS' => 'Arab (Sudan Kidul)',
        'ar_SY' => 'Arab (Suriah)',
        'ar_TD' => 'Arab (Chad)',
        'ar_TN' => 'Arab (Tunisia)',
        'ar_YE' => 'Arab (Yaman)',
        'as' => 'Assam',
        'as_IN' => 'Assam (Indhia)',
        'az' => 'Azerbaijan',
        'az_AZ' => 'Azerbaijan (Azerbaijan)',
        'az_Cyrl' => 'Azerbaijan (Sirilik)',
        'az_Cyrl_AZ' => 'Azerbaijan (Sirilik, Azerbaijan)',
        'az_Latn' => 'Azerbaijan (Latin)',
        'az_Latn_AZ' => 'Azerbaijan (Latin, Azerbaijan)',
        'be' => 'Belarus',
        'be_BY' => 'Belarus (Bélarus)',
        'bg' => 'Bulgaria',
        'bg_BG' => 'Bulgaria (Bulgari)',
        'bm' => 'Bambara',
        'bm_ML' => 'Bambara (Mali)',
        'bn' => 'Bengali',
        'bn_BD' => 'Bengali (Banggaladésa)',
        'bn_IN' => 'Bengali (Indhia)',
        'bo' => 'Tibet',
        'bo_CN' => 'Tibet (Tyongkok)',
        'bo_IN' => 'Tibet (Indhia)',
        'br' => 'Breton',
        'br_FR' => 'Breton (Prancis)',
        'bs' => 'Bosnia lan Hercegovina',
        'bs_BA' => 'Bosnia lan Hercegovina (Bosnia lan Hèrségovina)',
        'bs_Cyrl' => 'Bosnia lan Hercegovina (Sirilik)',
        'bs_Cyrl_BA' => 'Bosnia lan Hercegovina (Sirilik, Bosnia lan Hèrségovina)',
        'bs_Latn' => 'Bosnia lan Hercegovina (Latin)',
        'bs_Latn_BA' => 'Bosnia lan Hercegovina (Latin, Bosnia lan Hèrségovina)',
        'ca' => 'Katala',
        'ca_AD' => 'Katala (Andora)',
        'ca_ES' => 'Katala (Sepanyol)',
        'ca_FR' => 'Katala (Prancis)',
        'ca_IT' => 'Katala (Itali)',
        'ce' => 'Chechen',
        'ce_RU' => 'Chechen (Rusia)',
        'cs' => 'Ceska',
        'cs_CZ' => 'Ceska (Céko)',
        'cv' => 'Khuvash',
        'cv_RU' => 'Khuvash (Rusia)',
        'cy' => 'Welsh',
        'cy_GB' => 'Welsh (Karajan Manunggal)',
        'da' => 'Dansk',
        'da_DK' => 'Dansk (Dhènemarken)',
        'da_GL' => 'Dansk (Greenland)',
        'de' => 'Jérman',
        'de_AT' => 'Jérman (Ostenrik)',
        'de_BE' => 'Jérman (Bèlgi)',
        'de_CH' => 'Jérman (Switserlan)',
        'de_DE' => 'Jérman (Jérman)',
        'de_IT' => 'Jérman (Itali)',
        'de_LI' => 'Jérman (Liktenstén)',
        'de_LU' => 'Jérman (Luksemburg)',
        'dz' => 'Dzongkha',
        'dz_BT' => 'Dzongkha (Bhutan)',
        'ee' => 'Ewe',
        'ee_GH' => 'Ewe (Ghana)',
        'ee_TG' => 'Ewe (Togo)',
        'el' => 'Yunani',
        'el_CY' => 'Yunani (Siprus)',
        'el_GR' => 'Yunani (Grikenlan)',
        'en' => 'Inggris',
        'en_001' => 'Inggris (Donya)',
        'en_150' => 'Inggris (Éropah)',
        'en_AE' => 'Inggris (Uni Émirat Arab)',
        'en_AG' => 'Inggris (Antigua lan Barbuda)',
        'en_AI' => 'Inggris (Anguilla)',
        'en_AS' => 'Inggris (Samoa Amerika)',
        'en_AT' => 'Inggris (Ostenrik)',
        'en_AU' => 'Inggris (Ostrali)',
        'en_BB' => 'Inggris (Barbadhos)',
        'en_BE' => 'Inggris (Bèlgi)',
        'en_BI' => 'Inggris (Burundi)',
        'en_BM' => 'Inggris (Bermuda)',
        'en_BS' => 'Inggris (Bahama)',
        'en_BW' => 'Inggris (Botswana)',
        'en_BZ' => 'Inggris (Bélisé)',
        'en_CA' => 'Inggris (Kanada)',
        'en_CC' => 'Inggris (Kapuloan Cocos [Keeling])',
        'en_CH' => 'Inggris (Switserlan)',
        'en_CK' => 'Inggris (Kapuloan Cook)',
        'en_CM' => 'Inggris (Kamerun)',
        'en_CX' => 'Inggris (Pulo Natal)',
        'en_CY' => 'Inggris (Siprus)',
        'en_DE' => 'Inggris (Jérman)',
        'en_DK' => 'Inggris (Dhènemarken)',
        'en_DM' => 'Inggris (Dominika)',
        'en_ER' => 'Inggris (Éritréa)',
        'en_FI' => 'Inggris (Finlan)',
        'en_FJ' => 'Inggris (Fiji)',
        'en_FK' => 'Inggris (Kapuloan Falkland)',
        'en_FM' => 'Inggris (Féderasi Mikronésia)',
        'en_GB' => 'Inggris (Karajan Manunggal)',
        'en_GD' => 'Inggris (Grénada)',
        'en_GG' => 'Inggris (Guernsei)',
        'en_GH' => 'Inggris (Ghana)',
        'en_GI' => 'Inggris (Gibraltar)',
        'en_GM' => 'Inggris (Gambia)',
        'en_GU' => 'Inggris (Guam)',
        'en_GY' => 'Inggris (Guyana)',
        'en_HK' => 'Inggris (Laladan Administratif Astamiwa Hong Kong)',
        'en_ID' => 'Inggris (Indonésia)',
        'en_IE' => 'Inggris (Républik Irlan)',
        'en_IL' => 'Inggris (Israèl)',
        'en_IM' => 'Inggris (Pulo Man)',
        'en_IN' => 'Inggris (Indhia)',
        'en_IO' => 'Inggris (Wilayah Inggris ing Segara Hindia)',
        'en_JE' => 'Inggris (Jersey)',
        'en_JM' => 'Inggris (Jamaika)',
        'en_KE' => 'Inggris (Kénya)',
        'en_KI' => 'Inggris (Kiribati)',
        'en_KN' => 'Inggris (Saint Kits lan Nèvis)',
        'en_KY' => 'Inggris (Kapuloan Kéman)',
        'en_LC' => 'Inggris (Santa Lusia)',
        'en_LR' => 'Inggris (Libèria)',
        'en_LS' => 'Inggris (Lésotho)',
        'en_MG' => 'Inggris (Madagaskar)',
        'en_MH' => 'Inggris (Kapuloan Marshall)',
        'en_MO' => 'Inggris (Laladan Administratif Astamiwa Makau)',
        'en_MP' => 'Inggris (Kapuloan Mariana Lor)',
        'en_MS' => 'Inggris (Monsérat)',
        'en_MT' => 'Inggris (Malta)',
        'en_MU' => 'Inggris (Mauritius)',
        'en_MV' => 'Inggris (Maladéwa)',
        'en_MW' => 'Inggris (Malawi)',
        'en_MY' => 'Inggris (Malaysia)',
        'en_NA' => 'Inggris (Namibia)',
        'en_NF' => 'Inggris (Pulo Norfolk)',
        'en_NG' => 'Inggris (Nigéria)',
        'en_NL' => 'Inggris (Walanda)',
        'en_NR' => 'Inggris (Nauru)',
        'en_NU' => 'Inggris (Niue)',
        'en_NZ' => 'Inggris (Selandia Anyar)',
        'en_PG' => 'Inggris (Papua Nugini)',
        'en_PH' => 'Inggris (Pilipina)',
        'en_PK' => 'Inggris (Pakistan)',
        'en_PN' => 'Inggris (Kapuloan Pitcairn)',
        'en_PR' => 'Inggris (Puèrto Riko)',
        'en_PW' => 'Inggris (Palau)',
        'en_RW' => 'Inggris (Rwanda)',
        'en_SB' => 'Inggris (Kapuloan Suleman)',
        'en_SC' => 'Inggris (Sésèl)',
        'en_SD' => 'Inggris (Sudan)',
        'en_SE' => 'Inggris (Swèdhen)',
        'en_SG' => 'Inggris (Singapura)',
        'en_SH' => 'Inggris (Saint Héléna)',
        'en_SI' => 'Inggris (Slovénia)',
        'en_SL' => 'Inggris (Siéra Léoné)',
        'en_SS' => 'Inggris (Sudan Kidul)',
        'en_SX' => 'Inggris (Sint Martén)',
        'en_SZ' => 'Inggris (Swasiland)',
        'en_TC' => 'Inggris (Turks lan Kapuloan Kaikos)',
        'en_TK' => 'Inggris (Tokelau)',
        'en_TO' => 'Inggris (Tonga)',
        'en_TT' => 'Inggris (Trinidad lan Tobago)',
        'en_TV' => 'Inggris (Tuvalu)',
        'en_TZ' => 'Inggris (Tansania)',
        'en_UG' => 'Inggris (Uganda)',
        'en_UM' => 'Inggris (Kapuloan AS Paling Njaba)',
        'en_US' => 'Inggris (Amérika Sarékat)',
        'en_VC' => 'Inggris (Saint Vinsen lan Grénadin)',
        'en_VG' => 'Inggris (Kapuloan Virgin Britania)',
        'en_VI' => 'Inggris (Kapuloan Virgin Amérika)',
        'en_VU' => 'Inggris (Vanuatu)',
        'en_WS' => 'Inggris (Samoa)',
        'en_ZA' => 'Inggris (Afrika Kidul)',
        'en_ZM' => 'Inggris (Sambia)',
        'en_ZW' => 'Inggris (Simbabwe)',
        'eo' => 'Esperanto',
        'eo_001' => 'Esperanto (Donya)',
        'es' => 'Spanyol',
        'es_419' => 'Spanyol (Amérika Latin)',
        'es_AR' => 'Spanyol (Argèntina)',
        'es_BO' => 'Spanyol (Bolivia)',
        'es_BR' => 'Spanyol (Brasil)',
        'es_BZ' => 'Spanyol (Bélisé)',
        'es_CL' => 'Spanyol (Cilé)',
        'es_CO' => 'Spanyol (Kolombia)',
        'es_CR' => 'Spanyol (Kosta Rika)',
        'es_CU' => 'Spanyol (Kuba)',
        'es_DO' => 'Spanyol (Républik Dominika)',
        'es_EC' => 'Spanyol (Ékuadhor)',
        'es_ES' => 'Spanyol (Sepanyol)',
        'es_GQ' => 'Spanyol (Guinéa Katulistiwa)',
        'es_GT' => 'Spanyol (Guatémala)',
        'es_HN' => 'Spanyol (Honduras)',
        'es_MX' => 'Spanyol (Mèksiko)',
        'es_NI' => 'Spanyol (Nikaragua)',
        'es_PA' => 'Spanyol (Panama)',
        'es_PE' => 'Spanyol (Peru)',
        'es_PH' => 'Spanyol (Pilipina)',
        'es_PR' => 'Spanyol (Puèrto Riko)',
        'es_PY' => 'Spanyol (Paraguay)',
        'es_SV' => 'Spanyol (Èl Salvador)',
        'es_US' => 'Spanyol (Amérika Sarékat)',
        'es_UY' => 'Spanyol (Uruguay)',
        'es_VE' => 'Spanyol (Vénésuéla)',
        'et' => 'Estonia',
        'et_EE' => 'Estonia (Éstonia)',
        'eu' => 'Basque',
        'eu_ES' => 'Basque (Sepanyol)',
        'fa' => 'Persia',
        'fa_AF' => 'Persia (Afganistan)',
        'fa_IR' => 'Persia (Iran)',
        'ff' => 'Fula',
        'ff_Adlm' => 'Fula (Adlam)',
        'ff_Adlm_BF' => 'Fula (Adlam, Burkina Faso)',
        'ff_Adlm_CM' => 'Fula (Adlam, Kamerun)',
        'ff_Adlm_GH' => 'Fula (Adlam, Ghana)',
        'ff_Adlm_GM' => 'Fula (Adlam, Gambia)',
        'ff_Adlm_GN' => 'Fula (Adlam, Guinea)',
        'ff_Adlm_GW' => 'Fula (Adlam, Guinea-Bissau)',
        'ff_Adlm_LR' => 'Fula (Adlam, Libèria)',
        'ff_Adlm_MR' => 'Fula (Adlam, Mauritania)',
        'ff_Adlm_NE' => 'Fula (Adlam, Nigér)',
        'ff_Adlm_NG' => 'Fula (Adlam, Nigéria)',
        'ff_Adlm_SL' => 'Fula (Adlam, Siéra Léoné)',
        'ff_Adlm_SN' => 'Fula (Adlam, Sénégal)',
        'ff_CM' => 'Fula (Kamerun)',
        'ff_GN' => 'Fula (Guinea)',
        'ff_Latn' => 'Fula (Latin)',
        'ff_Latn_BF' => 'Fula (Latin, Burkina Faso)',
        'ff_Latn_CM' => 'Fula (Latin, Kamerun)',
        'ff_Latn_GH' => 'Fula (Latin, Ghana)',
        'ff_Latn_GM' => 'Fula (Latin, Gambia)',
        'ff_Latn_GN' => 'Fula (Latin, Guinea)',
        'ff_Latn_GW' => 'Fula (Latin, Guinea-Bissau)',
        'ff_Latn_LR' => 'Fula (Latin, Libèria)',
        'ff_Latn_MR' => 'Fula (Latin, Mauritania)',
        'ff_Latn_NE' => 'Fula (Latin, Nigér)',
        'ff_Latn_NG' => 'Fula (Latin, Nigéria)',
        'ff_Latn_SL' => 'Fula (Latin, Siéra Léoné)',
        'ff_Latn_SN' => 'Fula (Latin, Sénégal)',
        'ff_MR' => 'Fula (Mauritania)',
        'ff_SN' => 'Fula (Sénégal)',
        'fi' => 'Suomi',
        'fi_FI' => 'Suomi (Finlan)',
        'fo' => 'Faroe',
        'fo_DK' => 'Faroe (Dhènemarken)',
        'fo_FO' => 'Faroe (Kapuloan Faro)',
        'fr' => 'Prancis',
        'fr_BE' => 'Prancis (Bèlgi)',
        'fr_BF' => 'Prancis (Burkina Faso)',
        'fr_BI' => 'Prancis (Burundi)',
        'fr_BJ' => 'Prancis (Bénin)',
        'fr_BL' => 'Prancis (Saint Barthélémi)',
        'fr_CA' => 'Prancis (Kanada)',
        'fr_CD' => 'Prancis (Kongo - Kinshasa)',
        'fr_CF' => 'Prancis (Républik Afrika Tengah)',
        'fr_CG' => 'Prancis (Kongo - Brassaville)',
        'fr_CH' => 'Prancis (Switserlan)',
        'fr_CI' => 'Prancis (Pasisir Gadhing)',
        'fr_CM' => 'Prancis (Kamerun)',
        'fr_DJ' => 'Prancis (Jibuti)',
        'fr_DZ' => 'Prancis (Aljasair)',
        'fr_FR' => 'Prancis (Prancis)',
        'fr_GA' => 'Prancis (Gabon)',
        'fr_GF' => 'Prancis (Guyana Prancis)',
        'fr_GN' => 'Prancis (Guinea)',
        'fr_GP' => 'Prancis (Guadélup)',
        'fr_GQ' => 'Prancis (Guinéa Katulistiwa)',
        'fr_HT' => 'Prancis (Haiti)',
        'fr_KM' => 'Prancis (Komoro)',
        'fr_LU' => 'Prancis (Luksemburg)',
        'fr_MA' => 'Prancis (Maroko)',
        'fr_MC' => 'Prancis (Monako)',
        'fr_MF' => 'Prancis (Santa Martin)',
        'fr_MG' => 'Prancis (Madagaskar)',
        'fr_ML' => 'Prancis (Mali)',
        'fr_MQ' => 'Prancis (Martinik)',
        'fr_MR' => 'Prancis (Mauritania)',
        'fr_MU' => 'Prancis (Mauritius)',
        'fr_NC' => 'Prancis (Kalédonia Anyar)',
        'fr_NE' => 'Prancis (Nigér)',
        'fr_PF' => 'Prancis (Polinesia Prancis)',
        'fr_PM' => 'Prancis (Saint Pièr lan Mikuélon)',
        'fr_RE' => 'Prancis (Réunion)',
        'fr_RW' => 'Prancis (Rwanda)',
        'fr_SC' => 'Prancis (Sésèl)',
        'fr_SN' => 'Prancis (Sénégal)',
        'fr_SY' => 'Prancis (Suriah)',
        'fr_TD' => 'Prancis (Chad)',
        'fr_TG' => 'Prancis (Togo)',
        'fr_TN' => 'Prancis (Tunisia)',
        'fr_VU' => 'Prancis (Vanuatu)',
        'fr_WF' => 'Prancis (Wallis lan Futuna)',
        'fr_YT' => 'Prancis (Mayotte)',
        'fy' => 'Frisia Sisih Kulon',
        'fy_NL' => 'Frisia Sisih Kulon (Walanda)',
        'ga' => 'Irlandia',
        'ga_GB' => 'Irlandia (Karajan Manunggal)',
        'ga_IE' => 'Irlandia (Républik Irlan)',
        'gd' => 'Gaulia',
        'gd_GB' => 'Gaulia (Karajan Manunggal)',
        'gl' => 'Galisia',
        'gl_ES' => 'Galisia (Sepanyol)',
        'gu' => 'Gujarat',
        'gu_IN' => 'Gujarat (Indhia)',
        'gv' => 'Manx',
        'gv_IM' => 'Manx (Pulo Man)',
        'ha' => 'Hausa',
        'ha_GH' => 'Hausa (Ghana)',
        'ha_NE' => 'Hausa (Nigér)',
        'ha_NG' => 'Hausa (Nigéria)',
        'he' => 'Ibrani',
        'he_IL' => 'Ibrani (Israèl)',
        'hi' => 'India',
        'hi_IN' => 'India (Indhia)',
        'hi_Latn' => 'India (Latin)',
        'hi_Latn_IN' => 'India (Latin, Indhia)',
        'hr' => 'Kroasia',
        'hr_BA' => 'Kroasia (Bosnia lan Hèrségovina)',
        'hr_HR' => 'Kroasia (Kroasia)',
        'hu' => 'Hungaria',
        'hu_HU' => 'Hungaria (Honggari)',
        'hy' => 'Armenia',
        'hy_AM' => 'Armenia (Arménia)',
        'ia' => 'Interlingua',
        'ia_001' => 'Interlingua (Donya)',
        'id' => 'Indonesia',
        'id_ID' => 'Indonesia (Indonésia)',
        'ie' => 'Interlingue',
        'ie_EE' => 'Interlingue (Éstonia)',
        'ig' => 'Iqbo',
        'ig_NG' => 'Iqbo (Nigéria)',
        'ii' => 'Sichuan Yi',
        'ii_CN' => 'Sichuan Yi (Tyongkok)',
        'is' => 'Islandia',
        'is_IS' => 'Islandia (Èslan)',
        'it' => 'Italia',
        'it_CH' => 'Italia (Switserlan)',
        'it_IT' => 'Italia (Itali)',
        'it_SM' => 'Italia (San Marino)',
        'it_VA' => 'Italia (Kutha Vatikan)',
        'ja' => 'Jepang',
        'ja_JP' => 'Jepang (Jepang)',
        'jv' => 'Jawa',
        'jv_ID' => 'Jawa (Indonésia)',
        'ka' => 'Georgia',
        'ka_GE' => 'Georgia (Géorgia)',
        'ki' => 'Kikuyu',
        'ki_KE' => 'Kikuyu (Kénya)',
        'kk' => 'Kazakh',
        'kk_Cyrl' => 'Kazakh (Sirilik)',
        'kk_Cyrl_KZ' => 'Kazakh (Sirilik, Kasakstan)',
        'kk_KZ' => 'Kazakh (Kasakstan)',
        'kl' => 'Kalaallisut',
        'kl_GL' => 'Kalaallisut (Greenland)',
        'km' => 'Khmer',
        'km_KH' => 'Khmer (Kamboja)',
        'kn' => 'Kannada',
        'kn_IN' => 'Kannada (Indhia)',
        'ko' => 'Korea',
        'ko_CN' => 'Korea (Tyongkok)',
        'ko_KP' => 'Korea (Korea Lor)',
        'ko_KR' => 'Korea (Koréa Kidul)',
        'ks' => 'Kashmiri',
        'ks_Arab' => 'Kashmiri (hija’iyah)',
        'ks_Arab_IN' => 'Kashmiri (hija’iyah, Indhia)',
        'ks_Deva' => 'Kashmiri (Devanagari)',
        'ks_Deva_IN' => 'Kashmiri (Devanagari, Indhia)',
        'ks_IN' => 'Kashmiri (Indhia)',
        'ku' => 'Kurdis',
        'ku_TR' => 'Kurdis (Turki)',
        'kw' => 'Kernowek',
        'kw_GB' => 'Kernowek (Karajan Manunggal)',
        'ky' => 'Kirgis',
        'ky_KG' => 'Kirgis (Kirgistan)',
        'lb' => 'Luksemburg',
        'lb_LU' => 'Luksemburg (Luksemburg)',
        'lg' => 'Ganda',
        'lg_UG' => 'Ganda (Uganda)',
        'ln' => 'Lingala',
        'ln_AO' => 'Lingala (Angola)',
        'ln_CD' => 'Lingala (Kongo - Kinshasa)',
        'ln_CF' => 'Lingala (Républik Afrika Tengah)',
        'ln_CG' => 'Lingala (Kongo - Brassaville)',
        'lo' => 'Laos',
        'lo_LA' => 'Laos (Laos)',
        'lt' => 'Lithuania',
        'lt_LT' => 'Lithuania (Litowen)',
        'lu' => 'Luba-Katanga',
        'lu_CD' => 'Luba-Katanga (Kongo - Kinshasa)',
        'lv' => 'Latvia',
        'lv_LV' => 'Latvia (Latvia)',
        'mg' => 'Malagasi',
        'mg_MG' => 'Malagasi (Madagaskar)',
        'mi' => 'Maori',
        'mi_NZ' => 'Maori (Selandia Anyar)',
        'mk' => 'Makedonia',
        'mk_MK' => 'Makedonia (Républik Makédonia Lor)',
        'ml' => 'Malayalam',
        'ml_IN' => 'Malayalam (Indhia)',
        'mn' => 'Mongolia',
        'mn_MN' => 'Mongolia (Mongolia)',
        'mr' => 'Marathi',
        'mr_IN' => 'Marathi (Indhia)',
        'ms' => 'Melayu',
        'ms_BN' => 'Melayu (Brunéi)',
        'ms_ID' => 'Melayu (Indonésia)',
        'ms_MY' => 'Melayu (Malaysia)',
        'ms_SG' => 'Melayu (Singapura)',
        'mt' => 'Malta',
        'mt_MT' => 'Malta (Malta)',
        'my' => 'Myanmar',
        'my_MM' => 'Myanmar (Myanmar [Burma])',
        'nb' => 'Bokmål Norwegia',
        'nb_NO' => 'Bokmål Norwegia (Nurwègen)',
        'nb_SJ' => 'Bokmål Norwegia (Svalbard lan Jan Mayen)',
        'nd' => 'Ndebele Lor',
        'nd_ZW' => 'Ndebele Lor (Simbabwe)',
        'ne' => 'Nepal',
        'ne_IN' => 'Nepal (Indhia)',
        'ne_NP' => 'Nepal (Népal)',
        'nl' => 'Walanda',
        'nl_AW' => 'Walanda (Aruba)',
        'nl_BE' => 'Walanda (Bèlgi)',
        'nl_BQ' => 'Walanda (Karibia Walanda)',
        'nl_CW' => 'Walanda (Kurasao)',
        'nl_NL' => 'Walanda (Walanda)',
        'nl_SR' => 'Walanda (Suriname)',
        'nl_SX' => 'Walanda (Sint Martén)',
        'nn' => 'Nynorsk Norwegia',
        'nn_NO' => 'Nynorsk Norwegia (Nurwègen)',
        'no' => 'Norwegia',
        'no_NO' => 'Norwegia (Nurwègen)',
        'oc' => 'Ossitan',
        'oc_ES' => 'Ossitan (Sepanyol)',
        'oc_FR' => 'Ossitan (Prancis)',
        'om' => 'Oromo',
        'om_ET' => 'Oromo (Étiopia)',
        'om_KE' => 'Oromo (Kénya)',
        'or' => 'Odia',
        'or_IN' => 'Odia (Indhia)',
        'os' => 'Ossetia',
        'os_GE' => 'Ossetia (Géorgia)',
        'os_RU' => 'Ossetia (Rusia)',
        'pa' => 'Punjab',
        'pa_Arab' => 'Punjab (hija’iyah)',
        'pa_Arab_PK' => 'Punjab (hija’iyah, Pakistan)',
        'pa_Guru' => 'Punjab (Gurmukhi)',
        'pa_Guru_IN' => 'Punjab (Gurmukhi, Indhia)',
        'pa_IN' => 'Punjab (Indhia)',
        'pa_PK' => 'Punjab (Pakistan)',
        'pl' => 'Polandia',
        'pl_PL' => 'Polandia (Polen)',
        'ps' => 'Pashto',
        'ps_AF' => 'Pashto (Afganistan)',
        'ps_PK' => 'Pashto (Pakistan)',
        'pt' => 'Portugis',
        'pt_AO' => 'Portugis (Angola)',
        'pt_BR' => 'Portugis (Brasil)',
        'pt_CH' => 'Portugis (Switserlan)',
        'pt_CV' => 'Portugis (Pongol Verdé)',
        'pt_GQ' => 'Portugis (Guinéa Katulistiwa)',
        'pt_GW' => 'Portugis (Guinea-Bissau)',
        'pt_LU' => 'Portugis (Luksemburg)',
        'pt_MO' => 'Portugis (Laladan Administratif Astamiwa Makau)',
        'pt_MZ' => 'Portugis (Mosambik)',
        'pt_PT' => 'Portugis (Portugal)',
        'pt_ST' => 'Portugis (Sao Tomé lan Principé)',
        'pt_TL' => 'Portugis (Timor Leste)',
        'qu' => 'Quechua',
        'qu_BO' => 'Quechua (Bolivia)',
        'qu_EC' => 'Quechua (Ékuadhor)',
        'qu_PE' => 'Quechua (Peru)',
        'rm' => 'Roman',
        'rm_CH' => 'Roman (Switserlan)',
        'rn' => 'Rundi',
        'rn_BI' => 'Rundi (Burundi)',
        'ro' => 'Rumania',
        'ro_MD' => 'Rumania (Moldova)',
        'ro_RO' => 'Rumania (Ruméni)',
        'ru' => 'Rusia',
        'ru_BY' => 'Rusia (Bélarus)',
        'ru_KG' => 'Rusia (Kirgistan)',
        'ru_KZ' => 'Rusia (Kasakstan)',
        'ru_MD' => 'Rusia (Moldova)',
        'ru_RU' => 'Rusia (Rusia)',
        'ru_UA' => 'Rusia (Ukrania)',
        'rw' => 'Kinyarwanda',
        'rw_RW' => 'Kinyarwanda (Rwanda)',
        'sa' => 'Sanskerta',
        'sa_IN' => 'Sanskerta (Indhia)',
        'sc' => 'Sardinia',
        'sc_IT' => 'Sardinia (Itali)',
        'sd' => 'Sindhi',
        'sd_Arab' => 'Sindhi (hija’iyah)',
        'sd_Arab_PK' => 'Sindhi (hija’iyah, Pakistan)',
        'sd_Deva' => 'Sindhi (Devanagari)',
        'sd_Deva_IN' => 'Sindhi (Devanagari, Indhia)',
        'sd_IN' => 'Sindhi (Indhia)',
        'sd_PK' => 'Sindhi (Pakistan)',
        'se' => 'Sami Sisih Lor',
        'se_FI' => 'Sami Sisih Lor (Finlan)',
        'se_NO' => 'Sami Sisih Lor (Nurwègen)',
        'se_SE' => 'Sami Sisih Lor (Swèdhen)',
        'sg' => 'Sango',
        'sg_CF' => 'Sango (Républik Afrika Tengah)',
        'si' => 'Sinhala',
        'si_LK' => 'Sinhala (Sri Lanka)',
        'sk' => 'Slowakia',
        'sk_SK' => 'Slowakia (Slowak)',
        'sl' => 'Slovenia',
        'sl_SI' => 'Slovenia (Slovénia)',
        'sn' => 'Shona',
        'sn_ZW' => 'Shona (Simbabwe)',
        'so' => 'Somalia',
        'so_DJ' => 'Somalia (Jibuti)',
        'so_ET' => 'Somalia (Étiopia)',
        'so_KE' => 'Somalia (Kénya)',
        'so_SO' => 'Somalia (Somalia)',
        'sq' => 'Albania',
        'sq_AL' => 'Albania (Albani)',
        'sq_MK' => 'Albania (Républik Makédonia Lor)',
        'sr' => 'Serbia',
        'sr_BA' => 'Serbia (Bosnia lan Hèrségovina)',
        'sr_Cyrl' => 'Serbia (Sirilik)',
        'sr_Cyrl_BA' => 'Serbia (Sirilik, Bosnia lan Hèrségovina)',
        'sr_Cyrl_ME' => 'Serbia (Sirilik, Montenégro)',
        'sr_Cyrl_RS' => 'Serbia (Sirilik, Sèrbi)',
        'sr_Latn' => 'Serbia (Latin)',
        'sr_Latn_BA' => 'Serbia (Latin, Bosnia lan Hèrségovina)',
        'sr_Latn_ME' => 'Serbia (Latin, Montenégro)',
        'sr_Latn_RS' => 'Serbia (Latin, Sèrbi)',
        'sr_ME' => 'Serbia (Montenégro)',
        'sr_RS' => 'Serbia (Sèrbi)',
        'st' => 'Sotho Sisih Kidul',
        'st_LS' => 'Sotho Sisih Kidul (Lésotho)',
        'st_ZA' => 'Sotho Sisih Kidul (Afrika Kidul)',
        'su' => 'Sunda',
        'su_ID' => 'Sunda (Indonésia)',
        'su_Latn' => 'Sunda (Latin)',
        'su_Latn_ID' => 'Sunda (Latin, Indonésia)',
        'sv' => 'Swedia',
        'sv_AX' => 'Swedia (Kapuloan Alan)',
        'sv_FI' => 'Swedia (Finlan)',
        'sv_SE' => 'Swedia (Swèdhen)',
        'sw' => 'Swahili',
        'sw_CD' => 'Swahili (Kongo - Kinshasa)',
        'sw_KE' => 'Swahili (Kénya)',
        'sw_TZ' => 'Swahili (Tansania)',
        'sw_UG' => 'Swahili (Uganda)',
        'ta' => 'Tamil',
        'ta_IN' => 'Tamil (Indhia)',
        'ta_LK' => 'Tamil (Sri Lanka)',
        'ta_MY' => 'Tamil (Malaysia)',
        'ta_SG' => 'Tamil (Singapura)',
        'te' => 'Telugu',
        'te_IN' => 'Telugu (Indhia)',
        'tg' => 'Tajik',
        'tg_TJ' => 'Tajik (Tajikistan)',
        'th' => 'Thailand',
        'th_TH' => 'Thailand (Tanah Thai)',
        'ti' => 'Tigrinya',
        'ti_ER' => 'Tigrinya (Éritréa)',
        'ti_ET' => 'Tigrinya (Étiopia)',
        'tk' => 'Turkmen',
        'tk_TM' => 'Turkmen (Turkménistan)',
        'tn' => 'Tswana',
        'tn_BW' => 'Tswana (Botswana)',
        'tn_ZA' => 'Tswana (Afrika Kidul)',
        'to' => 'Tonga',
        'to_TO' => 'Tonga (Tonga)',
        'tr' => 'Turki',
        'tr_CY' => 'Turki (Siprus)',
        'tr_TR' => 'Turki (Turki)',
        'tt' => 'Tatar',
        'tt_RU' => 'Tatar (Rusia)',
        'ug' => 'Uighur',
        'ug_CN' => 'Uighur (Tyongkok)',
        'uk' => 'Ukraina',
        'uk_UA' => 'Ukraina (Ukrania)',
        'ur' => 'Urdu',
        'ur_IN' => 'Urdu (Indhia)',
        'ur_PK' => 'Urdu (Pakistan)',
        'uz' => 'Uzbek',
        'uz_AF' => 'Uzbek (Afganistan)',
        'uz_Arab' => 'Uzbek (hija’iyah)',
        'uz_Arab_AF' => 'Uzbek (hija’iyah, Afganistan)',
        'uz_Cyrl' => 'Uzbek (Sirilik)',
        'uz_Cyrl_UZ' => 'Uzbek (Sirilik, Usbèkistan)',
        'uz_Latn' => 'Uzbek (Latin)',
        'uz_Latn_UZ' => 'Uzbek (Latin, Usbèkistan)',
        'uz_UZ' => 'Uzbek (Usbèkistan)',
        'vi' => 'Vietnam',
        'vi_VN' => 'Vietnam (Viètnam)',
        'wo' => 'Wolof',
        'wo_SN' => 'Wolof (Sénégal)',
        'xh' => 'Xhosa',
        'xh_ZA' => 'Xhosa (Afrika Kidul)',
        'yi' => 'Yiddish',
        'yi_UA' => 'Yiddish (Ukrania)',
        'yo' => 'Yoruba',
        'yo_BJ' => 'Yoruba (Bénin)',
        'yo_NG' => 'Yoruba (Nigéria)',
        'za' => 'Zhuang',
        'za_CN' => 'Zhuang (Tyongkok)',
        'zh' => 'Tyonghwa',
        'zh_CN' => 'Tyonghwa (Tyongkok)',
        'zh_HK' => 'Tyonghwa (Laladan Administratif Astamiwa Hong Kong)',
        'zh_Hans' => 'Tyonghwa (Prasaja)',
        'zh_Hans_CN' => 'Tyonghwa (Prasaja, Tyongkok)',
        'zh_Hans_HK' => 'Tyonghwa (Prasaja, Laladan Administratif Astamiwa Hong Kong)',
        'zh_Hans_MO' => 'Tyonghwa (Prasaja, Laladan Administratif Astamiwa Makau)',
        'zh_Hans_MY' => 'Tyonghwa (Prasaja, Malaysia)',
        'zh_Hans_SG' => 'Tyonghwa (Prasaja, Singapura)',
        'zh_Hant' => 'Tyonghwa (Tradhisional)',
        'zh_Hant_HK' => 'Tyonghwa (Tradhisional, Laladan Administratif Astamiwa Hong Kong)',
        'zh_Hant_MO' => 'Tyonghwa (Tradhisional, Laladan Administratif Astamiwa Makau)',
        'zh_Hant_MY' => 'Tyonghwa (Tradhisional, Malaysia)',
        'zh_Hant_TW' => 'Tyonghwa (Tradhisional, Taiwan)',
        'zh_MO' => 'Tyonghwa (Laladan Administratif Astamiwa Makau)',
        'zh_SG' => 'Tyonghwa (Singapura)',
        'zh_TW' => 'Tyonghwa (Taiwan)',
        'zu' => 'Zulu',
        'zu_ZA' => 'Zulu (Afrika Kidul)',
    ],
];
