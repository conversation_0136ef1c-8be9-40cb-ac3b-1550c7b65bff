<?php
use App\Http\Middleware\EmployeeLoginCheck;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix("admin")->group(function () {

    // DashboardController
    Route::get('/', 'Admin\DashboardController@overview')
        ->name("admin_overview");

    // Institute
    Route::get('/institute/setup/{institute_id}', 'Admin\InstituteController@instituteSetup')
        ->name("admin_institute_setup");
    /*
    Route::get('/institutes', 'Admin\InstituteController@index')
        ->name("admin_institutes");
    Route::get('/institutes/list', 'Admin\InstituteController@ajaxList')
        ->name("admin_ajax_institute_list");*/
    Route::get('/institutes/add', 'Admin\InstituteController@addInstitute')
        ->name("admin_institute_add");
    Route::post('/institutes/save-new/{institute_id?}', 'Admin\InstituteController@saveNewInstitute')
        ->name("admin_institute_save_new");
    /*Route::delete('/institutes/remove', 'Admin\InstituteController@remove')
        ->name("admin_institute_remove");*/
    Route::post('/covenant/save-new', 'Admin\InstituteController@saveNewCovenant')
        ->name("admin_covenant_save_new");
    Route::post('/covenant/remove', 'Admin\InstituteController@removeCovenant')
        ->name("admin_covenant_remove");
    Route::post('/user_group/save-permission/{institute_id}', 'Admin\InstituteController@saveUserGroupPermission')
        ->name("admin_user_group_permission_save");
    Route::post('/covenant/sort', 'Admin\InstituteController@sort')
        ->name("admin_covenant_sort");
    Route::get('/institute/copy/{from_institute_id}/{to_institute_id}', 'Admin\InstituteController@copyInstituteSetup')
        ->name("admin_copy_institute_setup");

    Route::get('/user/permission/{institute_id}/{user_id}', 'Admin\InstituteController@userPermission')
        ->name("admin_user_permission");
    Route::post('/user/save-permission/{institute_id}/{user_id}', 'Admin\InstituteController@saveUserPermission')
        ->name("admin_user_permission_save");
    Route::delete('/user/remove/{institute_id}', 'Admin\InstituteController@delInstUser')
        ->name("admin_del_inst_user");


    //Institute product category and product routes
    Route::post('/product/save-new', 'Admin\ProductController@saveNewProduct')
        ->name("admin_product_save_new");
    Route::get('/product/edit/{id}', 'Admin\ProductController@editProduct')
        ->name("admin_product_edit");
    Route::post('/product/save', 'Admin\ProductController@saveProduct')
        ->name("admin_product_save");
    Route::post('/product/remove', 'Admin\ProductController@remove')
        ->name("admin_product_remove");
    Route::get('/product/detail/{id}', 'Admin\ProductController@editProductDetail')
        ->name("admin_product_detail");
    Route::post('/product/save-detail/{id}', 'Admin\ProductController@saveProductDetail')
        ->name("admin_product_save_detail");
    Route::post('/product/sort', 'Admin\ProductController@sort')
        ->name("admin_product_sort");
    Route::post('/product-category/save-new', 'Admin\ProductController@saveNewProductCategory')
        ->name("admin_product_category_save_new");
    Route::post('/product-safety-clause/save-new', 'Admin\ProductController@saveNewProductSafetyClause')
        ->name("admin_product_safety_clause_save_new");
    Route::post('/product-safety-clause/remove', 'Admin\ProductController@removeProductSafetyClause')
        ->name("admin_product_safety_clause_remove");
    Route::post('/product-safety-clause/sort', 'Admin\ProductController@productSafetySort')
        ->name("admin_product_safety_clause_sort");

    Route::post('/product-finance-term/save-new', 'Admin\ProductController@saveNewProductFinanceTerm')
        ->name("admin_product_finance_term_save_new");
    Route::post('/product-finance-term/remove', 'Admin\ProductController@removeProductFinanceTerm')
        ->name("admin_product_finance_term_remove");
    Route::post('/product-finance-term/sort', 'Admin\ProductController@productFinanceSort')
        ->name("admin_product_finance_term_sort");

    // Categories
    Route::get('/categories', 'Admin\CategoryController@index')
        ->name("admin_categories");
    Route::get('/categories/list', 'Admin\CategoryController@ajaxList')
        ->name("admin_ajax_category_list");
    Route::get('/categories/edit/{id?}', 'Admin\CategoryController@edit')
        ->name("admin_category_edit");
    Route::post('/categories/save/{id?}', 'Admin\CategoryController@save')
        ->name("admin_category_save");
    Route::delete('/categories/remove', 'Admin\CategoryController@remove')
        ->name("admin_category_remove");

    // Letter Templates Category
    Route::get('/letter-template/categories', 'Admin\LetterTemplateCategoryController@index')
        ->name("admin_letter_template_categories");
    Route::get('/letter-template/categories/list', 'Admin\LetterTemplateCategoryController@ajaxList')
        ->name("admin_ajax_letter_template_category_list");
    Route::get('/letter-template/category/edit/{id?}', 'Admin\LetterTemplateCategoryController@edit')
        ->name("admin_letter_template_category_edit");
    Route::post('/letter-template/category/save/{id?}', 'Admin\LetterTemplateCategoryController@save')
        ->name("admin_letter_template_category_save");
    Route::delete('/letter-template/category/remove', 'Admin\LetterTemplateCategoryController@remove')
        ->name("admin_letter_template_category_remove");

    // Letter Templates
    Route::get('/letter-template/edit/{institute_id}/{id?}', 'Admin\LetterTemplateController@edit')
        ->name("admin_letter_template_edit");
    Route::post('/letter-template/save/{institute_id}/{id?}', 'Admin\LetterTemplateController@save')
        ->name("admin_letter_template_save");
    Route::post('/letter-template/remove', 'Admin\LetterTemplateController@remove')
        ->name("admin_letter_template_remove");

    //Cover Image
    Route::get('/cover-image', 'Admin\CoverImageController@index')
        ->name("admin_cover_images");
    Route::get('/cover-image/list', 'Admin\CoverImageController@ajaxList')
        ->name("admin_ajax_cover_image_list");
    Route::get('/cover-image/edit/{id?}', 'Admin\CoverImageController@edit')
        ->name("admin_cover_image_edit");
    Route::post('/cover-image/save/{id?}', 'Admin\CoverImageController@save')
        ->name("admin_cover_image_save");
    Route::delete('/cover-image/remove', 'Admin\CoverImageController@remove')
        ->name("admin_cover_image_remove");

    // Faqs
    Route::get('/faqs', 'Admin\FaqController@index')
        ->name("admin_faqs");
    Route::get('/faqs/list', 'Admin\FaqController@ajaxList')
        ->name("admin_ajax_faq_list");
    Route::get('/faqs/edit/{id?}', 'Admin\FaqController@edit')
        ->name("admin_faq_edit");
    Route::post('/faqs/save/{id?}', 'Admin\FaqController@save')
        ->name("admin_faq_save");
    Route::delete('/faqs/remove', 'Admin\FaqController@remove')
        ->name("admin_faq_remove");

    // SettingController
    Route::get('/settings/{module?}', 'Admin\SettingController@settingForm')
        ->name("admin_settings");
    Route::get('/greco', 'Admin\SettingController@grecoForm')
        ->name("admin_greco_settings");
    Route::post('/ajax_save_setting', 'Admin\SettingController@ajaxSaveSetting')
        ->name("admin_save_setting");
    Route::post('/ajax_get_translate', 'Admin\SettingController@ajaxGetTranslate')
        ->name("admin_get_translate");

    // UploadController
    Route::post('/gfx_upload', 'Admin\UploadController@ajaxImageUpload')
        ->name('admin_ajax_image_upload');
    Route::post('/pdf_upload', 'Admin\UploadController@ajaxPdfUpload')
        ->name('admin_ajax_pdf_upload');
    Route::post('/xls_upload', 'Admin\UploadController@ajaxXlsUpload')
        ->name('admin_ajax_xls_upload');
    Route::post('/video_upload', 'Admin\UploadController@ajaxVideoUpload')
        ->name('admin_ajax_video_upload');
    Route::post('/audio_upload', 'Admin\UploadController@ajaxAudioUpload')
        ->name('admin_ajax_audio_upload');
    Route::post('/media_upload', 'Admin\UploadController@ajaxMediaUpload')
        ->name('admin_ajax_media_upload');

    //Texts admin module
    Route::get('/app_texts/{module?}', 'Admin\TextController@appTextForm')
        ->name("admin_app_texts");
    Route::get('/app_emails/{module?}', 'Admin\TextController@appEmailForm')
        ->name("admin_app_emails");
    Route::post('/ajax_save_text', 'Admin\TextController@ajaxSaveText')
        ->name("admin_save_text");
    Route::post('/ajax_delete_text', 'Admin\TextController@ajaxDeleteText')
        ->name("admin_delete_text");
    Route::post('/import_texts', 'Admin\TextController@importTexts')
        ->name("admin_import_texts");
    Route::get('/export_texts/{module?}', 'Admin\TextController@exportTexts')
        ->name("admin_export_texts");
    Route::post('/send_preview_mail', 'Admin\TextController@sendPreviewEmail')
        ->name("admin_send_preview_mail");

    // AdminController
    Route::get('/list', 'Admin\AdminController@listAdmin')
        ->name('admin_list_admin');
    Route::get('/admin/ajax/list', 'Admin\AdminController@ajaxList')
        ->name('admin_ajax_admin_list');
    Route::get('/new-admin', 'Admin\AdminController@newAdmin')
        ->name('admin_new');
    Route::post('/save-admin', 'Admin\AdminController@saveAdmin')
        ->name('admin_save');
    Route::post('/save-admin/{id?}', 'Admin\AdminController@saveAdmin')
        ->name('admin_save_now');
    Route::get('/edit-admin/{id}', 'Admin\AdminController@editAdmin')
        ->name('admin_edit');
    Route::get('/regenerate/{id}', 'Admin\AdminController@regenerate')
        ->name('admin_regenerate');
    Route::delete('/delete-admin', 'Admin\AdminController@deleteAdmin')
        ->name('admin_delete');

    // BackendController
    Route::get('/change-pass', 'Admin\BackendController@changePassword')
        ->name('admin_change_password');
    Route::get('/logout', 'Admin\BackendController@logout')
        ->name('admin_logout');
    Route::get('/login', 'Admin\BackendController@login')
        ->name('admin_login');
    Route::post('/login', 'Admin\BackendController@validateLogin')
        ->name('admin_validate_login');
    Route::post('/save-pass', 'Admin\BackendController@saveNewPassword')
        ->name('admin_save_new_password');
    Route::get('/change-password/{type}', 'Admin\BackendController@forceChangePassword')
        ->name('admin_force_change_password');
    Route::post('/change-password', 'Admin\BackendController@saveChangedPassword')
        ->name('admin_save_changed_password');
    Route::get('/forgot-password', 'Admin\BackendController@forgotPassword')
        ->name('admin_forgot_password');
    Route::post('/forgot-password', 'Admin\BackendController@submitForgotPassword')
        ->name('admin_forgot_password_submit');
    Route::get('/resend-mail', 'Admin\BackendController@resendEmail')
        ->name('admin_resend_email');
    Route::get('/reset-password/{code}', 'Admin\BackendController@resetPassword')
        ->name('admin_reset_password');
    Route::post('/reset-password/{code}', 'Admin\BackendController@saveResetPassword')
        ->name('admin_save_reset_password');
    Route::post('/google/login/validate', 'Admin\BackendController@validateGoogleLogin')
        ->name('admin_validate_google_login');
});

Route::prefix("deploy")->middleware(["deploy"])->group(function (){

    // DeploymentController
    Route::get('/migrate', "Deploy\DeploymentController@migrate")
        ->name("deploy_migrate")->withoutMiddleware("web");
    Route::get('/rebuild_cache', "Deploy\DeploymentController@rebuildCache")
        ->name("deploy_rebuild_cache");
    Route::get('/create_first_user', "Deploy\DeploymentController@createFirstUser")
        ->name("deploy_create_first_user");
    Route::get('/maintenance', "Deploy\DeploymentController@maintenance")
        ->name("deploy_maintenance");
	Route::get('/add_maintenance_ip', "Deploy\DeploymentController@addMaintenanceIP")
		->name("deploy_add_ip");
	Route::get('/add_backend_ip', "Deploy\DeploymentController@addBackendIP")
		->name("deploy_add_backend_ip");
     Route::get('/revert_htaccess', "Deploy\DeploymentController@revertHtaccess")
        ->name("deploy_revert_htaccess");
     Route::get('/optimize_images', "Deploy\DeploymentController@optimizeImages")
        ->name("deploy_optimize_images");
     /*Route::get('/generate_webp', "Deploy\DeploymentController@generateWebp")
        ->name("deploy_generate_webp");*/
});

// PWAController
Route::get('/manifest.json', 'Site\PWAController@manifestJson')
    ->name('site_manifest');

// DebugController
Route::get("/saml_debug_logged_in", 'Site\DebugController@index')
    ->name("site_debug_login");
Route::post("/validate/debug_in", 'Site\DebugController@validateLogin')
    ->name("site_validate_saml_debug_login");

//Employee Controller
Route::get("/", 'Site\EmployeeController@dashboard')
    ->name("site_index");
Route::post("/offer/search", 'Site\EmployeeController@searchOffer')
    ->name("site_offer_search");
Route::post("/report-pdf", 'Site\EmployeeController@reportPdf')
    ->name("site_report_pdf");
Route::get("/logout", 'Site\EmployeeController@logout')
    ->name("site_user_logout");

//saml login routes
Route::get("/saml-login", 'Site\SamlController@login')
     ->withoutMiddleware("employeelogincheck")
     ->name("site_saml_login");
Route::get("/saml_login", 'Site\SamlController@login')
     ->withoutMiddleware("employeelogincheck")
     ->name("site_saml_login_old_url_support");
Route::get("/saml-logout", 'Site\SamlController@logout')
     ->withoutMiddleware("employeelogincheck")
     ->name("site_saml_logout");

//Offer Controller
Route::post("/offer/start", 'Site\OfferController@startOffer')
    ->name("site_offer_start");
Route::get("/offer/select-products/{id?}", 'Site\OfferController@selectProducts')
    ->name("site_offer_select_products");
Route::post("/offer/save-products", 'Site\OfferController@saveProducts')
    ->name("site_offer_save_products");
Route::get("/offer/create", 'Site\OfferController@createOffer')
    ->name("site_create_offer");
Route::get("/offer/edit/{id}", 'Site\OfferController@editOffer')
    ->name("site_edit_offer");
Route::post("/offer/save/{session?}", 'Site\OfferController@saveOffer')
    ->name("site_save_offer");
Route::post("/offer/send-mail", 'Site\OfferController@sendMail')
    ->name("site_offer_send_mail");
Route::get("/download/angebot-{offer_code}.pdf", 'Site\OfferController@offerPdfDownload')
    ->name("site_offer_pdf_download");

//CustomerOffer Controller
Route::get("/kunden/login/{offer_code}", 'Site\CustomerOfferController@offerLogin')
    ->name("site_customer_offer_login");
Route::post("/kunden/login/validate", 'Site\CustomerOfferController@offerLoginValidate')
    ->name("site_customer_offer_login_validate");
Route::post("/kunden/login/token-validate", 'Site\CustomerOfferController@offerLoginTokenValidate')
    ->name("site_customer_offer_login_token-validate");

Route::get("/kunden/deckblatt/{offer_code}", 'Site\CustomerOfferController@offerCover')
    ->name("site_customer_offer_cover");
Route::get("/kunden/angebot/{offer_code}", 'Site\CustomerOfferController@offer')
    ->name("site_customer_offer");
Route::get("/kunden/angebot-{offer_code}.pdf", 'Site\CustomerOfferController@offerPdfDownload')
    ->name("site_customer_offer_pdf_download");
