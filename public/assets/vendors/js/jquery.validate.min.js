!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery)}(function(l){l.extend(l.fn,{validate:function(t){var s;if(this.length)return(s=l.data(this[0],"validator"))||(this.attr("novalidate","novalidate"),s=new l.validator(t,this[0]),l.data(this[0],"validator",s),s.settings.onsubmit&&(this.on("click.validate",":submit",function(t){s.settings.submitHandler&&(s.submitButton=t.target),l(this).hasClass("cancel")&&(s.cancelSubmit=!0),void 0!==l(this).attr("formnovalidate")&&(s.cancelSubmit=!0)}),this.on("submit.validate",function(i){function t(){var t,e;return!s.settings.submitHandler||(s.submitButton&&(t=l("<input type='hidden'/>").attr("name",s.submitButton.name).val(l(s.submitButton).val()).appendTo(s.currentForm)),e=s.settings.submitHandler.call(s,s.currentForm,i),s.submitButton&&t.remove(),void 0!==e&&e)}return s.settings.debug&&i.preventDefault(),s.cancelSubmit?(s.cancelSubmit=!1,t()):s.form()?s.pendingRequest?!(s.formSubmitted=!0):t():(s.focusInvalid(),!1)}))),s;t&&t.debug&&window.console&&console.warn("Nothing selected, can't validate, returning nothing.")},valid:function(){var t,e,i;return l(this[0]).is("form")?t=this.validate().form():(i=[],t=!0,e=l(this[0].form).validate(),this.each(function(){(t=e.element(this)&&t)||(i=i.concat(e.errorList))}),e.errorList=i),t},rules:function(t,e){if(this.length){var i,s,r,n,a,o=this[0];if(t)switch(s=(i=l.data(o.form,"validator").settings).rules,r=l.validator.staticRules(o),t){case"add":l.extend(r,l.validator.normalizeRule(e)),delete r.messages,s[o.name]=r,e.messages&&(i.messages[o.name]=l.extend(i.messages[o.name],e.messages));break;case"remove":return e?(a={},l.each(e.split(/\s/),function(t,e){a[e]=r[e],delete r[e],"required"===e&&l(o).removeAttr("aria-required")}),a):(delete s[o.name],r)}return(t=l.validator.normalizeRules(l.extend({},l.validator.classRules(o),l.validator.attributeRules(o),l.validator.dataRules(o),l.validator.staticRules(o)),o)).required&&(n=t.required,delete t.required,t=l.extend({required:n},t),l(o).attr("aria-required","true")),t.remote&&(n=t.remote,delete t.remote,t=l.extend(t,{remote:n})),t}}}),l.extend(l.expr[":"],{blank:function(t){return!l.trim(""+l(t).val())},filled:function(t){t=l(t).val();return null!==t&&!!l.trim(""+t)},unchecked:function(t){return!l(t).prop("checked")}}),l.validator=function(t,e){this.settings=l.extend(!0,{},l.validator.defaults,t),this.currentForm=e,this.init()},l.validator.format=function(i,t){return 1===arguments.length?function(){var t=l.makeArray(arguments);return t.unshift(i),l.validator.format.apply(this,t)}:(void 0!==t&&((t=2<arguments.length&&t.constructor!==Array?l.makeArray(arguments).slice(1):t).constructor!==Array&&(t=[t]),l.each(t,function(t,e){i=i.replace(new RegExp("\\{"+t+"\\}","g"),function(){return e})})),i)},l.extend(l.validator,{defaults:{messages:{},groups:{},rules:{},errorClass:"error",pendingClass:"pending",validClass:"valid",errorElement:"label",focusCleanup:!1,focusInvalid:!0,errorContainer:l([]),errorLabelContainer:l([]),onsubmit:!0,ignore:":hidden",ignoreTitle:!1,onfocusin:function(t){this.lastActive=t,this.settings.focusCleanup&&(this.settings.unhighlight&&this.settings.unhighlight.call(this,t,this.settings.errorClass,this.settings.validClass),this.hideThese(this.errorsFor(t)))},onfocusout:function(t){this.checkable(t)||!(t.name in this.submitted)&&this.optional(t)||this.element(t)},onkeyup:function(t,e){9===e.which&&""===this.elementValue(t)||-1!==l.inArray(e.keyCode,[16,17,18,20,35,36,37,38,39,40,45,144,225])||(t.name in this.submitted||t.name in this.invalid)&&this.element(t)},onclick:function(t){t.name in this.submitted?this.element(t):t.parentNode.name in this.submitted&&this.element(t.parentNode)},highlight:function(t,e,i){("radio"===t.type?this.findByName(t.name):l(t)).addClass(e).removeClass(i)},unhighlight:function(t,e,i){("radio"===t.type?this.findByName(t.name):l(t)).removeClass(e).addClass(i)}},setDefaults:function(t){l.extend(l.validator.defaults,t)},messages:{required:"This field is required.",remote:"Please fix this field.",email:"Please enter a valid email address.",url:"Please enter a valid URL.",date:"Please enter a valid date.",dateISO:"Please enter a valid date ( ISO ).",number:"Please enter a valid number.",digits:"Please enter only digits.",equalTo:"Please enter the same value again.",maxlength:l.validator.format("Please enter no more than {0} characters."),minlength:l.validator.format("Please enter at least {0} characters."),rangelength:l.validator.format("Please enter a value between {0} and {1} characters long."),range:l.validator.format("Please enter a value between {0} and {1}."),max:l.validator.format("Please enter a value less than or equal to {0}."),min:l.validator.format("Please enter a value greater than or equal to {0}."),step:l.validator.format("Please enter a multiple of {0}.")},autoCreateRanges:!1,prototype:{init:function(){this.labelContainer=l(this.settings.errorLabelContainer),this.errorContext=this.labelContainer.length&&this.labelContainer||l(this.currentForm),this.containers=l(this.settings.errorContainer).add(this.settings.errorLabelContainer),this.submitted={},this.valueCache={},this.pendingRequest=0,this.pending={},this.invalid={},this.reset();var i,s=this.groups={};function t(t){var e=l.data(this.form,"validator"),i="on"+t.type.replace(/^validate/,""),s=e.settings;s[i]&&!l(this).is(s.ignore)&&s[i].call(e,this,t)}l.each(this.settings.groups,function(i,t){"string"==typeof t&&(t=t.split(/\s/)),l.each(t,function(t,e){s[e]=i})}),i=this.settings.rules,l.each(i,function(t,e){i[t]=l.validator.normalizeRule(e)}),l(this.currentForm).on("focusin.validate focusout.validate keyup.validate",":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], [type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], [type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], [type='radio'], [type='checkbox'], [contenteditable]",t).on("click.validate","select, option, [type='radio'], [type='checkbox']",t),this.settings.invalidHandler&&l(this.currentForm).on("invalid-form.validate",this.settings.invalidHandler),l(this.currentForm).find("[required], [data-rule-required], .required").attr("aria-required","true")},form:function(){return this.checkForm(),l.extend(this.submitted,this.errorMap),this.invalid=l.extend({},this.errorMap),this.valid()||l(this.currentForm).triggerHandler("invalid-form",[this]),this.showErrors(),this.valid()},checkForm:function(){this.prepareForm();for(var t=0,e=this.currentElements=this.elements();e[t];t++)this.check(e[t]);return this.valid()},element:function(t){var e,i,s=this.clean(t),r=this.validationTargetFor(s),n=this,a=!0;return void 0===r?delete this.invalid[s.name]:(this.prepareElement(r),this.currentElements=l(r),(i=this.groups[r.name])&&l.each(this.groups,function(t,e){e===i&&t!==r.name&&(s=n.validationTargetFor(n.clean(n.findByName(t))))&&s.name in n.invalid&&(n.currentElements.push(s),a=a&&n.check(s))}),e=!1!==this.check(r),a=a&&e,this.invalid[r.name]=!e,this.numberOfInvalids()||(this.toHide=this.toHide.add(this.containers)),this.showErrors(),l(t).attr("aria-invalid",!e)),a},showErrors:function(e){var i;e&&(l.extend((i=this).errorMap,e),this.errorList=l.map(this.errorMap,function(t,e){return{message:t,element:i.findByName(e)[0]}}),this.successList=l.grep(this.successList,function(t){return!(t.name in e)})),this.settings.showErrors?this.settings.showErrors.call(this,this.errorMap,this.errorList):this.defaultShowErrors()},resetForm:function(){l.fn.resetForm&&l(this.currentForm).resetForm(),this.invalid={},this.submitted={},this.prepareForm(),this.hideErrors();var t=this.elements().removeData("previousValue").removeAttr("aria-invalid");this.resetElements(t)},resetElements:function(t){var e;if(this.settings.unhighlight)for(e=0;t[e];e++)this.settings.unhighlight.call(this,t[e],this.settings.errorClass,""),this.findByName(t[e].name).removeClass(this.settings.validClass);else t.removeClass(this.settings.errorClass).removeClass(this.settings.validClass)},numberOfInvalids:function(){return this.objectLength(this.invalid)},objectLength:function(t){var e,i=0;for(e in t)t[e]&&i++;return i},hideErrors:function(){this.hideThese(this.toHide)},hideThese:function(t){t.not(this.containers).text(""),this.addWrapper(t).hide()},valid:function(){return 0===this.size()},size:function(){return this.errorList.length},focusInvalid:function(){if(this.settings.focusInvalid)try{l(this.findLastActive()||this.errorList.length&&this.errorList[0].element||[]).filter(":visible").focus().trigger("focusin")}catch(t){}},findLastActive:function(){var e=this.lastActive;return e&&1===l.grep(this.errorList,function(t){return t.element.name===e.name}).length&&e},elements:function(){var e=this,i={};return l(this.currentForm).find("input, select, textarea, [contenteditable]").not(":submit, :reset, :image, :disabled").not(this.settings.ignore).filter(function(){var t=this.name||l(this).attr("name");return!t&&e.settings.debug&&window.console&&console.error("%o has no name assigned",this),this.hasAttribute("contenteditable")&&(this.form=l(this).closest("form")[0]),!(t in i||!e.objectLength(l(this).rules()))&&(i[t]=!0)})},clean:function(t){return l(t)[0]},errors:function(){var t=this.settings.errorClass.split(" ").join(".");return l(this.settings.errorElement+"."+t,this.errorContext)},resetInternals:function(){this.successList=[],this.errorList=[],this.errorMap={},this.toShow=l([]),this.toHide=l([])},reset:function(){this.resetInternals(),this.currentElements=l([])},prepareForm:function(){this.reset(),this.toHide=this.errors().add(this.containers)},prepareElement:function(t){this.reset(),this.toHide=this.errorsFor(t)},elementValue:function(t){var e=l(t),i=t.type;return"radio"===i||"checkbox"===i?this.findByName(t.name).filter(":checked").val():"number"===i&&void 0!==t.validity?t.validity.badInput?"NaN":e.val():(t=t.hasAttribute("contenteditable")?e.text():e.val(),"file"===i?"C:\\fakepath\\"===t.substr(0,12)?t.substr(12):0<=(e=t.lastIndexOf("/"))||0<=(e=t.lastIndexOf("\\"))?t.substr(e+1):t:"string"==typeof t?t.replace(/\r/g,""):t)},check:function(e){e=this.validationTargetFor(this.clean(e));var t,i,s,r=l(e).rules(),n=l.map(r,function(t,e){return e}).length,a=!1,o=this.elementValue(e);if("function"==typeof r.normalizer){if("string"!=typeof(o=r.normalizer.call(e,o)))throw new TypeError("The normalizer should return a string value.");delete r.normalizer}for(i in r){s={method:i,parameters:r[i]};try{if("dependency-mismatch"===(t=l.validator.methods[i].call(this,o,e,s.parameters))&&1===n)a=!0;else{if(a=!1,"pending"===t)return void(this.toHide=this.toHide.not(this.errorsFor(e)));if(!t)return this.formatAndAdd(e,s),!1}}catch(t){throw this.settings.debug&&window.console&&console.log("Exception occurred when checking element "+e.id+", check the '"+s.method+"' method.",t),t instanceof TypeError&&(t.message+=".  Exception occurred when checking element "+e.id+", check the '"+s.method+"' method."),t}}if(!a)return this.objectLength(r)&&this.successList.push(e),!0},customDataMessage:function(t,e){return l(t).data("msg"+e.charAt(0).toUpperCase()+e.substring(1).toLowerCase())||l(t).data("msg")},customMessage:function(t,e){t=this.settings.messages[t];return t&&(t.constructor===String?t:t[e])},findDefined:function(){for(var t=0;t<arguments.length;t++)if(void 0!==arguments[t])return arguments[t]},defaultMessage:function(t,e){var i=this.findDefined(this.customMessage(t.name,e.method),this.customDataMessage(t,e.method),!this.settings.ignoreTitle&&t.title||void 0,l.validator.messages[e.method],"<strong>Warning: No message defined for "+t.name+"</strong>"),s=/\$?\{(\d+)\}/g;return"function"==typeof i?i=i.call(this,e.parameters,t):s.test(i)&&(i=l.validator.format(i.replace(s,"{$1}"),e.parameters)),i},formatAndAdd:function(t,e){var i=this.defaultMessage(t,e);this.errorList.push({message:i,element:t,method:e.method}),this.errorMap[t.name]=i,this.submitted[t.name]=i},addWrapper:function(t){return t=this.settings.wrapper?t.add(t.parent(this.settings.wrapper)):t},defaultShowErrors:function(){for(var t,e,i=0;this.errorList[i];i++)e=this.errorList[i],this.settings.highlight&&this.settings.highlight.call(this,e.element,this.settings.errorClass,this.settings.validClass),this.showLabel(e.element,e.message);if(this.errorList.length&&(this.toShow=this.toShow.add(this.containers)),this.settings.success)for(i=0;this.successList[i];i++)this.showLabel(this.successList[i]);if(this.settings.unhighlight)for(i=0,t=this.validElements();t[i];i++)this.settings.unhighlight.call(this,t[i],this.settings.errorClass,this.settings.validClass);this.toHide=this.toHide.not(this.toShow),this.hideErrors(),this.addWrapper(this.toShow).show()},validElements:function(){return this.currentElements.not(this.invalidElements())},invalidElements:function(){return l(this.errorList).map(function(){return this.element})},showLabel:function(t,e){var i,s,r,n=this.errorsFor(t),a=this.idOrName(t),o=l(t).attr("aria-describedby");n.length?(n.removeClass(this.settings.validClass).addClass(this.settings.errorClass),n.html(e)):(s=n=l("<"+this.settings.errorElement+">").attr("id",a+"-error").addClass(this.settings.errorClass).html(e||""),this.settings.wrapper&&(s=n.hide().show().wrap("<"+this.settings.wrapper+"/>").parent()),this.labelContainer.length?this.labelContainer.append(s):this.settings.errorPlacement?this.settings.errorPlacement(s,l(t)):s.insertAfter(t),n.is("label")?n.attr("for",a):0===n.parents("label[for='"+this.escapeCssMeta(a)+"']").length&&(s=n.attr("id"),o?o.match(new RegExp("\\b"+this.escapeCssMeta(s)+"\\b"))||(o+=" "+s):o=s,l(t).attr("aria-describedby",o),i=this.groups[t.name])&&l.each((r=this).groups,function(t,e){e===i&&l("[name='"+r.escapeCssMeta(t)+"']",r.currentForm).attr("aria-describedby",n.attr("id"))})),!e&&this.settings.success&&(n.text(""),"string"==typeof this.settings.success?n.addClass(this.settings.success):this.settings.success(n,t)),this.toShow=this.toShow.add(n)},errorsFor:function(t){var e=this.escapeCssMeta(this.idOrName(t)),t=l(t).attr("aria-describedby"),e="label[for='"+e+"'], label[for='"+e+"'] *";return t&&(e=e+", #"+this.escapeCssMeta(t).replace(/\s+/g,", #")),this.errors().filter(e)},escapeCssMeta:function(t){return t.replace(/([\\!"#$%&'()*+,./:;<=>?@\[\]^`{|}~])/g,"\\$1")},idOrName:function(t){return this.groups[t.name]||!this.checkable(t)&&t.id||t.name},validationTargetFor:function(t){return this.checkable(t)&&(t=this.findByName(t.name)),l(t).not(this.settings.ignore)[0]},checkable:function(t){return/radio|checkbox/i.test(t.type)},findByName:function(t){return l(this.currentForm).find("[name='"+this.escapeCssMeta(t)+"']")},getLength:function(t,e){switch(e.nodeName.toLowerCase()){case"select":return l("option:selected",e).length;case"input":if(this.checkable(e))return this.findByName(e.name).filter(":checked").length}return t.length},depend:function(t,e){return!this.dependTypes[typeof t]||this.dependTypes[typeof t](t,e)},dependTypes:{boolean:function(t){return t},string:function(t,e){return!!l(t,e.form).length},function:function(t,e){return t(e)}},optional:function(t){var e=this.elementValue(t);return!l.validator.methods.required.call(this,e,t)&&"dependency-mismatch"},startRequest:function(t){this.pending[t.name]||(this.pendingRequest++,l(t).addClass(this.settings.pendingClass),this.pending[t.name]=!0)},stopRequest:function(t,e){this.pendingRequest--,this.pendingRequest<0&&(this.pendingRequest=0),delete this.pending[t.name],l(t).removeClass(this.settings.pendingClass),e&&0===this.pendingRequest&&this.formSubmitted&&this.form()?(l(this.currentForm).submit(),this.formSubmitted=!1):!e&&0===this.pendingRequest&&this.formSubmitted&&(l(this.currentForm).triggerHandler("invalid-form",[this]),this.formSubmitted=!1)},previousValue:function(t,e){return l.data(t,"previousValue")||l.data(t,"previousValue",{old:null,valid:!0,message:this.defaultMessage(t,{method:e})})},destroy:function(){this.resetForm(),l(this.currentForm).off(".validate").removeData("validator").find(".validate-equalTo-blur").off(".validate-equalTo").removeClass("validate-equalTo-blur")}},classRuleSettings:{required:{required:!0},email:{email:!0},url:{url:!0},date:{date:!0},dateISO:{dateISO:!0},number:{number:!0},digits:{digits:!0},creditcard:{creditcard:!0}},addClassRules:function(t,e){t.constructor===String?this.classRuleSettings[t]=e:l.extend(this.classRuleSettings,t)},classRules:function(t){var e={},t=l(t).attr("class");return t&&l.each(t.split(" "),function(){this in l.validator.classRuleSettings&&l.extend(e,l.validator.classRuleSettings[this])}),e},normalizeAttributeRule:function(t,e,i,s){(s=/min|max|step/.test(i)&&(null===e||/number|range|text/.test(e))&&(s=Number(s),isNaN(s))?void 0:s)||0===s?t[i]=s:e===i&&"range"!==e&&(t[i]=!0)},attributeRules:function(t){var e,i,s={},r=l(t),n=t.getAttribute("type");for(e in l.validator.methods)i="required"===e?!!(i=""===(i=t.getAttribute(e))?!0:i):r.attr(e),this.normalizeAttributeRule(s,n,e,i);return s.maxlength&&/-1|2147483647|524288/.test(s.maxlength)&&delete s.maxlength,s},dataRules:function(t){var e,i,s={},r=l(t),n=t.getAttribute("type");for(e in l.validator.methods)i=r.data("rule"+e.charAt(0).toUpperCase()+e.substring(1).toLowerCase()),this.normalizeAttributeRule(s,n,e,i);return s},staticRules:function(t){var e={},i=l.data(t.form,"validator");return e=i.settings.rules?l.validator.normalizeRule(i.settings.rules[t.name])||{}:e},normalizeRules:function(s,r){return l.each(s,function(t,e){if(!1===e)delete s[t];else if(e.param||e.depends){var i=!0;switch(typeof e.depends){case"string":i=!!l(e.depends,r.form).length;break;case"function":i=e.depends.call(r,r)}i?s[t]=void 0===e.param||e.param:(l.data(r.form,"validator").resetElements(l(r)),delete s[t])}}),l.each(s,function(t,e){s[t]=l.isFunction(e)&&"normalizer"!==t?e(r):e}),l.each(["minlength","maxlength"],function(){s[this]&&(s[this]=Number(s[this]))}),l.each(["rangelength","range"],function(){var t;s[this]&&(l.isArray(s[this])?s[this]=[Number(s[this][0]),Number(s[this][1])]:"string"==typeof s[this]&&(t=s[this].replace(/[\[\]]/g,"").split(/[\s,]+/),s[this]=[Number(t[0]),Number(t[1])]))}),l.validator.autoCreateRanges&&(null!=s.min&&null!=s.max&&(s.range=[s.min,s.max],delete s.min,delete s.max),null!=s.minlength)&&null!=s.maxlength&&(s.rangelength=[s.minlength,s.maxlength],delete s.minlength,delete s.maxlength),s},normalizeRule:function(t){var e;return"string"==typeof t&&(e={},l.each(t.split(/\s/),function(){e[this]=!0}),t=e),t},addMethod:function(t,e,i){l.validator.methods[t]=e,l.validator.messages[t]=void 0!==i?i:l.validator.messages[t],e.length<3&&l.validator.addClassRules(t,l.validator.normalizeRule(t))},methods:{required:function(t,e,i){return this.depend(i,e)?"select"===e.nodeName.toLowerCase()?(i=l(e).val())&&0<i.length:this.checkable(e)?0<this.getLength(t,e):0<t.length:"dependency-mismatch"},email:function(t,e){return this.optional(e)||/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)},url:function(t,e){return this.optional(e)||/^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(t)},date:function(t,e){return this.optional(e)||!/Invalid|NaN/.test(new Date(t).toString())},dateISO:function(t,e){return this.optional(e)||/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(t)},number:function(t,e){return this.optional(e)||/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(t)},digits:function(t,e){return this.optional(e)||/^\d+$/.test(t)},minlength:function(t,e,i){t=l.isArray(t)?t.length:this.getLength(t,e);return this.optional(e)||i<=t},maxlength:function(t,e,i){t=l.isArray(t)?t.length:this.getLength(t,e);return this.optional(e)||t<=i},rangelength:function(t,e,i){t=l.isArray(t)?t.length:this.getLength(t,e);return this.optional(e)||t>=i[0]&&t<=i[1]},min:function(t,e,i){return this.optional(e)||i<=t},max:function(t,e,i){return this.optional(e)||t<=i},range:function(t,e,i){return this.optional(e)||t>=i[0]&&t<=i[1]},step:function(t,e,i){var s=l(e).attr("type"),r="Step attribute on input type "+s+" is not supported.",n=new RegExp("\\b"+s+"\\b");if(s&&!n.test(["text","number","range"].join()))throw new Error(r);return this.optional(e)||t%i==0},equalTo:function(t,e,i){i=l(i);return this.settings.onfocusout&&i.not(".validate-equalTo-blur").length&&i.addClass("validate-equalTo-blur").on("blur.validate-equalTo",function(){l(e).valid()}),t===i.val()},remote:function(s,r,t,n){if(this.optional(r))return"dependency-mismatch";n="string"==typeof n&&n||"remote";var a,e,o=this.previousValue(r,n);return this.settings.messages[r.name]||(this.settings.messages[r.name]={}),o.originalMessage=o.originalMessage||this.settings.messages[r.name][n],this.settings.messages[r.name][n]=o.message,e=l.param(l.extend({data:s},(t="string"==typeof t?{url:t}:t).data)),o.old===e?o.valid:(o.old=e,(a=this).startRequest(r),(e={})[r.name]=s,l.ajax(l.extend(!0,{mode:"abort",port:"validate"+r.name,dataType:"json",data:e,context:a.currentForm,success:function(t){var e,i=!0===t||"true"===t;a.settings.messages[r.name][n]=o.originalMessage,i?(e=a.formSubmitted,a.resetInternals(),a.toHide=a.errorsFor(r),a.formSubmitted=e,a.successList.push(r),a.invalid[r.name]=!1,a.showErrors()):(e={},t=t||a.defaultMessage(r,{method:n,parameters:s}),e[r.name]=o.message=t,a.invalid[r.name]=!0,a.showErrors(e)),o.valid=i,a.stopRequest(r,i)}},t)),"pending")}}});var s,r={};l.ajaxPrefilter?l.ajaxPrefilter(function(t,e,i){var s=t.port;"abort"===t.mode&&(r[s]&&r[s].abort(),r[s]=i)}):(s=l.ajax,l.ajax=function(t){var e=("mode"in t?t:l.ajaxSettings).mode,i=("port"in t?t:l.ajaxSettings).port;return"abort"===e?(r[i]&&r[i].abort(),r[i]=s.apply(this,arguments),r[i]):s.apply(this,arguments)})});
//# sourceMappingURL=jquery.validate.min.js.map
