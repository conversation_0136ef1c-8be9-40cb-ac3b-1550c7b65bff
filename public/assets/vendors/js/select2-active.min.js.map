{"version": 3, "file": "select2-active.min.js", "sources": ["select2-active.min.js"], "sourcesContent": ["/*\r\n<--!----------------------------------------------------------------!-->\r\n<--! Select2 Active JS !-->\r\n<--!----------------------------------------------------------------!-->\r\n*/\r\n//icon format\r\nfunction iformat(icon) {\r\n\tvar originalOption = icon.element;\r\n\treturn $('<span class=\"hstack gap-3\"><i class=\" ' + $(originalOption).data(\"icon\") + '\"></i> ' + icon.text + \"</span>\");\r\n}\r\n//bg format\r\nfunction bgformat(bg) {\r\n\tvar originalOption = bg.element;\r\n\treturn $('<span class=\"hstack gap-2\"> <span class=\"wd-7 ht-7 rounded-circle ' + $(originalOption).data(\"bg\") + '\"></span> ' + bg.text + \"</span>\");\r\n}\r\n//storage format\r\nfunction storageformat(storage) {\r\n\tvar originalOption = storage.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <img src=\"' + \"assets/images/storage-icons\" + \"/\" + $(originalOption).data(\"storage\") + '.png\" class=\"avatar-image avatar-sm\" /> ' + storage.text + \"</span>\");\r\n}\r\n//user format\r\nfunction userformat(user) {\r\n\tvar originalOption = user.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <img src=\"' + \"assets/images/avatar\" + \"/\" + $(originalOption).data(\"user\") + '.png\" class=\"avatar-image avatar-sm\" /> ' + user.text + \"</span>\");\r\n}\r\n//payment format\r\nfunction paymentformat(payment) {\r\n\tvar originalOption = payment.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <img src=\"' + \"assets/images/payment\" + \"/\" + $(originalOption).data(\"payment\") + '.svg\" class=\"avatar-image avatar-sm\" /> ' + payment.text + \"</span>\");\r\n}\r\n//flag format\r\nfunction flagformat(flag) {\r\n\tvar originalOption = flag.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <img src=\"' + \"assets/vendors/img/flags/1x1\" + \"/\" + $(originalOption).data(\"flag\") + '.svg\" class=\"avatar-image avatar-sm\" /> ' + flag.text + \"</span>\");\r\n}\r\n//country format\r\nfunction countryformat(country) {\r\n\tvar originalOption = country.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <img src=\"' + \"assets/vendors/img/flags/1x1\" + \"/\" + $(originalOption).data(\"country\") + '.svg\" class=\"avatar-image avatar-sm\" /> ' + country.text + \"</span>\");\r\n}\r\n//tzone format\r\nfunction tzoneformat(tzone) {\r\n\tvar originalOption = tzone.element;\r\n\treturn $('<span class=\"hstack gap-3 text-truncate-1-line\"> <i class=\"me-2 ' + $(originalOption).data(\"tzone\") + '\"></i> ' + tzone.text + \"</span>\");\r\n}\r\n//state format\r\nfunction stateformat(state) {\r\n\tvar originalOption = state.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <img src=\"' + \"assets/vendors/img/flags/us\" + \"/\" + $(originalOption).data(\"state\") + '.png\" class=\"avatar-image avatar-sm\" /> ' + state.text + \"</span>\");\r\n}\r\n//city format\r\nfunction cityformat(city) {\r\n\tvar originalOption = city.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <span class=\"wd-7 ht-7 rounded-circle ' + $(originalOption).data(\"city\") + '\"></span> ' + city.text + \"</span>\");\r\n}\r\n//language format\r\nfunction languageformat(language) {\r\n\tvar originalOption = language.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <span class=\"wd-7 ht-7 rounded-circle ' + $(originalOption).data(\"language\") + '\"></span> ' + language.text + \"</span>\");\r\n}\r\n//currency format\r\nfunction currencyformat(currency) {\r\n\tvar originalOption = currency.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <img src=\"' + \"assets/vendors/img/flags/1x1\" + \"/\" + $(originalOption).data(\"currency\") + '.svg\" class=\"avatar-image avatar-sm\" /> ' + currency.text + \"</span>\");\r\n}\r\n//programming format\r\nfunction programmingformat(programming) {\r\n\tvar originalOption = programming.element;\r\n\treturn $('<span class=\"hstack gap-3\"> <i class=\"fa-brand ' + $(originalOption).data(\"programming\") + '\"></i> ' + programming.text + \"</span>\");\r\n}\r\n// Default\r\n$(\"[data-select2-selector='default']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n});\r\n// Icon + Visibility + Privacy\r\n$(\"[data-select2-selector='icon'], [data-select2-selector='visibility'], [data-select2-selector='privacy'\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: iformat,\r\n\ttemplateSelection: iformat,\r\n});\r\n// Storage\r\n$(\"[data-select2-selector='storage']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: storageformat,\r\n\ttemplateSelection: storageformat,\r\n});\r\n// Tag\r\n$(\"[data-select2-selector='tag'], [data-select2-selector='status'], [data-select2-selector='priority'], [data-select2-selector='label'], [data-select2-selector='type']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: bgformat,\r\n\ttemplateSelection: bgformat,\r\n});\r\n// User\r\n$(\"[data-select2-selector='user']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: userformat,\r\n\ttemplateSelection: userformat,\r\n});\r\n// Payment\r\n$(\"[data-select2-selector='payment']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: paymentformat,\r\n\ttemplateSelection: paymentformat,\r\n});\r\n// Flag\r\n$(\"[data-select2-selector='flag']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: flagformat,\r\n\ttemplateSelection: flagformat,\r\n});\r\n// Country\r\n$(\"[data-select2-selector='country']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: countryformat,\r\n\ttemplateSelection: countryformat,\r\n});\r\n// Time Zone\r\n$(\"[data-select2-selector='tzone']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: tzoneformat,\r\n\ttemplateSelection: tzoneformat,\r\n});\r\n// State\r\n$(\"[data-select2-selector='state']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: stateformat,\r\n\ttemplateSelection: stateformat,\r\n});\r\n// City\r\n$(\"[data-select2-selector='city']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: cityformat,\r\n\ttemplateSelection: cityformat,\r\n});\r\n// Language\r\n$(\"[data-select2-selector='language']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: languageformat,\r\n\ttemplateSelection: languageformat,\r\n});\r\n// Currency\r\n$(\"[data-select2-selector='currency']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: currencyformat,\r\n\ttemplateSelection: currencyformat,\r\n});\r\n// Programming\r\n$(\"[data-select2-selector='programming']\").select2({\r\n\ttheme: \"bootstrap-5\",\r\n\ttemplateResult: programmingformat,\r\n\ttemplateSelection: programmingformat,\r\n});\r\n"], "names": ["iformat", "icon", "originalOption", "element", "$", "data", "text", "bgformat", "bg", "storageformat", "storage", "userformat", "user", "paymentformat", "payment", "flagformat", "flag", "countryformat", "country", "tzoneformat", "tzone", "stateformat", "state", "cityformat", "city", "languageformat", "language", "currencyformat", "currency", "programmingformat", "programming", "select2", "theme", "templateResult", "templateSelection"], "mappings": "AAMA,SAASA,QAAQC,GAChB,IAAIC,EAAiBD,EAAKE,QAC1B,OAAOC,EAAE,yCAA2CA,EAAEF,CAAc,EAAEG,KAAK,MAAM,EAAI,UAAYJ,EAAKK,KAAO,SAAS,CACvH,CAEA,SAASC,SAASC,GACjB,IAAIN,EAAiBM,EAAGL,QACxB,OAAOC,EAAE,qEAAuEA,EAAEF,CAAc,EAAEG,KAAK,IAAI,EAAI,aAAeG,EAAGF,KAAO,SAAS,CAClJ,CAEA,SAASG,cAAcC,GACtB,IAAIR,EAAiBQ,EAAQP,QAC7B,OAAOC,EAAE,0EAAsFA,EAAEF,CAAc,EAAEG,KAAK,SAAS,EAAI,2CAA6CK,EAAQJ,KAAO,SAAS,CACzM,CAEA,SAASK,WAAWC,GACnB,IAAIV,EAAiBU,EAAKT,QAC1B,OAAOC,EAAE,mEAA+EA,EAAEF,CAAc,EAAEG,KAAK,MAAM,EAAI,2CAA6CO,EAAKN,KAAO,SAAS,CAC5L,CAEA,SAASO,cAAcC,GACtB,IAAIZ,EAAiBY,EAAQX,QAC7B,OAAOC,EAAE,oEAAgFA,EAAEF,CAAc,EAAEG,KAAK,SAAS,EAAI,2CAA6CS,EAAQR,KAAO,SAAS,CACnM,CAEA,SAASS,WAAWC,GACnB,IAAId,EAAiBc,EAAKb,QAC1B,OAAOC,EAAE,2EAAuFA,EAAEF,CAAc,EAAEG,KAAK,MAAM,EAAI,2CAA6CW,EAAKV,KAAO,SAAS,CACpM,CAEA,SAASW,cAAcC,GACtB,IAAIhB,EAAiBgB,EAAQf,QAC7B,OAAOC,EAAE,2EAAuFA,EAAEF,CAAc,EAAEG,KAAK,SAAS,EAAI,2CAA6Ca,EAAQZ,KAAO,SAAS,CAC1M,CAEA,SAASa,YAAYC,GACpB,IAAIlB,EAAiBkB,EAAMjB,QAC3B,OAAOC,EAAE,mEAAqEA,EAAEF,CAAc,EAAEG,KAAK,OAAO,EAAI,UAAYe,EAAMd,KAAO,SAAS,CACnJ,CAEA,SAASe,YAAYC,GACpB,IAAIpB,EAAiBoB,EAAMnB,QAC3B,OAAOC,EAAE,0EAAsFA,EAAEF,CAAc,EAAEG,KAAK,OAAO,EAAI,2CAA6CiB,EAAMhB,KAAO,SAAS,CACrM,CAEA,SAASiB,WAAWC,GACnB,IAAItB,EAAiBsB,EAAKrB,QAC1B,OAAOC,EAAE,qEAAuEA,EAAEF,CAAc,EAAEG,KAAK,MAAM,EAAI,aAAemB,EAAKlB,KAAO,SAAS,CACtJ,CAEA,SAASmB,eAAeC,GACvB,IAAIxB,EAAiBwB,EAASvB,QAC9B,OAAOC,EAAE,qEAAuEA,EAAEF,CAAc,EAAEG,KAAK,UAAU,EAAI,aAAeqB,EAASpB,KAAO,SAAS,CAC9J,CAEA,SAASqB,eAAeC,GACvB,IAAI1B,EAAiB0B,EAASzB,QAC9B,OAAOC,EAAE,2EAAuFA,EAAEF,CAAc,EAAEG,KAAK,UAAU,EAAI,2CAA6CuB,EAAStB,KAAO,SAAS,CAC5M,CAEA,SAASuB,kBAAkBC,GAC1B,IAAI5B,EAAiB4B,EAAY3B,QACjC,OAAOC,EAAE,kDAAoDA,EAAEF,CAAc,EAAEG,KAAK,aAAa,EAAI,UAAYyB,EAAYxB,KAAO,SAAS,CAC9I,CAEAF,EAAE,mCAAmC,EAAE2B,QAAQ,CAC9CC,MAAO,aACR,CAAC,EAED5B,EAAE,wGAAwG,EAAE2B,QAAQ,CACnHC,MAAO,cACPC,eAAgBjC,QAChBkC,kBAAmBlC,OACpB,CAAC,EAEDI,EAAE,mCAAmC,EAAE2B,QAAQ,CAC9CC,MAAO,cACPC,eAAgBxB,cAChByB,kBAAmBzB,aACpB,CAAC,EAEDL,EAAE,sKAAsK,EAAE2B,QAAQ,CACjLC,MAAO,cACPC,eAAgB1B,SAChB2B,kBAAmB3B,QACpB,CAAC,EAEDH,EAAE,gCAAgC,EAAE2B,QAAQ,CAC3CC,MAAO,cACPC,eAAgBtB,WAChBuB,kBAAmBvB,UACpB,CAAC,EAEDP,EAAE,mCAAmC,EAAE2B,QAAQ,CAC9CC,MAAO,cACPC,eAAgBpB,cAChBqB,kBAAmBrB,aACpB,CAAC,EAEDT,EAAE,gCAAgC,EAAE2B,QAAQ,CAC3CC,MAAO,cACPC,eAAgBlB,WAChBmB,kBAAmBnB,UACpB,CAAC,EAEDX,EAAE,mCAAmC,EAAE2B,QAAQ,CAC9CC,MAAO,cACPC,eAAgBhB,cAChBiB,kBAAmBjB,aACpB,CAAC,EAEDb,EAAE,iCAAiC,EAAE2B,QAAQ,CAC5CC,MAAO,cACPC,eAAgBd,YAChBe,kBAAmBf,WACpB,CAAC,EAEDf,EAAE,iCAAiC,EAAE2B,QAAQ,CAC5CC,MAAO,cACPC,eAAgBZ,YAChBa,kBAAmBb,WACpB,CAAC,EAEDjB,EAAE,gCAAgC,EAAE2B,QAAQ,CAC3CC,MAAO,cACPC,eAAgBV,WAChBW,kBAAmBX,UACpB,CAAC,EAEDnB,EAAE,oCAAoC,EAAE2B,QAAQ,CAC/CC,MAAO,cACPC,eAAgBR,eAChBS,kBAAmBT,cACpB,CAAC,EAEDrB,EAAE,oCAAoC,EAAE2B,QAAQ,CAC/CC,MAAO,cACPC,eAAgBN,eAChBO,kBAAmBP,cACpB,CAAC,EAEDvB,EAAE,uCAAuC,EAAE2B,QAAQ,CAClDC,MAAO,cACPC,eAAgBJ,kBAChBK,kBAAmBL,iBACpB,CAAC"}