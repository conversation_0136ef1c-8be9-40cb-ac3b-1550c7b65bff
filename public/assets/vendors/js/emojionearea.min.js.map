{"version": 3, "file": "emojionearea.min.js", "sources": ["emojionearea.min.js"], "sourcesContent": ["(window = \"undefined\" != typeof global ? global : \"undefined\" != typeof self ? self : \"undefined\" != typeof window ? window : {}),\r\n\t(document = window.document || {}),\r\n\t(function (e, a) {\r\n\t\t\"function\" == typeof require && \"object\" == typeof exports && \"object\" == typeof module ? e(require(\"jquery\")) : \"function\" == typeof define && define.amd ? define([\"jquery\"], e) : e(a.jQuery);\r\n\t})(function (y) {\r\n\t\t\"use strict\";\r\n\t\tvar n = 0,\r\n\t\t\tr = {},\r\n\t\t\tl = {},\r\n\t\t\tv = window.emojione,\r\n\t\t\tt = [];\r\n\t\tfunction i(e) {\r\n\t\t\tv ? e() : t.push(e);\r\n\t\t}\r\n\t\tvar j,\r\n\t\t\tx,\r\n\t\t\ts,\r\n\t\t\tC = \"data:image/gif;base64,R0lGODlhAQABAJH/AP///wAAAMDAwAAAACH5BAEAAAIALAAAAAABAAEAAAICVAEAOw==\",\r\n\t\t\t_ = [].slice,\r\n\t\t\tz = \"emojionearea\",\r\n\t\t\tT = 0,\r\n\t\t\tA = \"&#8203;\";\r\n\t\tfunction q(o, e, n) {\r\n\t\t\tvar t = !0,\r\n\t\t\t\ta = 1;\r\n\t\t\tif (e) {\r\n\t\t\t\te = e.toLowerCase();\r\n\t\t\t\tdo {\r\n\t\t\t\t\tvar i = 1 == a ? \"@\" + e : e;\r\n\t\t\t\t\tr[o.id][i] &&\r\n\t\t\t\t\t\tr[o.id][i].length &&\r\n\t\t\t\t\t\ty.each(r[o.id][i], function (e, a) {\r\n\t\t\t\t\t\t\treturn (t = !1 !== a.apply(o, n || []));\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} while (t && a--);\r\n\t\t\t}\r\n\t\t\treturn t;\r\n\t\t}\r\n\t\tfunction S(o, n, t, i) {\r\n\t\t\t(i =\r\n\t\t\t\ti ||\r\n\t\t\t\tfunction (e, a) {\r\n\t\t\t\t\treturn y(a.currentTarget);\r\n\t\t\t\t}),\r\n\t\t\t\ty.each(t, function (e, a) {\r\n\t\t\t\t\t(e = y.isArray(t) ? a : e), (l[o.id][a] || (l[o.id][a] = [])).push([n, e, i]);\r\n\t\t\t\t});\r\n\t\t}\r\n\t\tfunction c(e, a, o) {\r\n\t\t\tvar n = v.imageType,\r\n\t\t\t\tt = \"svg\" == n ? v.imagePathSVG : v.imagePathPNG,\r\n\t\t\t\ti = \"\";\r\n\t\t\to &&\r\n\t\t\t\t(i = o\r\n\t\t\t\t\t.substr(1, o.length - 2)\r\n\t\t\t\t\t.replace(/_/g, \" \")\r\n\t\t\t\t\t.replace(/\\w\\S*/g, function (e) {\r\n\t\t\t\t\t\treturn e.charAt(0).toUpperCase() + e.substr(1).toLowerCase();\r\n\t\t\t\t\t}));\r\n\t\t\tvar r = \"\";\r\n\t\t\treturn (\r\n\t\t\t\ta.uc_base && 4 < T ? ((r = a.uc_base), (a = a.uc_output.toUpperCase())) : (r = a),\r\n\t\t\t\te\r\n\t\t\t\t\t.replace(\"{name}\", o || \"\")\r\n\t\t\t\t\t.replace(\"{friendlyName}\", i)\r\n\t\t\t\t\t.replace(\"{img}\", t + (T < 2 ? r.toUpperCase() : r) + \".\" + n)\r\n\t\t\t\t\t.replace(\"{uni}\", a)\r\n\t\t\t\t\t.replace(\"{alt}\", v.convert(a))\r\n\t\t\t);\r\n\t\t}\r\n\t\tfunction P(e, o, n) {\r\n\t\t\treturn e.replace(/:?\\+?[\\w_\\-]+:?/g, function (e) {\r\n\t\t\t\te = \":\" + e.replace(/:$/, \"\").replace(/^:/, \"\") + \":\";\r\n\t\t\t\tvar a = v.emojioneList[e];\r\n\t\t\t\treturn a ? (4 < T ? c(o, a, e) : (3 < T && (a = a.unicode), c(o, a[a.length - 1], e))) : n ? \"\" : e;\r\n\t\t\t});\r\n\t\t}\r\n\t\tfunction E(e) {\r\n\t\t\tvar a, o;\r\n\t\t\tif (window.getSelection) {\r\n\t\t\t\tif ((a = window.getSelection()).getRangeAt && a.rangeCount) {\r\n\t\t\t\t\t(o = a.getRangeAt(0)).deleteContents();\r\n\t\t\t\t\tvar n = document.createElement(\"div\");\r\n\t\t\t\t\tn.innerHTML = e;\r\n\t\t\t\t\tfor (var t, i, r = document.createDocumentFragment(); (t = n.firstChild); ) i = r.appendChild(t);\r\n\t\t\t\t\to.insertNode(r), i && ((o = o.cloneRange()).setStartAfter(i), o.collapse(!0), a.removeAllRanges(), a.addRange(o));\r\n\t\t\t\t}\r\n\t\t\t} else document.selection && \"Control\" != document.selection.type && document.selection.createRange().pasteHTML(e);\r\n\t\t}\r\n\t\tfunction m() {\r\n\t\t\treturn window.emojioneVersion || \"3.1.2\";\r\n\t\t}\r\n\t\tfunction R(e) {\r\n\t\t\treturn \"object\" == typeof e;\r\n\t\t}\r\n\t\tfunction g(e) {\r\n\t\t\tvar a;\r\n\t\t\treturn e.cacheBustParam ? ((a = e.cacheBustParam), R(e.jsEscapeMap) ? (\"?v=1.2.4\" === a ? \"2.0.0\" : \"?v=2.0.1\" === a ? \"2.1.0\" : \"?v=2.1.1\" === a ? \"2.1.1\" : \"?v=2.1.2\" === a ? \"2.1.2\" : \"?v=2.1.3\" === a ? \"2.1.3\" : \"?v=2.1.4\" === a ? \"2.1.4\" : \"2.2.7\") : \"1.5.2\") : e.emojiVersion;\r\n\t\t}\r\n\t\tfunction u(e) {\r\n\t\t\tswitch (e) {\r\n\t\t\t\tcase \"1.5.2\":\r\n\t\t\t\t\treturn 0;\r\n\t\t\t\tcase \"2.0.0\":\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\tcase \"2.1.0\":\r\n\t\t\t\tcase \"2.1.1\":\r\n\t\t\t\t\treturn 2;\r\n\t\t\t\tcase \"2.1.2\":\r\n\t\t\t\t\treturn 3;\r\n\t\t\t\tcase \"2.1.3\":\r\n\t\t\t\tcase \"2.1.4\":\r\n\t\t\t\tcase \"2.2.7\":\r\n\t\t\t\t\treturn 4;\r\n\t\t\t\tcase \"3.0.1\":\r\n\t\t\t\tcase \"3.0.2\":\r\n\t\t\t\tcase \"3.0.3\":\r\n\t\t\t\tcase \"3.0\":\r\n\t\t\t\t\treturn 5;\r\n\t\t\t\tcase \"3.1.0\":\r\n\t\t\t\tcase \"3.1.1\":\r\n\t\t\t\tcase \"3.1.2\":\r\n\t\t\t\tcase \"3.1\":\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn 6;\r\n\t\t\t}\r\n\t\t}\r\n\t\tfunction o() {\r\n\t\t\tif (y.fn.emojioneArea && y.fn.emojioneArea.defaults) return y.fn.emojioneArea.defaults;\r\n\t\t\tvar e = { attributes: { dir: \"ltr\", spellcheck: !1, autocomplete: \"off\", autocorrect: \"off\", autocapitalize: \"off\" }, search: !0, placeholder: null, emojiPlaceholder: \":smiley:\", searchPlaceholder: \"SEARCH\", container: null, hideSource: !0, shortnames: !0, sprite: !0, pickerPosition: \"top\", filtersPosition: \"top\", searchPosition: \"top\", hidePickerOnBlur: !0, buttonTitle: \"Use the TAB key to insert emoji faster\", tones: !0, tonesStyle: \"bullet\", inline: null, saveEmojisAs: \"unicode\", shortcuts: !0, autocomplete: !0, autocompleteTones: !1, standalone: !1, useInternalCDN: !0, imageType: \"png\", recentEmojis: !0, textcomplete: { maxCount: 15, placement: null } },\r\n\t\t\t\ta = u(v ? g(v) : m());\r\n\t\t\treturn (e.filters = 4 < a ? { tones: { title: \"Diversity\", emoji: \"open_hands raised_hands palms_up_together clap pray thumbsup thumbsdown punch fist left_facing_fist right_facing_fist fingers_crossed v metal love_you_gesture ok_hand point_left point_right point_up_2 point_down point_up raised_hand raised_back_of_hand hand_splayed vulcan wave call_me muscle middle_finger writing_hand selfie nail_care ear nose baby boy girl man woman blond-haired_woman blond-haired_man older_man older_woman man_with_chinese_cap woman_wearing_turban man_wearing_turban woman_police_officer man_police_officer woman_construction_worker man_construction_worker woman_guard man_guard woman_detective man_detective woman_health_worker man_health_worker woman_farmer man_farmer woman_cook man_cook woman_student man_student woman_singer man_singer woman_teacher man_teacher woman_factory_worker man_factory_worker woman_technologist man_technologist woman_office_worker man_office_worker woman_mechanic man_mechanic woman_scientist man_scientist woman_artist man_artist woman_firefighter man_firefighter woman_pilot man_pilot woman_astronaut man_astronaut woman_judge man_judge mrs_claus santa princess prince bride_with_veil man_in_tuxedo angel pregnant_woman breast_feeding woman_bowing man_bowing man_tipping_hand woman_tipping_hand man_gesturing_no woman_gesturing_no man_gesturing_ok woman_gesturing_ok man_raising_hand woman_raising_hand woman_facepalming man_facepalming woman_shrugging man_shrugging man_pouting woman_pouting man_frowning woman_frowning man_getting_haircut woman_getting_haircut man_getting_face_massage woman_getting_face_massage man_in_business_suit_levitating dancer man_dancing woman_walking man_walking woman_running man_running adult child older_adult bearded_person woman_with_headscarf woman_mage man_mage woman_fairy man_fairy woman_vampire man_vampire mermaid merman woman_elf man_elf snowboarder woman_lifting_weights man_lifting_weights woman_cartwheeling man_cartwheeling woman_bouncing_ball man_bouncing_ball woman_playing_handball man_playing_handball woman_golfing man_golfing woman_surfing man_surfing woman_swimming man_swimming woman_playing_water_polo man_playing_water_polo woman_rowing_boat man_rowing_boat horse_racing woman_biking man_biking woman_mountain_biking man_mountain_biking woman_juggling man_juggling woman_in_steamy_room man_in_steamy_room woman_climbing man_climbing woman_in_lotus_position man_in_lotus_position bath person_in_bed\" }, recent: { icon: \"clock3\", title: \"Recent\", emoji: \"\" }, smileys_people: { icon: \"yum\", title: \"Smileys & People\", emoji: \"grinning smiley smile grin laughing sweat_smile joy rofl relaxed blush innocent slight_smile upside_down wink relieved crazy_face star_struck heart_eyes kissing_heart kissing kissing_smiling_eyes kissing_closed_eyes yum stuck_out_tongue_winking_eye stuck_out_tongue_closed_eyes stuck_out_tongue money_mouth hugging nerd sunglasses cowboy smirk unamused disappointed pensive worried face_with_raised_eyebrow face_with_monocle confused slight_frown frowning2 persevere confounded tired_face weary triumph angry rage face_with_symbols_over_mouth no_mouth neutral_face expressionless hushed frowning anguished open_mouth astonished dizzy_face exploding_head flushed scream fearful cold_sweat cry disappointed_relieved drooling_face sob sweat sleepy sleeping rolling_eyes thinking shushing_face face_with_hand_over_mouth lying_face grimacing zipper_mouth face_vomiting nauseated_face sneezing_face mask thermometer_face head_bandage smiling_imp imp japanese_ogre japanese_goblin poop ghost skull skull_crossbones alien space_invader robot jack_o_lantern clown smiley_cat smile_cat joy_cat heart_eyes_cat smirk_cat kissing_cat scream_cat crying_cat_face pouting_cat open_hands raised_hands palms_up_together clap pray handshake thumbsup thumbsdown punch fist left_facing_fist right_facing_fist fingers_crossed v metal love_you_gesture ok_hand point_left point_right point_up_2 point_down point_up raised_hand raised_back_of_hand hand_splayed vulcan wave call_me muscle middle_finger writing_hand selfie nail_care ring lipstick kiss lips tongue ear nose footprints eye eyes speaking_head bust_in_silhouette busts_in_silhouette baby boy girl man woman blond-haired_woman blond_haired_man older_man older_woman man_with_chinese_cap woman_wearing_turban man_wearing_turban woman_police_officer police_officer woman_construction_worker construction_worker woman_guard guard woman_detective detective woman_health_worker man_health_worker woman_farmer man_farmer woman_cook man_cook woman_student man_student woman_singer man_singer woman_teacher man_teacher woman_factory_worker man_factory_worker woman_technologist man_technologist woman_office_worker man_office_worker woman_mechanic man_mechanic woman_scientist man_scientist woman_artist man_artist woman_firefighter man_firefighter woman_pilot man_pilot woman_astronaut man_astronaut woman_judge man_judge mrs_claus santa princess prince bride_with_veil man_in_tuxedo angel pregnant_woman breast_feeding woman_bowing man_bowing woman_tipping_hand man_tipping_hand woman_gesturing_no man_gesturing_no woman_gesturing_ok man_gesturing_ok woman_raising_hand man_raising_hand woman_facepalming man_facepalming woman_shrugging man_shrugging woman_pouting man_pouting woman_frowning man_frowning woman_getting_haircut man_getting_haircut woman_getting_face_massage man_getting_face_massage man_in_business_suit_levitating dancer man_dancing women_with_bunny_ears_partying men_with_bunny_ears_partying woman_walking man_walking woman_running man_running couple two_women_holding_hands two_men_holding_hands couple_with_heart couple_ww couple_mm couplekiss kiss_ww kiss_mm family family_mwg family_mwgb family_mwbb family_mwgg family_wwb family_wwg family_wwgb family_wwbb family_wwgg family_mmb family_mmg family_mmgb family_mmbb family_mmgg family_woman_boy family_woman_girl family_woman_girl_boy family_woman_boy_boy family_woman_girl_girl family_man_boy family_man_girl family_man_girl_boy family_man_boy_boy family_man_girl_girl bearded_person woman_with_headscarf woman_mage man_mage woman_fairy man_fairy woman_vampire man_vampire mermaid merman woman_elf man_elf woman_genie man_genie woman_zombie man_zombie womans_clothes shirt jeans necktie dress bikini kimono high_heel sandal boot mans_shoe athletic_shoe womans_hat tophat mortar_board crown helmet_with_cross school_satchel pouch purse handbag briefcase eyeglasses dark_sunglasses closed_umbrella umbrella2 brain billed_cap scarf gloves coat socks \" }, animals_nature: { icon: \"hamster\", title: \"Animals & Nature\", emoji: \"dog cat mouse hamster rabbit fox bear panda_face koala tiger lion_face cow pig pig_nose frog monkey_face see_no_evil hear_no_evil speak_no_evil monkey chicken penguin bird baby_chick hatching_chick hatched_chick duck eagle owl bat wolf boar horse unicorn bee bug butterfly snail shell beetle ant spider spider_web turtle snake lizard scorpion crab squid octopus shrimp tropical_fish fish blowfish dolphin shark whale whale2 crocodile leopard tiger2 water_buffalo ox cow2 deer dromedary_camel camel elephant rhino gorilla racehorse pig2 goat ram sheep dog2 poodle cat2 rooster turkey dove rabbit2 mouse2 rat chipmunk dragon giraffe zebra hedgehog sauropod t_rex cricket dragon_face feet cactus christmas_tree evergreen_tree deciduous_tree palm_tree seedling herb shamrock four_leaf_clover bamboo tanabata_tree leaves fallen_leaf maple_leaf mushroom ear_of_rice bouquet tulip rose wilted_rose sunflower blossom cherry_blossom hibiscus earth_americas earth_africa earth_asia full_moon waning_gibbous_moon last_quarter_moon waning_crescent_moon new_moon waxing_crescent_moon first_quarter_moon waxing_gibbous_moon new_moon_with_face full_moon_with_face sun_with_face first_quarter_moon_with_face last_quarter_moon_with_face crescent_moon dizzy star star2 sparkles zap fire boom comet sunny white_sun_small_cloud partly_sunny white_sun_cloud white_sun_rain_cloud rainbow cloud cloud_rain thunder_cloud_rain cloud_lightning cloud_snow snowman2 snowman snowflake wind_blowing_face dash cloud_tornado fog ocean droplet sweat_drops umbrella \" }, food_drink: { icon: \"pizza\", title: \"Food & Drink\", emoji: \"green_apple apple pear tangerine lemon banana watermelon grapes strawberry melon cherries peach pineapple kiwi avocado tomato eggplant cucumber carrot corn hot_pepper potato sweet_potato chestnut peanuts honey_pot croissant bread french_bread cheese egg cooking bacon pancakes fried_shrimp poultry_leg meat_on_bone pizza hotdog hamburger fries stuffed_flatbread taco burrito salad shallow_pan_of_food spaghetti ramen stew fish_cake sushi bento curry rice_ball rice rice_cracker oden dango shaved_ice ice_cream icecream cake birthday custard lollipop candy chocolate_bar popcorn doughnut cookie milk baby_bottle coffee tea sake beer beers champagne_glass wine_glass tumbler_glass cocktail tropical_drink champagne spoon fork_and_knife fork_knife_plate dumpling fortune_cookie takeout_box chopsticks bowl_with_spoon cup_with_straw coconut broccoli pie pretzel cut_of_meat sandwich canned_food\" }, activity: { icon: \"basketball\", title: \"Activity\", emoji: \"soccer basketball football baseball tennis volleyball rugby_football 8ball ping_pong badminton goal hockey field_hockey cricket_game golf bow_and_arrow fishing_pole_and_fish boxing_glove martial_arts_uniform ice_skate ski skier snowboarder woman_lifting_weights man_lifting_weights person_fencing women_wrestling men_wrestling woman_cartwheeling man_cartwheeling woman_bouncing_ball man_bouncing_ball woman_playing_handball man_playing_handball woman_golfing man_golfing woman_surfing man_surfing woman_swimming man_swimming woman_playing_water_polo man_playing_water_polo woman_rowing_boat man_rowing_boat horse_racing woman_biking man_biking woman_mountain_biking man_mountain_biking woman_in_steamy_room man_in_steamy_room woman_climbing man_climbing woman_in_lotus_position man_in_lotus_position running_shirt_with_sash medal military_medal first_place second_place third_place trophy rosette reminder_ribbon ticket tickets circus_tent woman_juggling man_juggling performing_arts art clapper microphone headphones musical_score musical_keyboard drum saxophone trumpet guitar violin game_die dart bowling video_game slot_machine sled curling_stone \" }, travel_places: { icon: \"rocket\", title: \"Travel & Places\", emoji: \"red_car taxi blue_car bus trolleybus race_car police_car ambulance fire_engine minibus truck articulated_lorry tractor scooter bike motor_scooter motorcycle rotating_light oncoming_police_car oncoming_bus oncoming_automobile oncoming_taxi aerial_tramway mountain_cableway suspension_railway railway_car train mountain_railway monorail bullettrain_side bullettrain_front light_rail steam_locomotive train2 metro tram station helicopter airplane_small airplane airplane_departure airplane_arriving rocket satellite_orbital seat canoe sailboat motorboat speedboat cruise_ship ferry ship anchor construction fuelpump busstop vertical_traffic_light traffic_light map moyai statue_of_liberty fountain tokyo_tower european_castle japanese_castle stadium ferris_wheel roller_coaster carousel_horse beach_umbrella beach island mountain mountain_snow mount_fuji volcano desert camping tent railway_track motorway construction_site factory house house_with_garden homes house_abandoned office department_store post_office european_post_office hospital bank hotel convenience_store school love_hotel wedding classical_building church mosque synagogue kaaba shinto_shrine japan rice_scene park sunrise sunrise_over_mountains stars sparkler fireworks city_sunset city_dusk cityscape night_with_stars milky_way bridge_at_night foggy flying_saucer\" }, objects: { icon: \"bulb\", title: \"Objects\", emoji: \"watch iphone calling computer keyboard desktop printer mouse_three_button trackball joystick compression minidisc floppy_disk cd dvd vhs camera camera_with_flash video_camera movie_camera projector film_frames telephone_receiver telephone pager fax tv radio microphone2 level_slider control_knobs stopwatch timer alarm_clock clock hourglass hourglass_flowing_sand satellite battery electric_plug bulb flashlight candle wastebasket oil money_with_wings dollar yen euro pound moneybag credit_card gem scales wrench hammer hammer_pick tools pick nut_and_bolt gear chains gun bomb knife dagger crossed_swords shield smoking coffin urn amphora crystal_ball prayer_beads barber alembic telescope microscope hole pill syringe thermometer toilet potable_water shower bathtub bath bellhop key key2 door couch bed sleeping_accommodation frame_photo shopping_bags shopping_cart gift balloon flags ribbon confetti_ball tada dolls izakaya_lantern wind_chime envelope envelope_with_arrow incoming_envelope e-mail love_letter inbox_tray outbox_tray package label mailbox_closed mailbox mailbox_with_mail mailbox_with_no_mail postbox postal_horn scroll page_with_curl page_facing_up bookmark_tabs bar_chart chart_with_upwards_trend chart_with_downwards_trend notepad_spiral calendar_spiral calendar date card_index card_box ballot_box file_cabinet clipboard file_folder open_file_folder dividers newspaper2 newspaper notebook notebook_with_decorative_cover ledger closed_book green_book blue_book orange_book books book bookmark link paperclip paperclips triangular_ruler straight_ruler pushpin round_pushpin scissors pen_ballpoint pen_fountain black_nib paintbrush crayon pencil pencil2 mag mag_right lock_with_ink_pen closed_lock_with_key lock unlock\" }, symbols: { icon: \"heartpulse\", title: \"Symbols\", emoji: \"heart orange_heart yellow_heart green_heart blue_heart purple_heart black_heart broken_heart heart_exclamation two_hearts revolving_hearts heartbeat heartpulse sparkling_heart cupid gift_heart heart_decoration peace cross star_and_crescent om_symbol wheel_of_dharma star_of_david six_pointed_star menorah yin_yang orthodox_cross place_of_worship ophiuchus aries taurus gemini cancer leo virgo libra scorpius sagittarius capricorn aquarius pisces id atom accept radioactive biohazard mobile_phone_off vibration_mode u6709 u7121 u7533 u55b6 u6708 eight_pointed_black_star vs white_flower ideograph_advantage secret congratulations u5408 u6e80 u5272 u7981 a b ab cl o2 sos x o octagonal_sign no_entry name_badge no_entry_sign 100 anger hotsprings no_pedestrians do_not_litter no_bicycles non-potable_water underage no_mobile_phones no_smoking exclamation grey_exclamation question grey_question bangbang interrobang low_brightness high_brightness part_alternation_mark warning children_crossing trident fleur-de-lis beginner recycle white_check_mark u6307 chart sparkle eight_spoked_asterisk negative_squared_cross_mark globe_with_meridians diamond_shape_with_a_dot_inside m cyclone zzz atm wc wheelchair parking u7a7a sa passport_control customs baggage_claim left_luggage mens womens baby_symbol restroom put_litter_in_its_place cinema signal_strength koko symbols information_source abc abcd capital_abcd ng ok up cool new free zero one two three four five six seven eight nine keycap_ten 1234 hash asterisk arrow_forward pause_button play_pause stop_button record_button eject track_next track_previous fast_forward rewind arrow_double_up arrow_double_down arrow_backward arrow_up_small arrow_down_small arrow_right arrow_left arrow_up arrow_down arrow_upper_right arrow_lower_right arrow_lower_left arrow_upper_left arrow_up_down left_right_arrow arrow_right_hook leftwards_arrow_with_hook arrow_heading_up arrow_heading_down twisted_rightwards_arrows repeat repeat_one arrows_counterclockwise arrows_clockwise musical_note notes heavy_plus_sign heavy_minus_sign heavy_division_sign heavy_multiplication_x heavy_dollar_sign currency_exchange tm copyright registered wavy_dash curly_loop loop end back on top soon heavy_check_mark ballot_box_with_check radio_button white_circle black_circle red_circle blue_circle small_red_triangle small_red_triangle_down small_orange_diamond small_blue_diamond large_orange_diamond large_blue_diamond white_square_button black_square_button black_small_square white_small_square black_medium_small_square white_medium_small_square black_medium_square white_medium_square black_large_square white_large_square speaker mute sound loud_sound bell no_bell mega loudspeaker speech_left eye_in_speech_bubble speech_balloon thought_balloon anger_right spades clubs hearts diamonds black_joker flower_playing_cards mahjong clock1 clock2 clock3 clock4 clock5 clock6 clock7 clock8 clock9 clock10 clock11 clock12 clock130 clock230 clock330 clock430 clock530 clock630 clock730 clock830 clock930 clock1030 clock1130 clock1230\" }, flags: { icon: \"flag_gb\", title: \"Flags\", emoji: \"flag_white flag_black checkered_flag triangular_flag_on_post rainbow_flag flag_af flag_ax flag_al flag_dz flag_as flag_ad flag_ao flag_ai flag_aq flag_ag flag_ar flag_am flag_aw flag_au flag_at flag_az flag_bs flag_bh flag_bd flag_bb flag_by flag_be flag_bz flag_bj flag_bm flag_bt flag_bo flag_ba flag_bw flag_br flag_io flag_vg flag_bn flag_bg flag_bf flag_bi flag_kh flag_cm flag_ca flag_ic flag_cv flag_bq flag_ky flag_cf flag_td flag_cl flag_cn flag_cx flag_cc flag_co flag_km flag_cg flag_cd flag_ck flag_cr flag_ci flag_hr flag_cu flag_cw flag_cy flag_cz flag_dk flag_dj flag_dm flag_do flag_ec flag_eg flag_sv flag_gq flag_er flag_ee flag_et flag_eu flag_fk flag_fo flag_fj flag_fi flag_fr flag_gf flag_pf flag_tf flag_ga flag_gm flag_ge flag_de flag_gh flag_gi flag_gr flag_gl flag_gd flag_gp flag_gu flag_gt flag_gg flag_gn flag_gw flag_gy flag_ht flag_hn flag_hk flag_hu flag_is flag_in flag_id flag_ir flag_iq flag_ie flag_im flag_il flag_it flag_jm flag_jp crossed_flags flag_je flag_jo flag_kz flag_ke flag_ki flag_xk flag_kw flag_kg flag_la flag_lv flag_lb flag_ls flag_lr flag_ly flag_li flag_lt flag_lu flag_mo flag_mk flag_mg flag_mw flag_my flag_mv flag_ml flag_mt flag_mh flag_mq flag_mr flag_mu flag_yt flag_mx flag_fm flag_md flag_mc flag_mn flag_me flag_ms flag_ma flag_mz flag_mm flag_na flag_nr flag_np flag_nl flag_nc flag_nz flag_ni flag_ne flag_ng flag_nu flag_nf flag_kp flag_mp flag_no flag_om flag_pk flag_pw flag_ps flag_pa flag_pg flag_py flag_pe flag_ph flag_pn flag_pl flag_pt flag_pr flag_qa flag_re flag_ro flag_ru flag_rw flag_ws flag_sm flag_st flag_sa flag_sn flag_rs flag_sc flag_sl flag_sg flag_sx flag_sk flag_si flag_gs flag_sb flag_so flag_za flag_kr flag_ss flag_es flag_lk flag_bl flag_sh flag_kn flag_lc flag_pm flag_vc flag_sd flag_sr flag_sz flag_se flag_ch flag_sy flag_tw flag_tj flag_tz flag_th flag_tl flag_tg flag_tk flag_to flag_tt flag_tn flag_tr flag_tm flag_tc flag_tv flag_vi flag_ug flag_ua flag_ae flag_gb flag_us flag_uy flag_uz flag_vu flag_va flag_ve flag_vn flag_wf flag_eh flag_ye flag_zm flag_zw flag_ac flag_ta flag_bv flag_hm flag_sj flag_um flag_ea flag_cp flag_dg flag_mf united_nations england scotland wales\" } } : { tones: { title: \"Diversity\", emoji: \"santa runner surfer swimmer lifter ear nose point_up_2 point_down point_left point_right punch wave ok_hand thumbsup thumbsdown clap open_hands boy girl man woman cop bride_with_veil person_with_blond_hair man_with_gua_pi_mao man_with_turban older_man grandma baby construction_worker princess angel information_desk_person guardsman dancer nail_care massage haircut muscle spy hand_splayed middle_finger vulcan no_good ok_woman bow raising_hand raised_hands person_frowning person_with_pouting_face pray rowboat bicyclist mountain_bicyclist walking bath metal point_up basketball_player fist raised_hand v writing_hand\" }, recent: { icon: \"clock3\", title: \"Recent\", emoji: \"\" }, smileys_people: { icon: \"yum\", title: \"Smileys & People\", emoji: \"grinning grimacing grin joy smiley smile sweat_smile laughing innocent wink blush slight_smile upside_down relaxed yum relieved heart_eyes kissing_heart kissing kissing_smiling_eyes kissing_closed_eyes stuck_out_tongue_winking_eye stuck_out_tongue_closed_eyes stuck_out_tongue money_mouth nerd sunglasses hugging smirk no_mouth neutral_face expressionless unamused rolling_eyes thinking flushed disappointed worried angry rage pensive confused slight_frown frowning2 persevere confounded tired_face weary triumph open_mouth scream fearful cold_sweat hushed frowning anguished cry disappointed_relieved sleepy sweat sob dizzy_face astonished zipper_mouth mask thermometer_face head_bandage sleeping zzz poop smiling_imp imp japanese_ogre japanese_goblin skull ghost alien robot smiley_cat smile_cat joy_cat heart_eyes_cat smirk_cat kissing_cat scream_cat crying_cat_face pouting_cat raised_hands clap wave thumbsup thumbsdown punch fist v ok_hand raised_hand open_hands muscle pray point_up point_up_2 point_down point_left point_right middle_finger hand_splayed metal vulcan writing_hand nail_care lips tongue ear nose eye eyes bust_in_silhouette busts_in_silhouette speaking_head baby boy girl man woman person_with_blond_hair older_man older_woman man_with_gua_pi_mao man_with_turban cop construction_worker guardsman spy santa angel princess bride_with_veil walking runner dancer dancers couple two_men_holding_hands two_women_holding_hands bow information_desk_person no_good ok_woman raising_hand person_with_pouting_face person_frowning haircut massage couple_with_heart couple_ww couple_mm couplekiss kiss_ww kiss_mm family family_mwg family_mwgb family_mwbb family_mwgg family_wwb family_wwg family_wwgb family_wwbb family_wwgg family_mmb family_mmg family_mmgb family_mmbb family_mmgg womans_clothes shirt jeans necktie dress bikini kimono lipstick kiss footprints high_heel sandal boot mans_shoe athletic_shoe womans_hat tophat helmet_with_cross mortar_board crown school_satchel pouch purse handbag briefcase eyeglasses dark_sunglasses ring closed_umbrella\" }, animals_nature: { icon: \"hamster\", title: \"Animals & Nature\", emoji: \"dog cat mouse hamster rabbit bear panda_face koala tiger lion_face cow pig pig_nose frog octopus monkey_face see_no_evil hear_no_evil speak_no_evil monkey chicken penguin bird baby_chick hatching_chick hatched_chick wolf boar horse unicorn bee bug snail beetle ant spider scorpion crab snake turtle tropical_fish fish blowfish dolphin whale whale2 crocodile leopard tiger2 water_buffalo ox cow2 dromedary_camel camel elephant goat ram sheep racehorse pig2 rat mouse2 rooster turkey dove dog2 poodle cat2 rabbit2 chipmunk feet dragon dragon_face cactus christmas_tree evergreen_tree deciduous_tree palm_tree seedling herb shamrock four_leaf_clover bamboo tanabata_tree leaves fallen_leaf maple_leaf ear_of_rice hibiscus sunflower rose tulip blossom cherry_blossom bouquet mushroom chestnut jack_o_lantern shell spider_web earth_americas earth_africa earth_asia full_moon waning_gibbous_moon last_quarter_moon waning_crescent_moon new_moon waxing_crescent_moon first_quarter_moon waxing_gibbous_moon new_moon_with_face full_moon_with_face first_quarter_moon_with_face last_quarter_moon_with_face sun_with_face crescent_moon star star2 dizzy sparkles comet sunny white_sun_small_cloud partly_sunny white_sun_cloud white_sun_rain_cloud cloud cloud_rain thunder_cloud_rain cloud_lightning zap fire boom snowflake cloud_snow snowman2 snowman wind_blowing_face dash cloud_tornado fog umbrella2 umbrella droplet sweat_drops ocean\" }, food_drink: { icon: \"pizza\", title: \"Food & Drink\", emoji: \"green_apple apple pear tangerine lemon banana watermelon grapes strawberry melon cherries peach pineapple tomato eggplant hot_pepper corn sweet_potato honey_pot bread cheese poultry_leg meat_on_bone fried_shrimp egg hamburger fries hotdog pizza spaghetti taco burrito ramen stew fish_cake sushi bento curry rice_ball rice rice_cracker oden dango shaved_ice ice_cream icecream cake birthday custard candy lollipop chocolate_bar popcorn doughnut cookie beer beers wine_glass cocktail tropical_drink champagne sake tea coffee baby_bottle fork_and_knife fork_knife_plate\" }, activity: { icon: \"basketball\", title: \"Activity\", emoji: \"soccer basketball football baseball tennis volleyball rugby_football 8ball golf golfer ping_pong badminton hockey field_hockey cricket ski skier snowboarder ice_skate bow_and_arrow fishing_pole_and_fish rowboat swimmer surfer bath basketball_player lifter bicyclist mountain_bicyclist horse_racing levitate trophy running_shirt_with_sash medal military_medal reminder_ribbon rosette ticket tickets performing_arts art circus_tent microphone headphones musical_score musical_keyboard saxophone trumpet guitar violin clapper video_game space_invader dart game_die slot_machine bowling\" }, travel_places: { icon: \"rocket\", title: \"Travel & Places\", emoji: \"red_car taxi blue_car bus trolleybus race_car police_car ambulance fire_engine minibus truck articulated_lorry tractor motorcycle bike rotating_light oncoming_police_car oncoming_bus oncoming_automobile oncoming_taxi aerial_tramway mountain_cableway suspension_railway railway_car train monorail bullettrain_side bullettrain_front light_rail mountain_railway steam_locomotive train2 metro tram station helicopter airplane_small airplane airplane_departure airplane_arriving sailboat motorboat speedboat ferry cruise_ship rocket satellite_orbital seat anchor construction fuelpump busstop vertical_traffic_light traffic_light checkered_flag ship ferris_wheel roller_coaster carousel_horse construction_site foggy tokyo_tower factory fountain rice_scene mountain mountain_snow mount_fuji volcano japan camping tent park motorway railway_track sunrise sunrise_over_mountains desert beach island city_sunset city_dusk cityscape night_with_stars bridge_at_night milky_way stars sparkler fireworks rainbow homes european_castle japanese_castle stadium statue_of_liberty house house_with_garden house_abandoned office department_store post_office european_post_office hospital bank hotel convenience_store school love_hotel wedding classical_building church mosque synagogue kaaba shinto_shrine\" }, objects: { icon: \"bulb\", title: \"Objects\", emoji: \"watch iphone calling computer keyboard desktop printer mouse_three_button trackball joystick compression minidisc floppy_disk cd dvd vhs camera camera_with_flash video_camera movie_camera projector film_frames telephone_receiver telephone pager fax tv radio microphone2 level_slider control_knobs stopwatch timer alarm_clock clock hourglass_flowing_sand hourglass satellite battery electric_plug bulb flashlight candle wastebasket oil money_with_wings dollar yen euro pound moneybag credit_card gem scales wrench hammer hammer_pick tools pick nut_and_bolt gear chains gun bomb knife dagger crossed_swords shield smoking skull_crossbones coffin urn amphora crystal_ball prayer_beads barber alembic telescope microscope hole pill syringe thermometer label bookmark toilet shower bathtub key key2 couch sleeping_accommodation bed door bellhop frame_photo map beach_umbrella moyai shopping_bags balloon flags ribbon gift confetti_ball tada dolls wind_chime crossed_flags izakaya_lantern envelope envelope_with_arrow incoming_envelope e-mail love_letter postbox mailbox_closed mailbox mailbox_with_mail mailbox_with_no_mail package postal_horn inbox_tray outbox_tray scroll page_with_curl bookmark_tabs bar_chart chart_with_upwards_trend chart_with_downwards_trend page_facing_up date calendar calendar_spiral card_index card_box ballot_box file_cabinet clipboard notepad_spiral file_folder open_file_folder dividers newspaper2 newspaper notebook closed_book green_book blue_book orange_book notebook_with_decorative_cover ledger books book link paperclip paperclips scissors triangular_ruler straight_ruler pushpin round_pushpin triangular_flag_on_post flag_white flag_black closed_lock_with_key lock unlock lock_with_ink_pen pen_ballpoint pen_fountain black_nib pencil pencil2 crayon paintbrush mag mag_right\" }, symbols: { icon: \"heartpulse\", title: \"Symbols\", emoji: \"heart yellow_heart green_heart blue_heart purple_heart broken_heart heart_exclamation two_hearts revolving_hearts heartbeat heartpulse sparkling_heart cupid gift_heart heart_decoration peace cross star_and_crescent om_symbol wheel_of_dharma star_of_david six_pointed_star menorah yin_yang orthodox_cross place_of_worship ophiuchus aries taurus gemini cancer leo virgo libra scorpius sagittarius capricorn aquarius pisces id atom u7a7a u5272 radioactive biohazard mobile_phone_off vibration_mode u6709 u7121 u7533 u55b6 u6708 eight_pointed_black_star vs accept white_flower ideograph_advantage secret congratulations u5408 u6e80 u7981 a b ab cl o2 sos no_entry name_badge no_entry_sign x o anger hotsprings no_pedestrians do_not_litter no_bicycles non-potable_water underage no_mobile_phones exclamation grey_exclamation question grey_question bangbang interrobang 100 low_brightness high_brightness trident fleur-de-lis part_alternation_mark warning children_crossing beginner recycle u6307 chart sparkle eight_spoked_asterisk negative_squared_cross_mark white_check_mark diamond_shape_with_a_dot_inside cyclone loop globe_with_meridians m atm sa passport_control customs baggage_claim left_luggage wheelchair no_smoking wc parking potable_water mens womens baby_symbol restroom put_litter_in_its_place cinema signal_strength koko ng ok up cool new free zero one two three four five six seven eight nine ten 1234 arrow_forward pause_button play_pause stop_button record_button track_next track_previous fast_forward rewind twisted_rightwards_arrows repeat repeat_one arrow_backward arrow_up_small arrow_down_small arrow_double_up arrow_double_down arrow_right arrow_left arrow_up arrow_down arrow_upper_right arrow_lower_right arrow_lower_left arrow_upper_left arrow_up_down left_right_arrow arrows_counterclockwise arrow_right_hook leftwards_arrow_with_hook arrow_heading_up arrow_heading_down hash asterisk information_source abc abcd capital_abcd symbols musical_note notes wavy_dash curly_loop heavy_check_mark arrows_clockwise heavy_plus_sign heavy_minus_sign heavy_division_sign heavy_multiplication_x heavy_dollar_sign currency_exchange copyright registered tm end back on top soon ballot_box_with_check radio_button white_circle black_circle red_circle large_blue_circle small_orange_diamond small_blue_diamond large_orange_diamond large_blue_diamond small_red_triangle black_small_square white_small_square black_large_square white_large_square small_red_triangle_down black_medium_square white_medium_square black_medium_small_square white_medium_small_square black_square_button white_square_button speaker sound loud_sound mute mega loudspeaker bell no_bell black_joker mahjong spades clubs hearts diamonds flower_playing_cards thought_balloon anger_right speech_balloon clock1 clock2 clock3 clock4 clock5 clock6 clock7 clock8 clock9 clock10 clock11 clock12 clock130 clock230 clock330 clock430 clock530 clock630 clock730 clock830 clock930 clock1030 clock1130 clock1230 eye_in_speech_bubble\" }, flags: { icon: \"flag_gb\", title: \"Flags\", emoji: \"ac af al dz ad ao ai ag ar am aw au at az bs bh bd bb by be bz bj bm bt bo ba bw br bn bg bf bi cv kh cm ca ky cf td flag_cl cn co km cg flag_cd cr hr cu cy cz dk dj dm do ec eg sv gq er ee et fk fo fj fi fr pf ga gm ge de gh gi gr gl gd gu gt gn gw gy ht hn hk hu is in flag_id ir iq ie il it ci jm jp je jo kz ke ki xk kw kg la lv lb ls lr ly li lt lu mo mk mg mw my mv ml mt mh mr mu mx fm md mc mn me ms ma mz mm na nr np nl nc nz ni ne flag_ng nu kp no om pk pw ps pa pg py pe ph pl pt pr qa ro ru rw sh kn lc vc ws sm st flag_sa sn rs sc sl sg sk si sb so za kr es lk sd sr sz se ch sy tw tj tz th tl tg to tt tn tr flag_tm flag_tm ug ua ae gb us vi uy uz vu va ve vn wf eh ye zm zw re ax ta io bq cx cc gg im yt nf pn bl pm gs tk bv hm sj um ic ea cp dg as aq vg ck cw eu gf tf gp mq mp sx ss tc \" } }), e;\r\n\t\t}\r\n\t\tfunction F(e) {\r\n\t\t\tvar n,\r\n\t\t\t\ta = o();\r\n\t\t\treturn (\r\n\t\t\t\te &&\r\n\t\t\t\t\te.filters &&\r\n\t\t\t\t\t((n = a.filters),\r\n\t\t\t\t\ty.each(e.filters, function (o, e) {\r\n\t\t\t\t\t\treturn !R(e) || y.isEmptyObject(e)\r\n\t\t\t\t\t\t\t? void delete n[o]\r\n\t\t\t\t\t\t\t: void y.each(e, function (e, a) {\r\n\t\t\t\t\t\t\t\t\tn[o][e] = a;\r\n\t\t\t\t\t\t\t  });\r\n\t\t\t\t\t}),\r\n\t\t\t\t\t(e.filters = n)),\r\n\t\t\t\ty.extend({}, a, e)\r\n\t\t\t);\r\n\t\t}\r\n\t\tfunction d(e, o) {\r\n\t\t\treturn e.replace(s, function (e) {\r\n\t\t\t\tvar a = v[0 === T ? \"jsecapeMap\" : \"jsEscapeMap\"];\r\n\t\t\t\treturn void 0 !== e && e in a ? c(o, a[e]) : e;\r\n\t\t\t});\r\n\t\t}\r\n\t\tfunction B(e, a) {\r\n\t\t\treturn (\r\n\t\t\t\t(e = e\r\n\t\t\t\t\t.replace(/&/g, \"&amp;\")\r\n\t\t\t\t\t.replace(/</g, \"&lt;\")\r\n\t\t\t\t\t.replace(/>/g, \"&gt;\")\r\n\t\t\t\t\t.replace(/\"/g, \"&quot;\")\r\n\t\t\t\t\t.replace(/'/g, \"&#x27;\")\r\n\t\t\t\t\t.replace(/`/g, \"&#x60;\")\r\n\t\t\t\t\t.replace(/(?:\\r\\n|\\r|\\n)/g, \"\\n\")\r\n\t\t\t\t\t.replace(/(\\n+)/g, \"<div>$1</div>\")\r\n\t\t\t\t\t.replace(/\\n/g, \"<br/>\")\r\n\t\t\t\t\t.replace(/<br\\/><\\/div>/g, \"</div>\")),\r\n\t\t\t\td((e = a.shortnames ? v.shortnameToUnicode(e) : e), a.emojiTemplate)\r\n\t\t\t\t\t.replace(/\\t/g, \"&nbsp;&nbsp;&nbsp;&nbsp;\")\r\n\t\t\t\t\t.replace(/  /g, \"&nbsp;&nbsp;\")\r\n\t\t\t);\r\n\t\t}\r\n\t\tfunction D(e, a) {\r\n\t\t\tswitch (\r\n\t\t\t\t((e = e\r\n\t\t\t\t\t.replace(/&#10;/g, \"\\n\")\r\n\t\t\t\t\t.replace(/&#09;/g, \"\\t\")\r\n\t\t\t\t\t.replace(/<img[^>]*alt=\"([^\"]+)\"[^>]*>/gi, \"$1\")\r\n\t\t\t\t\t.replace(/\\n|\\r/g, \"\")\r\n\t\t\t\t\t.replace(/<br[^>]*>/gi, \"\\n\")\r\n\t\t\t\t\t.replace(/(?:<(?:div|p|ol|ul|li|pre|code|object)[^>]*>)+/gi, \"<div>\")\r\n\t\t\t\t\t.replace(/(?:<\\/(?:div|p|ol|ul|li|pre|code|object)>)+/gi, \"</div>\")\r\n\t\t\t\t\t.replace(/\\n<div><\\/div>/gi, \"\\n\")\r\n\t\t\t\t\t.replace(/<div><\\/div>\\n/gi, \"\\n\")\r\n\t\t\t\t\t.replace(/(?:<div>)+<\\/div>/gi, \"\\n\")\r\n\t\t\t\t\t.replace(/([^\\n])<\\/div><div>/gi, \"$1\\n\")\r\n\t\t\t\t\t.replace(/(?:<\\/div>)+/gi, \"</div>\")\r\n\t\t\t\t\t.replace(/([^\\n])<\\/div>([^\\n])/gi, \"$1\\n$2\")\r\n\t\t\t\t\t.replace(/<\\/div>/gi, \"\")\r\n\t\t\t\t\t.replace(/([^\\n])<div>/gi, \"$1\\n\")\r\n\t\t\t\t\t.replace(/\\n<div>/gi, \"\\n\")\r\n\t\t\t\t\t.replace(/<div>\\n/gi, \"\\n\\n\")\r\n\t\t\t\t\t.replace(/<(?:[^>]+)?>/g, \"\")\r\n\t\t\t\t\t.replace(new RegExp(A, \"g\"), \"\")\r\n\t\t\t\t\t.replace(/&nbsp;/g, \" \")\r\n\t\t\t\t\t.replace(/&lt;/g, \"<\")\r\n\t\t\t\t\t.replace(/&gt;/g, \">\")\r\n\t\t\t\t\t.replace(/&quot;/g, '\"')\r\n\t\t\t\t\t.replace(/&#x27;/g, \"'\")\r\n\t\t\t\t\t.replace(/&#x60;/g, \"`\")\r\n\t\t\t\t\t.replace(/&#60;/g, \"<\")\r\n\t\t\t\t\t.replace(/&#62;/g, \">\")\r\n\t\t\t\t\t.replace(/&amp;/g, \"&\")),\r\n\t\t\t\ta.saveEmojisAs)\r\n\t\t\t) {\r\n\t\t\t\tcase \"image\":\r\n\t\t\t\t\te = d(e, a.emojiTemplate);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"shortname\":\r\n\t\t\t\t\te = v.toShort(e);\r\n\t\t\t}\r\n\t\t\treturn e;\r\n\t\t}\r\n\t\tfunction O() {\r\n\t\t\tvar e = this,\r\n\t\t\t\ta = e.editor[0].offsetWidth - e.editor[0].clientWidth,\r\n\t\t\t\to = parseInt(e.button.css(\"marginRight\"));\r\n\t\t\to !== a && (e.button.css({ marginRight: a }), e.floatingPicker && e.picker.css({ right: parseInt(e.picker.css(\"right\")) - o + a }));\r\n\t\t}\r\n\t\tfunction I() {\r\n\t\t\tvar o,\r\n\t\t\t\tn,\r\n\t\t\t\te = this;\r\n\t\t\t!e.sprite &&\r\n\t\t\t\te.lasyEmoji[0] &&\r\n\t\t\t\te.lasyEmoji.eq(0).is(\".lazy-emoji\") &&\r\n\t\t\t\t((o = e.picker.offset().top),\r\n\t\t\t\t(n = o + e.picker.height() + 20),\r\n\t\t\t\te.lasyEmoji.each(function () {\r\n\t\t\t\t\tvar e = y(this),\r\n\t\t\t\t\t\ta = e.offset().top;\r\n\t\t\t\t\tif ((o < a && a < n && e.attr(\"src\", e.data(\"src\")).removeClass(\"lazy-emoji\"), n < a)) return !1;\r\n\t\t\t\t}),\r\n\t\t\t\t(e.lasyEmoji = e.lasyEmoji.filter(\".lazy-emoji\")));\r\n\t\t}\r\n\t\tfunction N(e, a) {\r\n\t\t\treturn (a ? \"\" : \".\") + z + (e ? \"-\" + e : \"\");\r\n\t\t}\r\n\t\tfunction L(e) {\r\n\t\t\tvar o = y(\"<div/>\", R(e) ? e : { class: N(e, !0) });\r\n\t\t\treturn (\r\n\t\t\t\ty.each(_.call(arguments).slice(1), function (e, a) {\r\n\t\t\t\t\t(a = y.isFunction(a) ? a.call(o) : a) && y(a).appendTo(o);\r\n\t\t\t\t}),\r\n\t\t\t\to\r\n\t\t\t);\r\n\t\t}\r\n\t\tfunction U() {\r\n\t\t\treturn localStorage.getItem(\"recent_emojis\") || \"\";\r\n\t\t}\r\n\t\tfunction $(e, a) {\r\n\t\t\tvar o,\r\n\t\t\t\tn,\r\n\t\t\t\tt,\r\n\t\t\t\ti = U();\r\n\t\t\t(e.recent && e.recent === i && !a) ||\r\n\t\t\t\t(i.length\r\n\t\t\t\t\t? ((t = e.scrollArea.is(\".skinnable\")) || ((o = e.scrollArea.scrollTop()), a && e.recentCategory.show(), (n = e.recentCategory.is(\":visible\") ? e.recentCategory.height() : 0)),\r\n\t\t\t\t\t  (a = P(i, e.emojiBtnTemplate, !0).split(\"|\").join(\"\")),\r\n\t\t\t\t\t  e.recentCategory.children(\".emojibtn\").remove(),\r\n\t\t\t\t\t  y(a).insertAfter(e.recentCategory.children(\".emojionearea-category-title\")),\r\n\t\t\t\t\t  e.recentCategory.children(\".emojibtn\").on(\"click\", function () {\r\n\t\t\t\t\t\t\te.trigger(\"emojibtn.click\", y(this));\r\n\t\t\t\t\t  }),\r\n\t\t\t\t\t  e.recentFilter.show(),\r\n\t\t\t\t\t  t || (e.recentCategory.show(), n !== (t = e.recentCategory.height()) && e.scrollArea.scrollTop(o + t - n)))\r\n\t\t\t\t\t: (e.recentFilter.hasClass(\"active\") && e.recentFilter.removeClass(\"active\").next().addClass(\"active\"), e.recentCategory.hide(), e.recentFilter.hide()),\r\n\t\t\t\t(e.recent = i));\r\n\t\t}\r\n\t\tfunction p(l, a, i) {\r\n\t\t\t(l.options = i = F(i)),\r\n\t\t\t\t(l.sprite = i.sprite && T < 3),\r\n\t\t\t\t(l.inline = null === i.inline ? a.is(\"INPUT\") : i.inline),\r\n\t\t\t\t(l.shortnames = i.shortnames),\r\n\t\t\t\t(l.saveEmojisAs = i.saveEmojisAs),\r\n\t\t\t\t(l.standalone = i.standalone),\r\n\t\t\t\t(l.emojiTemplate = '<img alt=\"{alt}\" class=\"emojione' + (l.sprite ? '-{uni}\" src=\"' + C + '\"/>' : 'emoji\" src=\"{img}\"/>')),\r\n\t\t\t\t(l.emojiTemplateAlt = l.sprite ? '<i class=\"emojione-{uni}\"/>' : '<img class=\"emojioneemoji\" src=\"{img}\"/>'),\r\n\t\t\t\t(l.emojiBtnTemplate = '<i class=\"emojibtn\" role=\"button\" data-name=\"{name}\" title=\"{friendlyName}\">' + l.emojiTemplateAlt + \"</i>\"),\r\n\t\t\t\t(l.recentEmojis =\r\n\t\t\t\t\ti.recentEmojis &&\r\n\t\t\t\t\t(function () {\r\n\t\t\t\t\t\tvar e = \"test\";\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\treturn localStorage.setItem(e, e), localStorage.removeItem(e), !0;\r\n\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\treturn !1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})());\r\n\t\t\tvar e = i.pickerPosition;\r\n\t\t\t(l.floatingPicker = \"top\" === e || \"bottom\" === e), ((l.source = a).is(\":disabled\") || a.is(\".disabled\")) && l.disable();\r\n\t\t\tvar t,\r\n\t\t\t\to,\r\n\t\t\t\tr,\r\n\t\t\t\ts,\r\n\t\t\t\t_,\r\n\t\t\t\tn,\r\n\t\t\t\tc,\r\n\t\t\t\tm,\r\n\t\t\t\tg,\r\n\t\t\t\tu,\r\n\t\t\t\td = a.is(\"TEXTAREA\") || a.is(\"INPUT\") ? \"val\" : \"text\",\r\n\t\t\t\tp = L(\r\n\t\t\t\t\t\"tones\",\r\n\t\t\t\t\ti.tones\r\n\t\t\t\t\t\t? function () {\r\n\t\t\t\t\t\t\t\tthis.addClass(N(\"tones-\" + i.tonesStyle, !0));\r\n\t\t\t\t\t\t\t\tfor (var e = 0; e <= 5; e++) this.append(y(\"<i/>\", { class: \"btn-tone btn-tone-\" + e + (e ? \"\" : \" active\"), \"data-skin\": e, role: \"button\" }));\r\n\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t: null\r\n\t\t\t\t),\r\n\t\t\t\tf = L(\r\n\t\t\t\t\t{ class: z + (l.standalone ? \" \" + z + \"-standalone \" : \" \") + (a.attr(\"class\") || \"\"), role: \"application\" },\r\n\t\t\t\t\t(t = l.editor = L(\"editor\").attr({ contenteditable: !l.standalone, placeholder: i.placeholder || a.data(\"placeholder\") || a.attr(\"placeholder\") || \"\", tabindex: 0 })),\r\n\t\t\t\t\t(o = l.button = L(\"button\", L(\"button-open\"), L(\"button-close\")).attr(\"title\", i.buttonTitle)),\r\n\t\t\t\t\t(r = l.picker =\r\n\t\t\t\t\t\tL(\r\n\t\t\t\t\t\t\t\"picker\",\r\n\t\t\t\t\t\t\tL(\r\n\t\t\t\t\t\t\t\t\"wrapper\",\r\n\t\t\t\t\t\t\t\t(s = L(\"filters\")),\r\n\t\t\t\t\t\t\t\ti.search\r\n\t\t\t\t\t\t\t\t\t? (n = L(\r\n\t\t\t\t\t\t\t\t\t\t\t\"search-panel\",\r\n\t\t\t\t\t\t\t\t\t\t\tL(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"search\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ti.search\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? function () {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t(l.search = y(\"<input/>\", { placeholder: i.searchPlaceholder || \"\", type: \"text\", class: \"search\" })), this.append(l.search);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: null\r\n\t\t\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\t\t\tp\r\n\t\t\t\t\t\t\t\t\t  ))\r\n\t\t\t\t\t\t\t\t\t: null,\r\n\t\t\t\t\t\t\t\t(u = L(\"scroll-area\", i.tones && !i.search ? L(\"tones-panel\", p) : null, (c = L(\"emojis-list\"))))\r\n\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t.addClass(N(\"picker-position-\" + i.pickerPosition, !0))\r\n\t\t\t\t\t\t\t.addClass(N(\"filters-position-\" + i.filtersPosition, !0))\r\n\t\t\t\t\t\t\t.addClass(N(\"search-position-\" + i.searchPosition, !0))\r\n\t\t\t\t\t\t\t.addClass(\"hidden\"))\r\n\t\t\t\t);\r\n\t\t\ti.search && n.addClass(N(\"with-search\", !0)),\r\n\t\t\t\t(l.searchSel = null),\r\n\t\t\t\tt.data(a.data()),\r\n\t\t\t\ty.each(i.attributes, function (e, a) {\r\n\t\t\t\t\tt.attr(e, a);\r\n\t\t\t\t});\r\n\t\t\tvar h = L(\"category-block\").attr({ \"data-tone\": 0 }).prependTo(c);\r\n\t\t\ty.each(i.filters, function (e, a) {\r\n\t\t\t\tvar o = 0;\r\n\t\t\t\tif (\"recent\" !== e || l.recentEmojis) {\r\n\t\t\t\t\tif (\"tones\" !== e)\r\n\t\t\t\t\t\ty(\"<i/>\", { class: N(\"filter\", !0) + \" \" + N(\"filter-\" + e, !0), \"data-filter\": e, title: a.title })\r\n\t\t\t\t\t\t\t.wrapInner(P(a.icon, l.emojiTemplateAlt))\r\n\t\t\t\t\t\t\t.appendTo(s);\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tif (!i.tones) return;\r\n\t\t\t\t\t\to = 5;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tdo {\r\n\t\t\t\t\t\tvar n = a.emoji.replace(/[\\s,;]+/g, \"|\"),\r\n\t\t\t\t\t\t\tt = 0 === o ? L(\"category\").attr({ name: e, \"data-tone\": o }).appendTo(h) : L(\"category-block\").attr({ name: e, \"data-tone\": o }).appendTo(c);\r\n\t\t\t\t\t} while (\r\n\t\t\t\t\t\t(0 < o && (t.hide(), (n = n.split(\"|\").join(\"_tone\" + o + \"|\") + \"_tone\" + o)),\r\n\t\t\t\t\t\t(n = P((n = \"recent\" === e ? U() : n), l.sprite ? '<i class=\"emojibtn\" role=\"button\" data-name=\"{name}\" title=\"{friendlyName}\"><i class=\"emojione-{uni}\"></i></i>' : '<i class=\"emojibtn\" role=\"button\" data-name=\"{name}\" title=\"{friendlyName}\"><img class=\"emojioneemoji lazy-emoji\" data-src=\"{img}\"/></i>', !0)\r\n\t\t\t\t\t\t\t.split(\"|\")\r\n\t\t\t\t\t\t\t.join(\"\")),\r\n\t\t\t\t\t\tt.html(n),\r\n\t\t\t\t\t\ty('<div class=\"emojionearea-category-title\"/>').text(a.title).prependTo(t),\r\n\t\t\t\t\t\t0 < --o)\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t\t}),\r\n\t\t\t\t(i.filters = null),\r\n\t\t\t\tl.sprite || (l.lasyEmoji = c.find(\".lazy-emoji\")),\r\n\t\t\t\t(_ = s.find(N(\"filter\"))).eq(0).addClass(\"active\"),\r\n\t\t\t\t(g = c.find(N(\"category-block\"))),\r\n\t\t\t\t(m = c.find(N(\"category\"))),\r\n\t\t\t\t(l.recentFilter = _.filter('[data-filter=\"recent\"]')),\r\n\t\t\t\t(l.recentCategory = m.filter(\"[name=recent]\")),\r\n\t\t\t\t(l.scrollArea = u),\r\n\t\t\t\ti.container ? y(i.container).wrapInner(f) : f.insertAfter(a),\r\n\t\t\t\ti.hideSource && a.hide(),\r\n\t\t\t\tl.setText(a[d]()),\r\n\t\t\t\ta[d](l.getText()),\r\n\t\t\t\tO.apply(l),\r\n\t\t\t\tl.standalone && !l.getText().length && ((w = y(a).data(\"emoji-placeholder\") || i.emojiPlaceholder), l.setText(w), t.addClass(\"has-placeholder\")),\r\n\t\t\t\tS(l, c.find(\".emojibtn\"), { click: \"emojibtn.click\" }),\r\n\t\t\t\tS(l, window, { resize: \"!resize\" }),\r\n\t\t\t\tS(l, p.children(), { click: \"tone.click\" }),\r\n\t\t\t\tS(l, [r, o], { mousedown: \"!mousedown\" }, t),\r\n\t\t\t\tS(l, o, { click: \"button.click\" }),\r\n\t\t\t\tS(l, t, { paste: \"!paste\" }, t),\r\n\t\t\t\tS(l, t, [\"focus\", \"blur\"], function () {\r\n\t\t\t\t\treturn !l.stayFocused && t;\r\n\t\t\t\t}),\r\n\t\t\t\tS(l, r, { mousedown: \"picker.mousedown\", mouseup: \"picker.mouseup\", click: \"picker.click\", keyup: \"picker.keyup\", keydown: \"picker.keydown\", keypress: \"picker.keypress\" }),\r\n\t\t\t\tS(l, t, [\"mousedown\", \"mouseup\", \"click\", \"keyup\", \"keydown\", \"keypress\"]),\r\n\t\t\t\tS(l, r.find(\".emojionearea-filter\"), { click: \"filter.click\" }),\r\n\t\t\t\tS(l, a, { change: \"source.change\" }),\r\n\t\t\t\ti.search && S(l, l.search, { keyup: \"search.keypress\", focus: \"search.focus\", blur: \"search.blur\" });\r\n\t\t\tvar b,\r\n\t\t\t\tw,\r\n\t\t\t\tk = !1;\r\n\t\t\tu.on(\"scroll\", function () {\r\n\t\t\t\tvar o, n, e;\r\n\t\t\t\t!k &&\r\n\t\t\t\t\t(I.call(l), u.is(\":not(.skinnable)\")) &&\r\n\t\t\t\t\t((o = m.eq(0)),\r\n\t\t\t\t\t(n = u.offset().top),\r\n\t\t\t\t\tm.each(function (e, a) {\r\n\t\t\t\t\t\treturn !(10 <= y(a).offset().top - n) && void (o = y(a));\r\n\t\t\t\t\t}),\r\n\t\t\t\t\t(e = _.filter('[data-filter=\"' + o.attr(\"name\") + '\"]'))[0] && !e.is(\".active\") && (_.removeClass(\"active\"), e.addClass(\"active\")));\r\n\t\t\t}),\r\n\t\t\t\tl\r\n\t\t\t\t\t.on(\"@filter.click\", function (e) {\r\n\t\t\t\t\t\tvar a = e.is(\".active\");\r\n\t\t\t\t\t\tif (u.is(\".skinnable\")) {\r\n\t\t\t\t\t\t\tif (a) return;\r\n\t\t\t\t\t\t\tp.children().eq(0).click();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t(k = !0), a || (_.filter(\".active\").removeClass(\"active\"), e.addClass(\"active\"));\r\n\t\t\t\t\t\tvar o = m.filter('[name=\"' + e.data(\"filter\") + '\"]').offset().top,\r\n\t\t\t\t\t\t\ta = u.scrollTop(),\r\n\t\t\t\t\t\t\te = u.offset().top;\r\n\t\t\t\t\t\tu.stop().animate({ scrollTop: o + a - e - 2 }, 200, \"swing\", function () {\r\n\t\t\t\t\t\t\tI.call(l), (k = !1);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@picker.show\", function () {\r\n\t\t\t\t\t\tl.recentEmojis && $(l), I.call(l);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@tone.click\", function (e) {\r\n\t\t\t\t\t\tp.children().removeClass(\"active\");\r\n\t\t\t\t\t\te = e.addClass(\"active\").data(\"skin\");\r\n\t\t\t\t\t\te\r\n\t\t\t\t\t\t\t? (u.addClass(\"skinnable\"),\r\n\t\t\t\t\t\t\t  g\r\n\t\t\t\t\t\t\t\t\t.hide()\r\n\t\t\t\t\t\t\t\t\t.filter(\"[data-tone=\" + e + \"]\")\r\n\t\t\t\t\t\t\t\t\t.show(),\r\n\t\t\t\t\t\t\t  _.removeClass(\"active\"))\r\n\t\t\t\t\t\t\t: (u.removeClass(\"skinnable\"), g.hide().filter(\"[data-tone=0]\").show(), _.eq(0).click()),\r\n\t\t\t\t\t\t\tI.call(l),\r\n\t\t\t\t\t\t\ti.search && l.trigger(\"search.keypress\");\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@button.click\", function (e) {\r\n\t\t\t\t\t\te.is(\".active\") ? l.hidePicker() : (l.showPicker(), (l.searchSel = null));\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@!paste\", function (i, e) {\r\n\t\t\t\t\t\tfunction a(e) {\r\n\t\t\t\t\t\t\tvar a = \"caret-\" + new Date().getTime(),\r\n\t\t\t\t\t\t\t\to = B(e, l);\r\n\t\t\t\t\t\t\tE(o), E('<i id=\"' + a + '\"></i>'), i.scrollTop(r);\r\n\t\t\t\t\t\t\tvar n = y(\"#\" + a),\r\n\t\t\t\t\t\t\t\tt = n.offset().top - i.offset().top;\r\n\t\t\t\t\t\t\t((a = i.height()) <= r + t || t < r) && i.scrollTop(r + t - (2 * a) / 3), n.remove(), (l.stayFocused = !1), O.apply(l), q(l, \"paste\", [i, e, o]);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (e.originalEvent.clipboardData) {\r\n\t\t\t\t\t\t\tvar o = e.originalEvent.clipboardData.getData(\"text/plain\");\r\n\t\t\t\t\t\t\treturn a(o), e.preventDefault ? e.preventDefault() : e.stop(), (e.returnValue = !1), e.stopPropagation(), !1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t(l.stayFocused = !0), E(\"<span>\" + A + \"</span>\");\r\n\t\t\t\t\t\tvar n = j(i[0]),\r\n\t\t\t\t\t\t\tr = i.scrollTop(),\r\n\t\t\t\t\t\t\tt = y(\"<div/>\", { contenteditable: !0 }).css({ position: \"fixed\", left: \"-999px\", width: \"1px\", height: \"1px\", top: \"20px\", overflow: \"hidden\" }).appendTo(y(\"BODY\")).focus();\r\n\t\t\t\t\t\twindow.setTimeout(function () {\r\n\t\t\t\t\t\t\ti.focus(), x(i[0], n);\r\n\t\t\t\t\t\t\tvar e = D(t.html().replace(/\\r\\n|\\n|\\r/g, \"<br>\"), l);\r\n\t\t\t\t\t\t\tt.remove(), a(e);\r\n\t\t\t\t\t\t}, 200);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@emojibtn.click\", function (e) {\r\n\t\t\t\t\t\tvar a, o, n;\r\n\t\t\t\t\t\tt.removeClass(\"has-placeholder\"), null !== l.searchSel && (t.focus(), x(t[0], l.searchSel), (l.searchSel = null)), l.standalone ? (t.html(P(e.data(\"name\"), l.emojiTemplate)), l.trigger(\"blur\")) : (j(t[0]), E(P(e.data(\"name\"), l.emojiTemplate))), l.recentEmojis && ((a = l), (o = e.data(\"name\")), (n = U().split(\"|\")), -1 !== (e = n.indexOf(o)) && n.splice(e, 1), n.unshift(o), 9 < n.length && n.pop(), localStorage.setItem(\"recent_emojis\", n.join(\"|\")), $(a)), l.trigger(\"search.keypress\");\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@!resize @keyup @emojibtn.click\", O)\r\n\t\t\t\t\t.on(\"@!mousedown\", function (e, a) {\r\n\t\t\t\t\t\treturn y(a.target).hasClass(\"search\") ? ((l.stayFocused = !0), null === l.searchSel && (l.searchSel = j(e[0]))) : (f.is(\".focused\") || e.trigger(\"focus\"), a.preventDefault()), !1;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@change\", function () {\r\n\t\t\t\t\t\tvar e = l.editor.html().replace(/<\\/?(?:div|span|p)[^>]*>/gi, \"\");\r\n\t\t\t\t\t\t(e.length && !/^<br[^>]*>$/i.test(e)) || l.editor.html((l.content = \"\")), a[d](l.getText());\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@source.change\", function () {\r\n\t\t\t\t\t\tl.setText(a[d]()), q(\"change\");\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@focus\", function () {\r\n\t\t\t\t\t\tf.addClass(\"focused\");\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.on(\"@blur\", function () {\r\n\t\t\t\t\t\tf.removeClass(\"focused\"), i.hidePickerOnBlur && l.hidePicker();\r\n\t\t\t\t\t\tvar e = l.editor.html();\r\n\t\t\t\t\t\tl.content !== e ? ((l.content = e), q(l, \"change\", [l.editor]), a.trigger(\"blur\").trigger(\"change\")) : a.trigger(\"blur\"), i.search && (l.search.val(\"\"), l.trigger(\"search.keypress\", !0));\r\n\t\t\t\t\t}),\r\n\t\t\t\ti.search &&\r\n\t\t\t\t\tl\r\n\t\t\t\t\t\t.on(\"@search.focus\", function () {\r\n\t\t\t\t\t\t\t(l.stayFocused = !0), l.search.addClass(\"focused\");\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.on(\"@search.keypress\", function (e) {\r\n\t\t\t\t\t\t\tvar n = r.find(\".emojionearea-filter\"),\r\n\t\t\t\t\t\t\t\to = i.tones ? p.find(\"i.active\").data(\"skin\") : 0,\r\n\t\t\t\t\t\t\t\tt = l.search.val().replace(/ /g, \"_\").replace(/\"/g, '\\\\\"');\r\n\t\t\t\t\t\t\tt && t.length\r\n\t\t\t\t\t\t\t\t? (l.recentFilter.hasClass(\"active\") && l.recentFilter.removeClass(\"active\").next().addClass(\"active\"),\r\n\t\t\t\t\t\t\t\t  l.recentCategory.hide(),\r\n\t\t\t\t\t\t\t\t  l.recentFilter.hide(),\r\n\t\t\t\t\t\t\t\t  g.each(function () {\r\n\t\t\t\t\t\t\t\t\t\tfunction e(e, a) {\r\n\t\t\t\t\t\t\t\t\t\t\tvar o = e.find('.emojibtn[data-name*=\"' + t + '\"]');\r\n\t\t\t\t\t\t\t\t\t\t\t0 === o.length ? (e.data(\"tone\") === a && e.hide(), n.filter('[data-filter=\"' + e.attr(\"name\") + '\"]').hide()) : (e.find('.emojibtn:not([data-name*=\"' + t + '\"])').hide(), o.show(), e.data(\"tone\") === a && e.show(), n.filter('[data-filter=\"' + e.attr(\"name\") + '\"]').show());\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tvar a = y(this);\r\n\t\t\t\t\t\t\t\t\t\t0 === a.data(\"tone\")\r\n\t\t\t\t\t\t\t\t\t\t\t? m.filter(':not([name=\"recent\"])').each(function () {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\te(y(this), 0);\r\n\t\t\t\t\t\t\t\t\t\t\t  })\r\n\t\t\t\t\t\t\t\t\t\t\t: e(a, o);\r\n\t\t\t\t\t\t\t\t  }),\r\n\t\t\t\t\t\t\t\t  k ? I.call(l) : u.trigger(\"scroll\"))\r\n\t\t\t\t\t\t\t\t: ($(l, !0), g.filter('[data-tone=\"' + p.find(\"i.active\").data(\"skin\") + '\"]:not([name=\"recent\"])').show(), y(\".emojibtn\", g).show(), n.show(), I.call(l));\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.on(\"@search.blur\", function () {\r\n\t\t\t\t\t\t\t(l.stayFocused = !1), l.search.removeClass(\"focused\"), l.trigger(\"blur\");\r\n\t\t\t\t\t\t}),\r\n\t\t\t\ti.shortcuts &&\r\n\t\t\t\t\tl.on(\"@keydown\", function (e, a) {\r\n\t\t\t\t\t\ta.ctrlKey || (9 == a.which ? (a.preventDefault(), o.click()) : 27 == a.which && (a.preventDefault(), o.is(\".active\") && l.hidePicker()));\r\n\t\t\t\t\t}),\r\n\t\t\t\tR(i.events) &&\r\n\t\t\t\t\t!y.isEmptyObject(i.events) &&\r\n\t\t\t\t\ty.each(i.events, function (e, a) {\r\n\t\t\t\t\t\tl.on(e.replace(/_/g, \".\"), a);\r\n\t\t\t\t\t}),\r\n\t\t\t\ti.autocomplete &&\r\n\t\t\t\t\t((b = function () {\r\n\t\t\t\t\t\tvar e = { maxCount: i.textcomplete.maxCount, placement: i.textcomplete.placement };\r\n\t\t\t\t\t\ti.shortcuts &&\r\n\t\t\t\t\t\t\t(e.onKeydown = function (e, a) {\r\n\t\t\t\t\t\t\t\tif (!e.ctrlKey && 13 == e.which) return a.KEY_ENTER;\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\tvar o = y.map(v.emojioneList, function (e, a) {\r\n\t\t\t\t\t\t\treturn !i.autocompleteTones && /_tone[12345]/.test(a) ? null : a;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\to.sort(),\r\n\t\t\t\t\t\t\tt.textcomplete(\r\n\t\t\t\t\t\t\t\t[\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\tid: z,\r\n\t\t\t\t\t\t\t\t\t\tmatch: /\\B(:[\\-+\\w]*)$/,\r\n\t\t\t\t\t\t\t\t\t\tsearch: function (a, e) {\r\n\t\t\t\t\t\t\t\t\t\t\te(\r\n\t\t\t\t\t\t\t\t\t\t\t\ty.map(o, function (e) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\treturn 0 === e.indexOf(a) ? e : null;\r\n\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\ttemplate: function (e) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn P(e, l.emojiTemplate) + \" \" + e.replace(/:/g, \"\");\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\treplace: function (e) {\r\n\t\t\t\t\t\t\t\t\t\t\treturn P(e, l.emojiTemplate);\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tcache: !0,\r\n\t\t\t\t\t\t\t\t\t\tindex: 1,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\te\r\n\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\ti.textcomplete.placement && \"static\" == y(t.data(\"textComplete\").option.appendTo).css(\"position\") && y(t.data(\"textComplete\").option.appendTo).css(\"position\", \"relative\");\r\n\t\t\t\t\t}),\r\n\t\t\t\t\t(w = function () {\r\n\t\t\t\t\t\tvar e;\r\n\t\t\t\t\t\tl.disabled\r\n\t\t\t\t\t\t\t? ((e = function () {\r\n\t\t\t\t\t\t\t\t\tl.off(\"enabled\", e), b();\r\n\t\t\t\t\t\t\t  }),\r\n\t\t\t\t\t\t\t  l.on(\"enabled\", e))\r\n\t\t\t\t\t\t\t: b();\r\n\t\t\t\t\t}),\r\n\t\t\t\t\ty.fn.textcomplete ? w() : y.ajax({ url: \"https://cdn.rawgit.com/yuku-t/jquery-textcomplete/v1.3.4/dist/jquery.textcomplete.js\", dataType: \"script\", cache: !0, success: w })),\r\n\t\t\t\tl.inline &&\r\n\t\t\t\t\t(f.addClass(N(\"inline\", !0)),\r\n\t\t\t\t\tl.on(\"@keydown\", function (e, a) {\r\n\t\t\t\t\t\t13 == a.which && a.preventDefault();\r\n\t\t\t\t\t})),\r\n\t\t\t\t/firefox/i.test(navigator.userAgent) && document.execCommand(\"enableObjectResizing\", !1, !1),\r\n\t\t\t\t(l.isReady = !0),\r\n\t\t\t\tl.trigger(\"onLoad\", t),\r\n\t\t\t\tl.trigger(\"ready\", t);\r\n\t\t}\r\n\t\twindow.getSelection && document.createRange\r\n\t\t\t? ((j = function (e) {\r\n\t\t\t\t\tvar a = window.getSelection && window.getSelection();\r\n\t\t\t\t\tif (a && 0 < a.rangeCount) return a.getRangeAt(0);\r\n\t\t\t  }),\r\n\t\t\t  (x = function (e, a) {\r\n\t\t\t\t\tvar o = document.createRange();\r\n\t\t\t\t\to.setStart(a.startContainer, a.startOffset), o.setEnd(a.endContainer, a.endOffset), (a = window.getSelection()).removeAllRanges(), a.addRange(o);\r\n\t\t\t  }))\r\n\t\t\t: document.selection &&\r\n\t\t\t  document.body.createTextRange &&\r\n\t\t\t  ((j = function (e) {\r\n\t\t\t\t\treturn document.selection.createRange();\r\n\t\t\t  }),\r\n\t\t\t  (x = function (e, a) {\r\n\t\t\t\t\tvar o = document.body.createTextRange();\r\n\t\t\t\t\to.moveToElementText(e), o.setStart(a.startContanier, a.startOffset), o.setEnd(a.endContainer, a.endOffset), o.select();\r\n\t\t\t  }));\r\n\t\tvar f = { defaultBase: \"https://cdnjs.cloudflare.com/ajax/libs/emojione/\", defaultBase3: \"https://cdn.jsdelivr.net/\", base: null, isLoading: !1 };\r\n\t\tfunction h(a) {\r\n\t\t\tvar e,\r\n\t\t\t\to = m();\r\n\t\t\t(a = F(a)),\r\n\t\t\t\tf.isLoading ||\r\n\t\t\t\t\t(!v || u(g(v)) < 2\r\n\t\t\t\t\t\t? ((f.isLoading = !0),\r\n\t\t\t\t\t\t  (e = 5 < u(o) ? f.defaultBase3 + \"npm/emojione@\" + o : 4 < u(o) ? f.defaultBase3 + \"emojione/\" + o : f.defaultBase + \"/\" + o),\r\n\t\t\t\t\t\t  y.ajax({\r\n\t\t\t\t\t\t\t\turl: e + \"/lib/js/emojione.min.js\",\r\n\t\t\t\t\t\t\t\tdataType: \"script\",\r\n\t\t\t\t\t\t\t\tcache: !0,\r\n\t\t\t\t\t\t\t\tsuccess: function () {\r\n\t\t\t\t\t\t\t\t\tvar e;\r\n\t\t\t\t\t\t\t\t\tfor (v = window.emojione, o = g(v), e = 4 < (T = u(o)) ? ((f.base = f.defaultBase3 + \"emojione/assets/\" + o), f.base + \"/sprites/emojione-sprite-\" + v.emojiSize + \".css\") : ((f.base = f.defaultBase + o + \"/assets\"), f.base + \"/sprites/emojione.sprites.css\"), a.sprite && (document.createStyleSheet ? document.createStyleSheet(e) : y(\"<link/>\", { rel: \"stylesheet\", href: e }).appendTo(\"head\")); t.length; ) t.shift().call();\r\n\t\t\t\t\t\t\t\t\tf.isLoading = !1;\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t  }))\r\n\t\t\t\t\t\t: ((o = g(v)), (T = u(o)), (f.base = 4 < T ? f.defaultBase3 + \"emojione/assets/\" + o : f.defaultBase + o + \"/assets\"))),\r\n\t\t\t\ti(function () {\r\n\t\t\t\t\tvar e = \"\";\r\n\t\t\t\t\ta.useInternalCDN && (4 < T && (e = v.emojiSize + \"/\"), (v.imagePathPNG = f.base + \"/png/\" + e), (v.imagePathSVG = f.base + \"/svg/\" + e), (v.imagePathSVGSprites = f.base + \"/sprites/emojione.sprites.svg\"), (v.imageType = a.imageType)), 4 < u(o) ? ((s = v.regUnicode), (v.imageType = a.imageType || \"png\")) : (s = new RegExp(\"<object[^>]*>.*?</object>|<span[^>]*>.*?</span>|<(?:object|embed|svg|img|div|span|p|a)[^>]*>|(\" + v.unicodeRegexp + \")\", \"gi\"));\r\n\t\t\t\t});\r\n\t\t}\r\n\t\tfunction a(e, a) {\r\n\t\t\tvar o = this;\r\n\t\t\th(a),\r\n\t\t\t\t(r[(o.id = ++n)] = {}),\r\n\t\t\t\t(l[o.id] = {}),\r\n\t\t\t\ti(function () {\r\n\t\t\t\t\tp(o, e, a);\r\n\t\t\t\t});\r\n\t\t}\r\n\t\t(a.prototype.on = function (e, o) {\r\n\t\t\tvar n;\r\n\t\t\treturn (\r\n\t\t\t\te &&\r\n\t\t\t\t\ty.isFunction(o) &&\r\n\t\t\t\t\t((n = this),\r\n\t\t\t\t\ty.each(e.toLowerCase().split(\" \"), function (e, a) {\r\n\t\t\t\t\t\t(function (n, t) {\r\n\t\t\t\t\t\t\tt = t.replace(/^@/, \"\");\r\n\t\t\t\t\t\t\tvar e = n.id;\r\n\t\t\t\t\t\t\tl[e][t] &&\r\n\t\t\t\t\t\t\t\t(y.each(l[e][t], function (e, o) {\r\n\t\t\t\t\t\t\t\t\ty.each(y.isArray(o[0]) ? o[0] : [o[0]], function (e, a) {\r\n\t\t\t\t\t\t\t\t\t\ty(a).on(o[1], function () {\r\n\t\t\t\t\t\t\t\t\t\t\tvar e = _.call(arguments),\r\n\t\t\t\t\t\t\t\t\t\t\t\ta = y.isFunction(o[2]) ? o[2].apply(n, [t].concat(e)) : o[2];\r\n\t\t\t\t\t\t\t\t\t\t\ta && q(n, t, [a].concat(e));\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}),\r\n\t\t\t\t\t\t\t\t(l[e][t] = null));\r\n\t\t\t\t\t\t})(n, a),\r\n\t\t\t\t\t\t\t(r[n.id][a] || (r[n.id][a] = [])).push(o);\r\n\t\t\t\t\t})),\r\n\t\t\t\tthis\r\n\t\t\t);\r\n\t\t}),\r\n\t\t\t(a.prototype.off = function (e, n) {\r\n\t\t\t\tvar t;\r\n\t\t\t\treturn (\r\n\t\t\t\t\te &&\r\n\t\t\t\t\t\t((t = this.id),\r\n\t\t\t\t\t\ty.each(e.toLowerCase().replace(/_/g, \".\").split(\" \"), function (e, o) {\r\n\t\t\t\t\t\t\tr[t][o] &&\r\n\t\t\t\t\t\t\t\t!/^@/.test(o) &&\r\n\t\t\t\t\t\t\t\t(n\r\n\t\t\t\t\t\t\t\t\t? y.each(r[t][o], function (e, a) {\r\n\t\t\t\t\t\t\t\t\t\t\ta === n && (r[t][o] = r[t][o].splice(e, 1));\r\n\t\t\t\t\t\t\t\t\t  })\r\n\t\t\t\t\t\t\t\t\t: (r[t][o] = []));\r\n\t\t\t\t\t\t})),\r\n\t\t\t\t\tthis\r\n\t\t\t\t);\r\n\t\t\t}),\r\n\t\t\t(a.prototype.trigger = function () {\r\n\t\t\t\tvar e = _.call(arguments),\r\n\t\t\t\t\ta = [this].concat(e.slice(0, 1));\r\n\t\t\t\treturn a.push(e.slice(1)), q.apply(this, a);\r\n\t\t\t}),\r\n\t\t\t(a.prototype.setFocus = function () {\r\n\t\t\t\tvar e = this;\r\n\t\t\t\treturn (\r\n\t\t\t\t\ti(function () {\r\n\t\t\t\t\t\te.editor.focus();\r\n\t\t\t\t\t}),\r\n\t\t\t\t\te\r\n\t\t\t\t);\r\n\t\t\t}),\r\n\t\t\t(a.prototype.setText = function (e) {\r\n\t\t\t\tvar a = this;\r\n\t\t\t\treturn (\r\n\t\t\t\t\ti(function () {\r\n\t\t\t\t\t\ta.editor.html(B(e, a)), (a.content = a.editor.html()), q(a, \"change\", [a.editor]), O.apply(a);\r\n\t\t\t\t\t}),\r\n\t\t\t\t\ta\r\n\t\t\t\t);\r\n\t\t\t}),\r\n\t\t\t(a.prototype.getText = function () {\r\n\t\t\t\treturn D(this.editor.html(), this);\r\n\t\t\t}),\r\n\t\t\t(a.prototype.showPicker = function () {\r\n\t\t\t\tvar e = this;\r\n\t\t\t\treturn (\r\n\t\t\t\t\te._sh_timer && window.clearTimeout(e._sh_timer),\r\n\t\t\t\t\te.picker.removeClass(\"hidden\"),\r\n\t\t\t\t\t(e._sh_timer = window.setTimeout(function () {\r\n\t\t\t\t\t\te.button.addClass(\"active\");\r\n\t\t\t\t\t}, 50)),\r\n\t\t\t\t\tq(e, \"picker.show\", [e.picker]),\r\n\t\t\t\t\te\r\n\t\t\t\t);\r\n\t\t\t}),\r\n\t\t\t(a.prototype.hidePicker = function () {\r\n\t\t\t\tvar e = this;\r\n\t\t\t\treturn (\r\n\t\t\t\t\te._sh_timer && window.clearTimeout(e._sh_timer),\r\n\t\t\t\t\te.button.removeClass(\"active\"),\r\n\t\t\t\t\t(e._sh_timer = window.setTimeout(function () {\r\n\t\t\t\t\t\te.picker.addClass(\"hidden\");\r\n\t\t\t\t\t}, 500)),\r\n\t\t\t\t\tq(e, \"picker.hide\", [e.picker]),\r\n\t\t\t\t\te\r\n\t\t\t\t);\r\n\t\t\t}),\r\n\t\t\t(a.prototype.enable = function () {\r\n\t\t\t\tfunction e() {\r\n\t\t\t\t\t(a.disabled = !1), a.editor.prop(\"contenteditable\", !0), a.button.show();\r\n\t\t\t\t\tvar e = a[a.standalone ? \"button\" : \"editor\"];\r\n\t\t\t\t\te.parent().removeClass(\"emojionearea-disable\"), q(a, \"enabled\", [e]);\r\n\t\t\t\t}\r\n\t\t\t\tvar a = this;\r\n\t\t\t\treturn a.isReady ? e() : a.on(\"ready\", e), a;\r\n\t\t\t}),\r\n\t\t\t(a.prototype.disable = function () {\r\n\t\t\t\tvar a = this;\r\n\t\t\t\ta.disabled = !0;\r\n\t\t\t\tfunction e() {\r\n\t\t\t\t\ta.editor.prop(\"contenteditable\", !1), a.hidePicker(), a.button.hide();\r\n\t\t\t\t\tvar e = a[a.standalone ? \"button\" : \"editor\"];\r\n\t\t\t\t\te.parent().addClass(\"emojionearea-disable\"), q(a, \"disabled\", [e]);\r\n\t\t\t\t}\r\n\t\t\t\treturn a.isReady ? e() : a.on(\"ready\", e), a;\r\n\t\t\t}),\r\n\t\t\t(y.fn.emojioneArea = function (e) {\r\n\t\t\t\treturn this.each(function () {\r\n\t\t\t\t\treturn this.emojioneArea || (y.data(this, \"emojioneArea\", (this.emojioneArea = new a(y(this), e))), this.emojioneArea);\r\n\t\t\t\t});\r\n\t\t\t}),\r\n\t\t\t(y.fn.emojioneArea.defaults = o()),\r\n\t\t\t(y.fn.emojioneAreaText = function (e) {\r\n\t\t\t\te = F(e);\r\n\t\t\t\tvar a = this,\r\n\t\t\t\t\to = { shortnames: !e || void 0 === e.shortnames || e.shortnames, emojiTemplate: '<img alt=\"{alt}\" class=\"emojione' + (e && e.sprite && T < 3 ? '-{uni}\" src=\"' + C : 'emoji\" src=\"{img}') + '\"/>' };\r\n\t\t\t\treturn (\r\n\t\t\t\t\th(e),\r\n\t\t\t\t\ti(function () {\r\n\t\t\t\t\t\ta.each(function () {\r\n\t\t\t\t\t\t\tvar e = y(this);\r\n\t\t\t\t\t\t\treturn e.hasClass(\"emojionearea-text\") || e.addClass(\"emojionearea-text\").html(B(e.is(\"TEXTAREA\") || e.is(\"INPUT\") ? e.val() : e.text(), o)), e;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}),\r\n\t\t\t\t\tthis\r\n\t\t\t\t);\r\n\t\t\t});\r\n\t}, window);\r\n"], "names": ["window", "global", "self", "document", "e", "a", "require", "exports", "module", "define", "amd", "j<PERSON><PERSON><PERSON>", "y", "n", "r", "l", "v", "emojione", "t", "i", "push", "j", "x", "s", "C", "_", "slice", "z", "T", "A", "q", "o", "toLowerCase", "id", "length", "each", "apply", "S", "currentTarget", "isArray", "c", "imageType", "imagePathSVG", "imagePathPNG", "substr", "replace", "char<PERSON>t", "toUpperCase", "uc_base", "uc_output", "convert", "P", "emojioneList", "unicode", "E", "getSelection", "getRangeAt", "rangeCount", "deleteContents", "createElement", "innerHTML", "createDocumentFragment", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "insertNode", "cloneRange", "setStartAfter", "collapse", "removeAllRanges", "addRange", "selection", "type", "createRange", "pasteHTML", "m", "emojioneVersion", "R", "g", "cacheBustParam", "jsEscapeMap", "emojiVersion", "u", "fn", "emojioneArea", "defaults", "attributes", "dir", "spellcheck", "autocomplete", "autocorrect", "autocapitalize", "search", "placeholder", "emojiPlaceholder", "searchPlaceholder", "container", "hideSource", "shortnames", "sprite", "pickerPosition", "filtersPosition", "searchPosition", "hidePickerOnBlur", "buttonTitle", "tones", "tonesStyle", "inline", "saveEmojisAs", "shortcuts", "autocompleteTones", "standalone", "useInternalCDN", "recentEmojis", "textcomplete", "maxCount", "placement", "filters", "title", "emoji", "recent", "icon", "smileys_people", "animals_nature", "food_drink", "activity", "travel_places", "objects", "symbols", "flags", "F", "isEmptyObject", "extend", "d", "B", "shortnameToUnicode", "emojiTemplate", "D", "RegExp", "toShort", "O", "this", "editor", "offsetWidth", "clientWidth", "parseInt", "button", "css", "marginRight", "floatingPicker", "picker", "right", "I", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eq", "is", "offset", "top", "height", "attr", "data", "removeClass", "filter", "N", "L", "class", "call", "arguments", "isFunction", "appendTo", "U", "localStorage", "getItem", "$", "scrollArea", "scrollTop", "recentCategory", "show", "emojiBtnTemplate", "split", "join", "children", "remove", "insertAfter", "on", "trigger", "recentFilter", "hasClass", "next", "addClass", "hide", "setStart", "startContainer", "startOffset", "setEnd", "endContainer", "endOffset", "body", "createTextRange", "moveToElementText", "start<PERSON><PERSON><PERSON>er", "select", "f", "defaultBase", "defaultBase3", "base", "isLoading", "h", "ajax", "url", "dataType", "cache", "success", "emojiSize", "createStyleSheet", "rel", "href", "shift", "imagePathSVGSprites", "regUnicode", "unicodeRegexp", "p", "b", "w", "k", "options", "emojiTemplateAlt", "setItem", "removeItem", "source", "disable", "append", "data-skin", "role", "contenteditable", "tabindex", "searchSel", "data-tone", "prependTo", "data-filter", "wrapInner", "name", "html", "text", "find", "setText", "getText", "click", "resize", "mousedown", "paste", "stayFocused", "mouseup", "keyup", "keydown", "keypress", "change", "focus", "blur", "stop", "animate", "hidePicker", "showPicker", "Date", "getTime", "originalEvent", "clipboardData", "getData", "preventDefault", "returnValue", "stopPropagation", "position", "left", "width", "overflow", "setTimeout", "indexOf", "splice", "unshift", "pop", "target", "test", "content", "val", "ctrl<PERSON>ey", "which", "events", "onKeydown", "KEY_ENTER", "map", "sort", "match", "template", "index", "option", "disabled", "off", "navigator", "userAgent", "execCommand", "isReady", "prototype", "concat", "setFocus", "_sh_timer", "clearTimeout", "enable", "prop", "parent", "emojioneAreaText"], "mappings": "AAACA,OAAS,aAAe,OAAOC,OAASA,OAAS,aAAe,OAAOC,KAAOA,KAAO,aAAe,OAAOF,OAASA,OAAS,GAC5HG,SAAWH,OAAOG,UAAY,GAC/B,SAAWC,EAAGC,GACb,YAAc,OAAOC,SAAW,UAAY,OAAOC,SAAW,UAAY,OAAOC,OAASJ,EAAEE,QAAQ,QAAQ,CAAC,EAAI,YAAc,OAAOG,QAAUA,OAAOC,IAAMD,OAAO,CAAC,UAAWL,CAAC,EAAIA,EAAEC,EAAEM,MAAM,CAC/L,EAAE,SAAUC,GACZ,aACA,IAAIC,EAAI,EACPC,EAAI,GACJC,EAAI,GACJC,EAAIhB,OAAOiB,SACXC,EAAI,GACL,SAASC,EAAEf,GACVY,EAAIZ,EAAE,EAAIc,EAAEE,KAAKhB,CAAC,CACnB,CACA,IAAIiB,EACHC,EACAC,EACAC,EAAI,6FACJC,EAAI,GAAGC,MACPC,EAAI,eACJC,EAAI,EACJC,EAAI,UACL,SAASC,EAAEC,EAAG3B,EAAGS,GAChB,IAAIK,EAAI,CAAA,EACPb,EAAI,EACL,GAAID,EAAG,CACNA,EAAIA,EAAE4B,YAAY,EAClB,EAAG,CACF,IAAIb,EAAI,GAAKd,EAAI,IAAMD,EAAIA,EAC3BU,EAAEiB,EAAEE,IAAId,IACPL,EAAEiB,EAAEE,IAAId,GAAGe,QACXtB,EAAEuB,KAAKrB,EAAEiB,EAAEE,IAAId,GAAI,SAAUf,EAAGC,GAC/B,OAAQa,EAAI,CAAA,IAAOb,EAAE+B,MAAML,EAAGlB,GAAK,EAAE,CACtC,CAAC,CACH,OAASK,GAAKb,CAAC,GAChB,CACA,OAAOa,CACR,CACA,SAASmB,EAAEN,EAAGlB,EAAGK,EAAGC,GAClBA,EACAA,GACA,SAAUf,EAAGC,GACZ,OAAOO,EAAEP,EAAEiC,aAAa,CACzB,EACA1B,EAAEuB,KAAKjB,EAAG,SAAUd,EAAGC,GACrBD,EAAIQ,EAAE2B,QAAQrB,CAAC,EAAIb,EAAID,GAAKW,EAAEgB,EAAEE,IAAI5B,KAAOU,EAAEgB,EAAEE,IAAI5B,GAAK,KAAKe,KAAK,CAACP,EAAGT,EAAGe,EAAE,CAC7E,CAAC,CACH,CACA,SAASqB,EAAEpC,EAAGC,EAAG0B,GAChB,IAAIlB,EAAIG,EAAEyB,UACTvB,EAAI,OAASL,EAAIG,EAAE0B,aAAe1B,EAAE2B,aACpCxB,EAAI,GAQDL,GAPJiB,IACEZ,EAAIY,EACHa,OAAO,EAAGb,EAAEG,OAAS,CAAC,EACtBW,QAAQ,KAAM,GAAG,EACjBA,QAAQ,SAAU,SAAUzC,GAC5B,OAAOA,EAAE0C,OAAO,CAAC,EAAEC,YAAY,EAAI3C,EAAEwC,OAAO,CAAC,EAAEZ,YAAY,CAC5D,CAAC,GACK,IACR,OACC3B,EAAE2C,SAAW,EAAIpB,GAAMd,EAAIT,EAAE2C,QAAW3C,EAAIA,EAAE4C,UAAUF,YAAY,GAAOjC,EAAIT,EAC/ED,EACEyC,QAAQ,SAAUd,GAAK,EAAE,EACzBc,QAAQ,iBAAkB1B,CAAC,EAC3B0B,QAAQ,QAAS3B,GAAKU,EAAI,EAAId,EAAEiC,YAAY,EAAIjC,GAAK,IAAMD,CAAC,EAC5DgC,QAAQ,QAASxC,CAAC,EAClBwC,QAAQ,QAAS7B,EAAEkC,QAAQ7C,CAAC,CAAC,CAEjC,CACA,SAAS8C,EAAE/C,EAAG2B,EAAGlB,GAChB,OAAOT,EAAEyC,QAAQ,mBAAoB,SAAUzC,GAC9CA,EAAI,IAAMA,EAAEyC,QAAQ,KAAM,EAAE,EAAEA,QAAQ,KAAM,EAAE,EAAI,IAClD,IAAIxC,EAAIW,EAAEoC,aAAahD,GACvB,OAAOC,EAAK,EAAIuB,EAAIY,EAAET,EAAG1B,EAAGD,CAAC,GAAK,EAAIwB,IAAMvB,EAAIA,EAAEgD,SAAUb,EAAET,EAAG1B,EAAEA,EAAE6B,OAAS,GAAI9B,CAAC,GAAMS,EAAI,GAAKT,CACnG,CAAC,CACF,CACA,SAASkD,EAAElD,GACV,IAAIC,EAAG0B,EACP,GAAI/B,OAAOuD,cACV,IAAKlD,EAAIL,OAAOuD,aAAa,GAAGC,YAAcnD,EAAEoD,WAAY,EAC1D1B,EAAI1B,EAAEmD,WAAW,CAAC,GAAGE,eAAe,EACrC,IAAI7C,EAAIV,SAASwD,cAAc,KAAK,EACpC9C,EAAE+C,UAAYxD,EACd,IAAK,IAAIc,EAAGC,EAAGL,EAAIX,SAAS0D,uBAAuB,EAAI3C,EAAIL,EAAEiD,YAAe3C,EAAIL,EAAEiD,YAAY7C,CAAC,EAC/Fa,EAAEiC,WAAWlD,CAAC,EAAGK,KAAOY,EAAIA,EAAEkC,WAAW,GAAGC,cAAc/C,CAAC,EAAGY,EAAEoC,SAAS,CAAA,CAAE,EAAG9D,EAAE+D,gBAAgB,EAAG/D,EAAEgE,SAAStC,CAAC,EAChH,CAAA,MACM5B,SAASmE,WAAa,WAAanE,SAASmE,UAAUC,MAAQpE,SAASmE,UAAUE,YAAY,EAAEC,UAAUrE,CAAC,CAClH,CACA,SAASsE,IACR,OAAO1E,OAAO2E,iBAAmB,OAClC,CACA,SAASC,EAAExE,GACV,MAAO,UAAY,OAAOA,CAC3B,CACA,SAASyE,EAAEzE,GACV,IAAIC,EACJ,OAAOD,EAAE0E,gBAAmBzE,EAAID,EAAE0E,eAAiBF,EAAExE,EAAE2E,WAAW,EAAK,aAAe1E,EAAI,QAAU,aAAeA,EAAI,QAAU,aAAeA,EAAI,QAAU,aAAeA,EAAI,QAAU,aAAeA,EAAI,QAAU,aAAeA,EAAI,QAAU,QAAW,SAAWD,EAAE4E,YAC9Q,CACA,SAASC,EAAE7E,GACV,OAAQA,GACP,IAAK,QACJ,OAAO,EACR,IAAK,QACJ,OAAO,EACR,IAAK,QACL,IAAK,QACJ,OAAO,EACR,IAAK,QACJ,OAAO,EACR,IAAK,QACL,IAAK,QACL,IAAK,QACJ,OAAO,EACR,IAAK,QACL,IAAK,QACL,IAAK,QACL,IAAK,MACJ,OAAO,EAKR,QACC,OAAO,CACT,CACD,CACA,SAAS2B,IACR,IACI3B,EACHC,EAFD,OAAIO,EAAEsE,GAAGC,cAAgBvE,EAAEsE,GAAGC,aAAaC,SAAiBxE,EAAEsE,GAAGC,aAAaC,UAC1EhF,EAAI,CAAEiF,WAAY,CAAEC,IAAK,MAAOC,WAAY,CAAA,EAAIC,aAAc,MAAOC,YAAa,MAAOC,eAAgB,KAAM,EAAGC,OAAQ,CAAA,EAAIC,YAAa,KAAMC,iBAAkB,WAAYC,kBAAmB,SAAUC,UAAW,KAAMC,WAAY,CAAA,EAAIC,WAAY,CAAA,EAAIC,OAAQ,CAAA,EAAIC,eAAgB,MAAOC,gBAAiB,MAAOC,eAAgB,MAAOC,iBAAkB,CAAA,EAAIC,YAAa,yCAA0CC,MAAO,CAAA,EAAIC,WAAY,SAAUC,OAAQ,KAAMC,aAAc,UAAWC,UAAW,CAAA,EAAIpB,aAAc,CAAA,EAAIqB,kBAAmB,CAAA,EAAIC,WAAY,CAAA,EAAIC,eAAgB,CAAA,EAAItE,UAAW,MAAOuE,aAAc,CAAA,EAAIC,aAAc,CAAEC,SAAU,GAAIC,UAAW,IAAK,CAAE,EACvpB9G,EAAI4E,EAAEjE,EAAI6D,EAAE7D,CAAC,EAAI0D,EAAE,CAAC,EACbtE,EAAEgH,QAAU,EAAI/G,EAAI,CAAEmG,MAAO,CAAEa,MAAO,YAAaC,MAAO,02EAA22E,EAAGC,OAAQ,CAAEC,KAAM,SAAUH,MAAO,SAAUC,MAAO,EAAG,EAAGG,eAAgB,CAAED,KAAM,MAAOH,MAAO,mBAAoBC,MAAO,g3HAAi3H,EAAGI,eAAgB,CAAEF,KAAM,UAAWH,MAAO,mBAAoBC,MAAO,8/CAA+/C,EAAGK,WAAY,CAAEH,KAAM,QAASH,MAAO,eAAgBC,MAAO,43BAA63B,EAAGM,SAAU,CAAEJ,KAAM,aAAcH,MAAO,WAAYC,MAAO,ioCAAkoC,EAAGO,cAAe,CAAEL,KAAM,SAAUH,MAAO,kBAAmBC,MAAO,qzCAAszC,EAAGQ,QAAS,CAAEN,KAAM,OAAQH,MAAO,UAAWC,MAAO,0sDAA2sD,EAAGS,QAAS,CAAEP,KAAM,aAAcH,MAAO,UAAWC,MAAO,4+FAA6+F,EAAGU,MAAO,CAAER,KAAM,UAAWH,MAAO,QAASC,MAAO,uoEAAwoE,CAAE,EAAI,CAAEd,MAAO,CAAEa,MAAO,YAAaC,MAAO,6mBAA8mB,EAAGC,OAAQ,CAAEC,KAAM,SAAUH,MAAO,SAAUC,MAAO,EAAG,EAAGG,eAAgB,CAAED,KAAM,MAAOH,MAAO,mBAAoBC,MAAO,qhEAAshE,EAAGI,eAAgB,CAAEF,KAAM,UAAWH,MAAO,mBAAoBC,MAAO,+4CAAg5C,EAAGK,WAAY,CAAEH,KAAM,QAASH,MAAO,eAAgBC,MAAO,wjBAAyjB,EAAGM,SAAU,CAAEJ,KAAM,aAAcH,MAAO,WAAYC,MAAO,wkBAAykB,EAAGO,cAAe,CAAEL,KAAM,SAAUH,MAAO,kBAAmBC,MAAO,ywCAA0wC,EAAGQ,QAAS,CAAEN,KAAM,OAAQH,MAAO,UAAWC,MAAO,+wDAAgxD,EAAGS,QAAS,CAAEP,KAAM,aAAcH,MAAO,UAAWC,MAAO,27FAA47F,EAAGU,MAAO,CAAER,KAAM,UAAWH,MAAO,QAASC,MAAO,qyBAAsyB,CAAE,EAAIlH,EACv69B,CACA,SAAS6H,EAAE7H,GACV,IAAIS,EACHR,EAAI0B,EAAE,EACP,OACC3B,GACCA,EAAEgH,UACAvG,EAAIR,EAAE+G,QACRxG,EAAEuB,KAAK/B,EAAEgH,QAAS,SAAUrF,EAAG3B,GAC9B,MAAO,CAACwE,EAAExE,CAAC,GAAKQ,EAAEsH,cAAc9H,CAAC,EAC9B,KAAK,OAAOS,EAAEkB,GACd,KAAKnB,EAAEuB,KAAK/B,EAAG,SAAUA,EAAGC,GAC5BQ,EAAEkB,GAAG3B,GAAKC,CACV,CAAC,CACL,CAAC,EACAD,EAAEgH,QAAUvG,GACdD,EAAEuH,OAAO,GAAI9H,EAAGD,CAAC,CAEnB,CACA,SAASgI,EAAEhI,EAAG2B,GACb,OAAO3B,EAAEyC,QAAQtB,EAAG,SAAUnB,GAC7B,IAAIC,EAAIW,EAAE,IAAMY,EAAI,aAAe,eACnC,OAAO,KAAA,IAAWxB,GAAKA,KAAKC,EAAImC,EAAET,EAAG1B,EAAED,EAAE,EAAIA,CAC9C,CAAC,CACF,CACA,SAASiI,EAAEjI,EAAGC,GACb,OACED,EAAIA,EACHyC,QAAQ,KAAM,OAAO,EACrBA,QAAQ,KAAM,MAAM,EACpBA,QAAQ,KAAM,MAAM,EACpBA,QAAQ,KAAM,QAAQ,EACtBA,QAAQ,KAAM,QAAQ,EACtBA,QAAQ,KAAM,QAAQ,EACtBA,QAAQ,kBAAmB,IAAI,EAC/BA,QAAQ,SAAU,eAAe,EACjCA,QAAQ,MAAO,OAAO,EACtBA,QAAQ,iBAAkB,QAAQ,EACpCuF,EAAGhI,EAAIC,EAAE4F,WAAajF,EAAEsH,mBAAmBlI,CAAC,EAAIA,EAAIC,EAAEkI,aAAa,EACjE1F,QAAQ,MAAO,0BAA0B,EACzCA,QAAQ,MAAO,cAAc,CAEjC,CACA,SAAS2F,EAAEpI,EAAGC,GACb,OACGD,EAAIA,EACJyC,QAAQ,SAAU,IAAI,EACtBA,QAAQ,SAAU,IAAI,EACtBA,QAAQ,iCAAkC,IAAI,EAC9CA,QAAQ,SAAU,EAAE,EACpBA,QAAQ,cAAe,IAAI,EAC3BA,QAAQ,mDAAoD,OAAO,EACnEA,QAAQ,gDAAiD,QAAQ,EACjEA,QAAQ,mBAAoB,IAAI,EAChCA,QAAQ,mBAAoB,IAAI,EAChCA,QAAQ,sBAAuB,IAAI,EACnCA,QAAQ,wBAAyB,MAAM,EACvCA,QAAQ,iBAAkB,QAAQ,EAClCA,QAAQ,0BAA2B,QAAQ,EAC3CA,QAAQ,YAAa,EAAE,EACvBA,QAAQ,iBAAkB,MAAM,EAChCA,QAAQ,YAAa,IAAI,EACzBA,QAAQ,YAAa,MAAM,EAC3BA,QAAQ,gBAAiB,EAAE,EAC3BA,QAAQ,IAAI4F,OAAO5G,EAAG,GAAG,EAAG,EAAE,EAC9BgB,QAAQ,UAAW,GAAG,EACtBA,QAAQ,QAAS,GAAG,EACpBA,QAAQ,QAAS,GAAG,EACpBA,QAAQ,UAAW,GAAG,EACtBA,QAAQ,UAAW,GAAG,EACtBA,QAAQ,UAAW,GAAG,EACtBA,QAAQ,SAAU,GAAG,EACrBA,QAAQ,SAAU,GAAG,EACrBA,QAAQ,SAAU,GAAG,EACvBxC,EAAEsG,cAEF,IAAK,QACJvG,EAAIgI,EAAEhI,EAAGC,EAAEkI,aAAa,EACxB,MACD,IAAK,YACJnI,EAAIY,EAAE0H,QAAQtI,CAAC,CACjB,CACA,OAAOA,CACR,CACA,SAASuI,IACR,IAAIvI,EAAIwI,KACPvI,EAAID,EAAEyI,OAAO,GAAGC,YAAc1I,EAAEyI,OAAO,GAAGE,YAC1ChH,EAAIiH,SAAS5I,EAAE6I,OAAOC,IAAI,aAAa,CAAC,EACzCnH,IAAM1B,IAAMD,EAAE6I,OAAOC,IAAI,CAAEC,YAAa9I,CAAE,CAAC,EAAGD,EAAEgJ,iBAAkBhJ,EAAEiJ,OAAOH,IAAI,CAAEI,MAAON,SAAS5I,EAAEiJ,OAAOH,IAAI,OAAO,CAAC,EAAInH,EAAI1B,CAAE,CAAC,CAClI,CACA,SAASkJ,IACR,IAAIxH,EACHlB,EACAT,EAAIwI,KACL,CAACxI,EAAE8F,QACF9F,EAAEoJ,UAAU,IACZpJ,EAAEoJ,UAAUC,GAAG,CAAC,EAAEC,GAAG,aAAa,IAChC3H,EAAI3B,EAAEiJ,OAAOM,OAAO,EAAEC,IACvB/I,EAAIkB,EAAI3B,EAAEiJ,OAAOQ,OAAO,EAAI,GAC7BzJ,EAAEoJ,UAAUrH,KAAK,WAChB,IAAI/B,EAAIQ,EAAEgI,IAAI,EACbvI,EAAID,EAAEuJ,OAAO,EAAEC,IAChB,GAAK7H,EAAI1B,GAAKA,EAAIQ,GAAKT,EAAE0J,KAAK,MAAO1J,EAAE2J,KAAK,KAAK,CAAC,EAAEC,YAAY,YAAY,EAAGnJ,EAAIR,EAAI,MAAO,CAAA,CAC/F,CAAC,EACAD,EAAEoJ,UAAYpJ,EAAEoJ,UAAUS,OAAO,aAAa,EACjD,CACA,SAASC,EAAE9J,EAAGC,GACb,OAAQA,EAAI,GAAK,KAAOsB,GAAKvB,EAAI,IAAMA,EAAI,GAC5C,CACA,SAAS+J,EAAE/J,GACV,IAAI2B,EAAInB,EAAE,SAAUgE,EAAExE,CAAC,EAAIA,EAAI,CAAEgK,MAAOF,EAAE9J,EAAG,CAAA,CAAE,CAAE,CAAC,EAClD,OACCQ,EAAEuB,KAAKV,EAAE4I,KAAKC,SAAS,EAAE5I,MAAM,CAAC,EAAG,SAAUtB,EAAGC,IAC9CA,EAAIO,EAAE2J,WAAWlK,CAAC,EAAIA,EAAEgK,KAAKtI,CAAC,EAAI1B,IAAMO,EAAEP,CAAC,EAAEmK,SAASzI,CAAC,CACzD,CAAC,EACDA,CAEF,CACA,SAAS0I,IACR,OAAOC,aAAaC,QAAQ,eAAe,GAAK,EACjD,CACA,SAASC,EAAExK,EAAGC,GACb,IAAI0B,EACHlB,EACAK,EACAC,EAAIsJ,EAAE,EACNrK,EAAEmH,QAAUnH,EAAEmH,SAAWpG,GAAK,CAACd,IAC9Bc,EAAEe,SACEhB,EAAId,EAAEyK,WAAWnB,GAAG,YAAY,KAAQ3H,EAAI3B,EAAEyK,WAAWC,UAAU,EAAIzK,GAAKD,EAAE2K,eAAeC,KAAK,EAAInK,EAAIT,EAAE2K,eAAerB,GAAG,UAAU,EAAItJ,EAAE2K,eAAelB,OAAO,EAAI,GACzKxJ,EAAI8C,EAAEhC,EAAGf,EAAE6K,iBAAkB,CAAA,CAAE,EAAEC,MAAM,GAAG,EAAEC,KAAK,EAAE,EACpD/K,EAAE2K,eAAeK,SAAS,WAAW,EAAEC,OAAO,EAC9CzK,EAAEP,CAAC,EAAEiL,YAAYlL,EAAE2K,eAAeK,SAAS,8BAA8B,CAAC,EAC1EhL,EAAE2K,eAAeK,SAAS,WAAW,EAAEG,GAAG,QAAS,WACnDnL,EAAEoL,QAAQ,iBAAkB5K,EAAEgI,IAAI,CAAC,CACnC,CAAC,EACDxI,EAAEqL,aAAaT,KAAK,EACpB9J,IAAMd,EAAE2K,eAAeC,KAAK,EAAGnK,KAAOK,EAAId,EAAE2K,eAAelB,OAAO,IAAMzJ,EAAEyK,WAAWC,UAAU/I,EAAIb,EAAIL,CAAC,KACvGT,EAAEqL,aAAaC,SAAS,QAAQ,GAAKtL,EAAEqL,aAAazB,YAAY,QAAQ,EAAE2B,KAAK,EAAEC,SAAS,QAAQ,EAAGxL,EAAE2K,eAAec,KAAK,EAAGzL,EAAEqL,aAAaI,KAAK,GACrJzL,EAAEmH,OAASpG,EACd,CAuUAnB,OAAOuD,cAAgBpD,SAASqE,aAC3BnD,EAAI,SAAUjB,GAChB,IAAIC,EAAIL,OAAOuD,cAAgBvD,OAAOuD,aAAa,EACnD,GAAIlD,GAAK,EAAIA,EAAEoD,WAAY,OAAOpD,EAAEmD,WAAW,CAAC,CAChD,EACClC,EAAI,SAAUlB,EAAGC,GAClB,IAAI0B,EAAI5B,SAASqE,YAAY,EAC7BzC,EAAE+J,SAASzL,EAAE0L,eAAgB1L,EAAE2L,WAAW,EAAGjK,EAAEkK,OAAO5L,EAAE6L,aAAc7L,EAAE8L,SAAS,GAAI9L,EAAIL,OAAOuD,aAAa,GAAGa,gBAAgB,EAAG/D,EAAEgE,SAAStC,CAAC,CAC/I,GACA5B,SAASmE,WACTnE,SAASiM,KAAKC,kBACZhL,EAAI,SAAUjB,GAChB,OAAOD,SAASmE,UAAUE,YAAY,CACtC,EACClD,EAAI,SAAUlB,EAAGC,GAClB,IAAI0B,EAAI5B,SAASiM,KAAKC,gBAAgB,EACtCtK,EAAEuK,kBAAkBlM,CAAC,EAAG2B,EAAE+J,SAASzL,EAAEkM,eAAgBlM,EAAE2L,WAAW,EAAGjK,EAAEkK,OAAO5L,EAAE6L,aAAc7L,EAAE8L,SAAS,EAAGpK,EAAEyK,OAAO,CACrH,GACH,IAAIC,EAAI,CAAEC,YAAa,mDAAoDC,aAAc,4BAA6BC,KAAM,KAAMC,UAAW,CAAA,CAAG,EAChJ,SAASC,EAAEzM,GACV,IAAID,EACH2B,EAAI2C,EAAE,EACNrE,EAAI4H,EAAE5H,CAAC,EACPoM,EAAEI,YACA,CAAC7L,GAAKiE,EAAEJ,EAAE7D,CAAC,CAAC,EAAI,GACZyL,EAAEI,UAAY,CAAA,EACfzM,EAAI,EAAI6E,EAAElD,CAAC,EAAI0K,EAAEE,aAAe,gBAAkB5K,EAAI,EAAIkD,EAAElD,CAAC,EAAI0K,EAAEE,aAAe,YAAc5K,EAAI0K,EAAEC,YAAc,IAAM3K,EAC3HnB,EAAEmM,KAAK,CACPC,IAAK5M,EAAI,0BACT6M,SAAU,SACVC,MAAO,CAAA,EACPC,QAAS,WACR,IAAI/M,EACJ,IAAKY,EAAIhB,OAAOiB,SAAUc,EAAI8C,EAAE7D,CAAC,EAAGZ,EAAI,GAAKwB,EAAIqD,EAAElD,CAAC,IAAO0K,EAAEG,KAAOH,EAAEE,aAAe,mBAAqB5K,EAAI0K,EAAEG,KAAO,4BAA8B5L,EAAEoM,UAAY,SAAYX,EAAEG,KAAOH,EAAEC,YAAc3K,EAAI,UAAY0K,EAAEG,KAAO,iCAAkCvM,EAAE6F,SAAW/F,SAASkN,iBAAmBlN,SAASkN,iBAAiBjN,CAAC,EAAIQ,EAAE,UAAW,CAAE0M,IAAK,aAAcC,KAAMnN,CAAE,CAAC,EAAEoK,SAAS,MAAM,GAAItJ,EAAEgB,QAAUhB,EAAEsM,MAAM,EAAEnD,KAAK,EACtaoC,EAAEI,UAAY,CAAA,CACf,CACA,CAAC,IACC9K,EAAI8C,EAAE7D,CAAC,EAAKY,EAAIqD,EAAElD,CAAC,EAAK0K,EAAEG,KAAO,EAAIhL,EAAI6K,EAAEE,aAAe,mBAAqB5K,EAAI0K,EAAEC,YAAc3K,EAAI,YAC7GZ,EAAE,WACD,IAAIf,EAAI,GACRC,EAAE0G,iBAAmB,EAAInF,IAAMxB,EAAIY,EAAEoM,UAAY,KAAOpM,EAAE2B,aAAe8J,EAAEG,KAAO,QAAUxM,EAAKY,EAAE0B,aAAe+J,EAAEG,KAAO,QAAUxM,EAAKY,EAAEyM,oBAAsBhB,EAAEG,KAAO,gCAAmC5L,EAAEyB,UAAYpC,EAAEoC,WAAa,EAAIwC,EAAElD,CAAC,GAAMR,EAAIP,EAAE0M,WAAc1M,EAAEyB,UAAYpC,EAAEoC,WAAa,OAAWlB,EAAI,IAAIkH,OAAO,iGAAmGzH,EAAE2M,cAAgB,IAAK,IAAI,CAClc,CAAC,CACH,CACA,SAAStN,EAAED,EAAGC,GACb,IAAI0B,EAAI6G,KACRkE,EAAEzM,CAAC,EACDS,EAAGiB,EAAEE,GAAK,EAAEpB,GAAM,GAClBE,EAAEgB,EAAEE,IAAM,GACXd,EAAE,WAtXJ,IAAWJ,EAAGV,EAAGc,EAoBZf,EAEAc,EACHa,EACAjB,EACAS,EACAE,EACAZ,EACA2B,EACAkC,EACAG,EACAI,EACAmD,EACAwF,EASAnB,EAsCGK,EAsDAe,EACHC,EACAC,EAxIY1N,EAuXND,EAvXSe,EAuXNd,GAvXAU,EAuXNgB,GAtXDiM,QAAU7M,EAAI8G,EAAE9G,CAAC,EAClBJ,EAAEmF,OAAS/E,EAAE+E,QAAUtE,EAAI,EAC3Bb,EAAE2F,OAAS,OAASvF,EAAEuF,OAASrG,EAAEqJ,GAAG,OAAO,EAAIvI,EAAEuF,OACjD3F,EAAEkF,WAAa9E,EAAE8E,WACjBlF,EAAE4F,aAAexF,EAAEwF,aACnB5F,EAAE+F,WAAa3F,EAAE2F,WACjB/F,EAAEwH,cAAgB,oCAAsCxH,EAAEmF,OAAS,gBAAkB1E,EAAI,MAAQ,wBACjGT,EAAEkN,iBAAmBlN,EAAEmF,OAAS,8BAAgC,2CAChEnF,EAAEkK,iBAAmB,+EAAiFlK,EAAEkN,iBAAmB,OAC3HlN,EAAEiG,aACF7F,EAAE6F,cACF,WACC,IAAI5G,EAAI,OACR,IACC,OAAOsK,aAAawD,QAAQ9N,EAAGA,CAAC,EAAGsK,aAAayD,WAAW/N,CAAC,EAAG,CAAA,CAGhE,CAFE,MAAOA,GACR,MAAO,CAAA,CACR,CACA,EAAE,EACDA,EAAIe,EAAEgF,eACTpF,EAAEqI,eAAiB,QAAUhJ,GAAK,WAAaA,IAAMW,EAAEqN,OAAS/N,GAAGqJ,GAAG,WAAW,GAAKrJ,EAAEqJ,GAAG,WAAW,IAAM3I,EAAEsN,QAAQ,EAWtHjG,EAAI/H,EAAEqJ,GAAG,UAAU,GAAKrJ,EAAEqJ,GAAG,OAAO,EAAI,MAAQ,OAChDkE,EAAIzD,EACH,QACAhJ,EAAEqF,MACC,WACAoC,KAAKgD,SAAS1B,EAAE,SAAW/I,EAAEsF,WAAY,CAAA,CAAE,CAAC,EAC5C,IAAK,IAAIrG,EAAI,EAAGA,GAAK,EAAGA,CAAC,GAAIwI,KAAK0F,OAAO1N,EAAE,OAAQ,CAAEwJ,MAAO,qBAAuBhK,GAAKA,EAAI,GAAK,WAAYmO,YAAanO,EAAGoO,KAAM,QAAS,CAAC,CAAC,CAC9I,EACA,IACJ,EACA/B,EAAItC,EACH,CAAEC,MAAOzI,GAAKZ,EAAE+F,WAAa,IAAMnF,EAAI,eAAiB,MAAQtB,EAAEyJ,KAAK,OAAO,GAAK,IAAK0E,KAAM,aAAc,EAC3GtN,EAAIH,EAAE8H,OAASsB,EAAE,QAAQ,EAAEL,KAAK,CAAE2E,gBAAiB,CAAC1N,EAAE+F,WAAYlB,YAAazE,EAAEyE,aAAevF,EAAE0J,KAAK,aAAa,GAAK1J,EAAEyJ,KAAK,aAAa,GAAK,GAAI4E,SAAU,CAAE,CAAC,EACnK3M,EAAIhB,EAAEkI,OAASkB,EAAE,SAAUA,EAAE,aAAa,EAAGA,EAAE,cAAc,CAAC,EAAEL,KAAK,QAAS3I,EAAEoF,WAAW,EAC3FzF,EAAIC,EAAEsI,OACNc,EACC,SACAA,EACC,UACC5I,EAAI4I,EAAE,SAAS,EAChBhJ,EAAEwE,OACE9E,EAAIsJ,EACL,eACAA,EACC,SACAhJ,EAAEwE,OACC,WACC5E,EAAE4E,OAAS/E,EAAE,WAAY,CAAEgF,YAAazE,EAAE2E,mBAAqB,GAAIvB,KAAM,OAAQ6F,MAAO,QAAS,CAAC,EAAIxB,KAAK0F,OAAOvN,EAAE4E,MAAM,CAC3H,EACA,IACJ,EACAiI,CACA,EACA,KACF3I,EAAIkF,EAAE,cAAehJ,EAAEqF,OAAS,CAACrF,EAAEwE,OAASwE,EAAE,cAAeyD,CAAC,EAAI,KAAOpL,EAAI2H,EAAE,aAAa,CAAE,CAChG,CACD,EACEyB,SAAS1B,EAAE,mBAAqB/I,EAAEgF,eAAgB,CAAA,CAAE,CAAC,EACrDyF,SAAS1B,EAAE,oBAAsB/I,EAAEiF,gBAAiB,CAAA,CAAE,CAAC,EACvDwF,SAAS1B,EAAE,mBAAqB/I,EAAEkF,eAAgB,CAAA,CAAE,CAAC,EACrDuF,SAAS,QAAQ,CACrB,EACDzK,EAAEwE,QAAU9E,EAAE+K,SAAS1B,EAAE,cAAe,CAAA,CAAE,CAAC,EACzCnJ,EAAE4N,UAAY,KACfzN,EAAE6I,KAAK1J,EAAE0J,KAAK,CAAC,EACfnJ,EAAEuB,KAAKhB,EAAEkE,WAAY,SAAUjF,EAAGC,GACjCa,EAAE4I,KAAK1J,EAAGC,CAAC,CACZ,CAAC,EACEyM,EAAI3C,EAAE,gBAAgB,EAAEL,KAAK,CAAE8E,YAAa,CAAE,CAAC,EAAEC,UAAUrM,CAAC,EAChE5B,EAAEuB,KAAKhB,EAAEiG,QAAS,SAAUhH,EAAGC,GAC9B,IAAI0B,EAAI,EACR,GAAI,WAAa3B,GAAKW,EAAEiG,aAAc,CACrC,GAAI,UAAY5G,EACfQ,EAAE,OAAQ,CAAEwJ,MAAOF,EAAE,SAAU,CAAA,CAAE,EAAI,IAAMA,EAAE,UAAY9J,EAAG,CAAA,CAAE,EAAG0O,cAAe1O,EAAGiH,MAAOhH,EAAEgH,KAAM,CAAC,EACjG0H,UAAU5L,EAAE9C,EAAEmH,KAAMzG,EAAEkN,gBAAgB,CAAC,EACvCzD,SAASjJ,CAAC,MACR,CACJ,GAAI,CAACJ,EAAEqF,MAAO,OACdzE,EAAI,CACL,CACA,GACC,IAAIlB,EAAIR,EAAEiH,MAAMzE,QAAQ,WAAY,GAAG,EACtC3B,EAAI,IAAMa,EAAIoI,EAAE,UAAU,EAAEL,KAAK,CAAEkF,KAAM5O,EAAGwO,YAAa7M,CAAE,CAAC,EAAEyI,SAASsC,CAAC,EAAI3C,EAAE,gBAAgB,EAAEL,KAAK,CAAEkF,KAAM5O,EAAGwO,YAAa7M,CAAE,CAAC,EAAEyI,SAAShI,CAAC,CAAC,OAE7I,EAAIT,IAAMb,EAAE2K,KAAK,EAAIhL,EAAIA,EAAEqK,MAAM,GAAG,EAAEC,KAAK,QAAUpJ,EAAI,GAAG,EAAI,QAAUA,GAC1ElB,EAAIsC,EAAGtC,EAAI,WAAaT,EAAIqK,EAAE,EAAI5J,EAAIE,EAAEmF,OAAS,iHAAmH,2IAA4I,CAAA,CAAE,EACjTgF,MAAM,GAAG,EACTC,KAAK,EAAE,EACTjK,EAAE+N,KAAKpO,CAAC,EACRD,EAAE,4CAA4C,EAAEsO,KAAK7O,EAAEgH,KAAK,EAAEwH,UAAU3N,CAAC,EACzE,EAAI,EAAEa,EAER,CACD,CAAC,EACCZ,EAAEiG,QAAU,KACbrG,EAAEmF,SAAWnF,EAAEyI,UAAYhH,EAAE2M,KAAK,aAAa,IAC9C1N,EAAIF,EAAE4N,KAAKjF,EAAE,QAAQ,CAAC,GAAGT,GAAG,CAAC,EAAEmC,SAAS,QAAQ,EAChD/G,EAAIrC,EAAE2M,KAAKjF,EAAE,gBAAgB,CAAC,EAC9BxF,EAAIlC,EAAE2M,KAAKjF,EAAE,UAAU,CAAC,EACxBnJ,EAAE0K,aAAehK,EAAEwI,OAAO,wBAAwB,EAClDlJ,EAAEgK,eAAiBrG,EAAEuF,OAAO,eAAe,EAC3ClJ,EAAE8J,WAAa5F,EAChB9D,EAAE4E,UAAYnF,EAAEO,EAAE4E,SAAS,EAAEgJ,UAAUtC,CAAC,EAAIA,EAAEnB,YAAYjL,CAAC,EAC3Dc,EAAE6E,YAAc3F,EAAEwL,KAAK,EACvB9K,EAAEqO,QAAQ/O,EAAE+H,GAAG,CAAC,EAChB/H,EAAE+H,GAAGrH,EAAEsO,QAAQ,CAAC,EAChB1G,EAAEvG,MAAMrB,CAAC,EACTA,EAAE+F,YAAc,CAAC/F,EAAEsO,QAAQ,EAAEnN,SAAY4L,EAAIlN,EAAEP,CAAC,EAAE0J,KAAK,mBAAmB,GAAK5I,EAAE0E,iBAAmB9E,EAAEqO,QAAQtB,CAAC,EAAG5M,EAAE0K,SAAS,iBAAiB,GAC9IvJ,EAAEtB,EAAGyB,EAAE2M,KAAK,WAAW,EAAG,CAAEG,MAAO,gBAAiB,CAAC,EACrDjN,EAAEtB,EAAGf,OAAQ,CAAEuP,OAAQ,SAAU,CAAC,EAClClN,EAAEtB,EAAG6M,EAAExC,SAAS,EAAG,CAAEkE,MAAO,YAAa,CAAC,EAC1CjN,EAAEtB,EAAG,CAACD,EAAGiB,GAAI,CAAEyN,UAAW,YAAa,EAAGtO,CAAC,EAC3CmB,EAAEtB,EAAGgB,EAAG,CAAEuN,MAAO,cAAe,CAAC,EACjCjN,EAAEtB,EAAGG,EAAG,CAAEuO,MAAO,QAAS,EAAGvO,CAAC,EAC9BmB,EAAEtB,EAAGG,EAAG,CAAC,QAAS,QAAS,WAC1B,MAAO,CAACH,EAAE2O,aAAexO,CAC1B,CAAC,EACDmB,EAAEtB,EAAGD,EAAG,CAAE0O,UAAW,mBAAoBG,QAAS,iBAAkBL,MAAO,eAAgBM,MAAO,eAAgBC,QAAS,iBAAkBC,SAAU,iBAAkB,CAAC,EAC1KzN,EAAEtB,EAAGG,EAAG,CAAC,YAAa,UAAW,QAAS,QAAS,UAAW,WAAW,EACzEmB,EAAEtB,EAAGD,EAAEqO,KAAK,sBAAsB,EAAG,CAAEG,MAAO,cAAe,CAAC,EAC9DjN,EAAEtB,EAAGV,EAAG,CAAE0P,OAAQ,eAAgB,CAAC,EACnC5O,EAAEwE,QAAUtD,EAAEtB,EAAGA,EAAE4E,OAAQ,CAAEiK,MAAO,kBAAmBI,MAAO,eAAgBC,KAAM,aAAc,CAAC,EAGnGlC,EAAI,CAAA,EACL9I,EAAEsG,GAAG,SAAU,WACd,IAAIxJ,EAAGlB,EAAGT,EACV,CAAC2N,IACCxE,EAAEc,KAAKtJ,CAAC,EAAGkE,EAAEyE,GAAG,kBAAkB,KACjC3H,EAAI2C,EAAE+E,GAAG,CAAC,EACX5I,EAAIoE,EAAE0E,OAAO,EAAEC,IAChBlF,EAAEvC,KAAK,SAAU/B,EAAGC,GACnB,MAAO,EAAE,IAAMO,EAAEP,CAAC,EAAEsJ,OAAO,EAAEC,IAAM/I,IAAM,KAAMkB,EAAInB,EAAEP,CAAC,EACvD,CAAC,GACAD,EAAIqB,EAAEwI,OAAO,iBAAmBlI,EAAE+H,KAAK,MAAM,EAAI,IAAI,GAAG,IAAM,CAAC1J,EAAEsJ,GAAG,SAAS,IAAMjI,EAAEuI,YAAY,QAAQ,EAAG5J,EAAEwL,SAAS,QAAQ,GAClI,CAAC,EACA7K,EACEwK,GAAG,gBAAiB,SAAUnL,GAC9B,IAAIC,EAAID,EAAEsJ,GAAG,SAAS,EACtB,GAAIzE,EAAEyE,GAAG,YAAY,EAAG,CACvB,GAAIrJ,EAAG,OACPuN,EAAExC,SAAS,EAAE3B,GAAG,CAAC,EAAE6F,MAAM,CAC1B,CACCvB,EAAI,CAAA,EAAK1N,IAAMoB,EAAEwI,OAAO,SAAS,EAAED,YAAY,QAAQ,EAAG5J,EAAEwL,SAAS,QAAQ,GAC9E,IAAI7J,EAAI2C,EAAEuF,OAAO,UAAY7J,EAAE2J,KAAK,QAAQ,EAAI,IAAI,EAAEJ,OAAO,EAAEC,IAC9DvJ,EAAI4E,EAAE6F,UAAU,EAChB1K,EAAI6E,EAAE0E,OAAO,EAAEC,IAChB3E,EAAEiL,KAAK,EAAEC,QAAQ,CAAErF,UAAW/I,EAAI1B,EAAID,EAAI,CAAE,EAAG,IAAK,QAAS,WAC5DmJ,EAAEc,KAAKtJ,CAAC,EAAIgN,EAAI,CAAA,CACjB,CAAC,CACF,CAAC,EACAxC,GAAG,eAAgB,WACnBxK,EAAEiG,cAAgB4D,EAAE7J,CAAC,EAAGwI,EAAEc,KAAKtJ,CAAC,CACjC,CAAC,EACAwK,GAAG,cAAe,SAAUnL,GAC5BwN,EAAExC,SAAS,EAAEpB,YAAY,QAAQ,GACjC5J,EAAIA,EAAEwL,SAAS,QAAQ,EAAE7B,KAAK,MAAM,IAEhC9E,EAAE2G,SAAS,WAAW,EACvB/G,EACCgH,KAAK,EACL5B,OAAO,cAAgB7J,EAAI,GAAG,EAC9B4K,KAAK,EACNvJ,EAAEuI,YAAY,QAAQ,IACrB/E,EAAE+E,YAAY,WAAW,EAAGnF,EAAEgH,KAAK,EAAE5B,OAAO,eAAe,EAAEe,KAAK,EAAGvJ,EAAEgI,GAAG,CAAC,EAAE6F,MAAM,GACtF/F,EAAEc,KAAKtJ,CAAC,EACRI,EAAEwE,QAAU5E,EAAEyK,QAAQ,iBAAiB,CACzC,CAAC,EACAD,GAAG,gBAAiB,SAAUnL,GAC9BA,EAAEsJ,GAAG,SAAS,EAAI3I,EAAEqP,WAAW,GAAKrP,EAAEsP,WAAW,EAAItP,EAAE4N,UAAY,KACpE,CAAC,EACApD,GAAG,UAAW,SAAUpK,EAAGf,GAC3B,SAASC,EAAED,GACV,IAAIC,EAAI,UAAW,IAAIiQ,MAAOC,QAAQ,EACrCxO,EAAIsG,EAAEjI,EAAGW,CAAC,EAEPF,GADJyC,EAAEvB,CAAC,EAAGuB,EAAE,UAAYjD,EAAI,QAAQ,EAAGc,EAAE2J,UAAUhK,CAAC,EACxCF,EAAE,IAAMP,CAAC,GAChBa,EAAIL,EAAE8I,OAAO,EAAEC,IAAMzI,EAAEwI,OAAO,EAAEC,MAC/BvJ,EAAIc,EAAE0I,OAAO,IAAM/I,EAAII,GAAKA,EAAIJ,IAAMK,EAAE2J,UAAUhK,EAAII,EAAK,EAAIb,EAAK,CAAC,EAAGQ,EAAEwK,OAAO,EAAItK,EAAE2O,YAAc,CAAA,EAAK/G,EAAEvG,MAAMrB,CAAC,EAAGe,EAAEf,EAAG,QAAS,CAACI,EAAGf,EAAG2B,EAAE,CAChJ,CACA,GAAI3B,EAAEoQ,cAAcC,cAEnB,OAAOpQ,EADCD,EAAEoQ,cAAcC,cAAcC,QAAQ,YAAY,CAChD,EAAGtQ,EAAEuQ,eAAiBvQ,EAAEuQ,eAAe,EAAIvQ,EAAE8P,KAAK,EAAI9P,EAAEwQ,YAAc,CAAA,EAAKxQ,EAAEyQ,gBAAgB,EAAG,CAAA,EAE1G9P,EAAE2O,YAAc,CAAA,EAAKpM,EAAE,SAAWzB,EAAI,SAAS,EAChD,IAAIhB,EAAIQ,EAAEF,EAAE,EAAE,EACbL,EAAIK,EAAE2J,UAAU,EAChB5J,EAAIN,EAAE,SAAU,CAAE6N,gBAAiB,CAAA,CAAG,CAAC,EAAEvF,IAAI,CAAE4H,SAAU,QAASC,KAAM,SAAUC,MAAO,MAAOnH,OAAQ,MAAOD,IAAK,OAAQqH,SAAU,QAAS,CAAC,EAAEzG,SAAS5J,EAAE,MAAM,CAAC,EAAEoP,MAAM,EAC7KhQ,OAAOkR,WAAW,WACjB/P,EAAE6O,MAAM,EAAG1O,EAAEH,EAAE,GAAIN,CAAC,EACpB,IAAIT,EAAIoI,EAAEtH,EAAE+N,KAAK,EAAEpM,QAAQ,cAAe,MAAM,EAAG9B,CAAC,EACpDG,EAAEmK,OAAO,EAAGhL,EAAED,CAAC,CAChB,EAAG,GAAG,CACP,CAAC,EACAmL,GAAG,kBAAmB,SAAUnL,GAChC,IAAIC,EAAG0B,EAAGlB,EACVK,EAAE8I,YAAY,iBAAiB,EAAG,OAASjJ,EAAE4N,YAAczN,EAAE8O,MAAM,EAAG1O,EAAEJ,EAAE,GAAIH,EAAE4N,SAAS,EAAI5N,EAAE4N,UAAY,MAAQ5N,EAAE+F,YAAc5F,EAAE+N,KAAK9L,EAAE/C,EAAE2J,KAAK,MAAM,EAAGhJ,EAAEwH,aAAa,CAAC,EAAGxH,EAAEyK,QAAQ,MAAM,IAAMnK,EAAEH,EAAE,EAAE,EAAGoC,EAAEH,EAAE/C,EAAE2J,KAAK,MAAM,EAAGhJ,EAAEwH,aAAa,CAAC,GAAIxH,EAAEiG,eAAkB3G,EAAIU,EAAKgB,EAAI3B,EAAE2J,KAAK,MAAM,EAA0B,CAAC,KAAO3J,GAA7BS,EAAI4J,EAAE,EAAES,MAAM,GAAG,GAAkBiG,QAAQpP,CAAC,IAAMlB,EAAEuQ,OAAOhR,EAAG,CAAC,EAAGS,EAAEwQ,QAAQtP,CAAC,EAAG,EAAIlB,EAAEqB,QAAUrB,EAAEyQ,IAAI,EAAG5G,aAAawD,QAAQ,gBAAiBrN,EAAEsK,KAAK,GAAG,CAAC,EAAGP,EAAEvK,CAAC,GAAIU,EAAEyK,QAAQ,iBAAiB,CACze,CAAC,EACAD,GAAG,kCAAmC5C,CAAC,EACvC4C,GAAG,cAAe,SAAUnL,EAAGC,GAC/B,OAAOO,EAAEP,EAAEkR,MAAM,EAAE7F,SAAS,QAAQ,GAAM3K,EAAE2O,YAAc,CAAA,EAAK,OAAS3O,EAAE4N,YAAc5N,EAAE4N,UAAYtN,EAAEjB,EAAE,EAAE,KAAOqM,EAAE/C,GAAG,UAAU,GAAKtJ,EAAEoL,QAAQ,OAAO,EAAGnL,EAAEsQ,eAAe,GAAI,CAAA,CACjL,CAAC,EACApF,GAAG,UAAW,WACd,IAAInL,EAAIW,EAAE8H,OAAOoG,KAAK,EAAEpM,QAAQ,6BAA8B,EAAE,EAC/DzC,EAAE8B,QAAU,CAAC,eAAesP,KAAKpR,CAAC,GAAMW,EAAE8H,OAAOoG,KAAMlO,EAAE0Q,QAAU,EAAG,EAAGpR,EAAE+H,GAAGrH,EAAEsO,QAAQ,CAAC,CAC3F,CAAC,EACA9D,GAAG,iBAAkB,WACrBxK,EAAEqO,QAAQ/O,EAAE+H,GAAG,CAAC,EAAGtG,EAAE,QAAQ,CAC9B,CAAC,EACAyJ,GAAG,SAAU,WACbkB,EAAEb,SAAS,SAAS,CACrB,CAAC,EACAL,GAAG,QAAS,WACZkB,EAAEzC,YAAY,SAAS,EAAG7I,EAAEmF,kBAAoBvF,EAAEqP,WAAW,EAC7D,IAAIhQ,EAAIW,EAAE8H,OAAOoG,KAAK,EACtBlO,EAAE0Q,UAAYrR,GAAMW,EAAE0Q,QAAUrR,EAAI0B,EAAEf,EAAG,SAAU,CAACA,EAAE8H,OAAO,EAAGxI,EAAEmL,QAAQ,MAAM,EAAEA,QAAQ,QAAQ,GAAKnL,EAAEmL,QAAQ,MAAM,EAAGrK,EAAEwE,SAAW5E,EAAE4E,OAAO+L,IAAI,EAAE,EAAG3Q,EAAEyK,QAAQ,kBAAmB,CAAA,CAAE,EACzL,CAAC,EACFrK,EAAEwE,QACD5E,EACEwK,GAAG,gBAAiB,WACnBxK,EAAE2O,YAAc,CAAA,EAAK3O,EAAE4E,OAAOiG,SAAS,SAAS,CAClD,CAAC,EACAL,GAAG,mBAAoB,SAAUnL,GACjC,IAAIS,EAAIC,EAAEqO,KAAK,sBAAsB,EACpCpN,EAAIZ,EAAEqF,MAAQoH,EAAEuB,KAAK,UAAU,EAAEpF,KAAK,MAAM,EAAI,EAChD7I,EAAIH,EAAE4E,OAAO+L,IAAI,EAAE7O,QAAQ,KAAM,GAAG,EAAEA,QAAQ,KAAM,KAAK,EAC1D3B,GAAKA,EAAEgB,QACHnB,EAAE0K,aAAaC,SAAS,QAAQ,GAAK3K,EAAE0K,aAAazB,YAAY,QAAQ,EAAE2B,KAAK,EAAEC,SAAS,QAAQ,EACnG7K,EAAEgK,eAAec,KAAK,EACtB9K,EAAE0K,aAAaI,KAAK,EACpBhH,EAAE1C,KAAK,WACP,SAAS/B,EAAEA,EAAGC,GACb,IAAI0B,EAAI3B,EAAE+O,KAAK,yBAA2BjO,EAAI,IAAI,EAClD,IAAMa,EAAEG,QAAU9B,EAAE2J,KAAK,MAAM,IAAM1J,GAAKD,EAAEyL,KAAK,EAAGhL,EAAEoJ,OAAO,iBAAmB7J,EAAE0J,KAAK,MAAM,EAAI,IAAI,EAAE+B,KAAK,IAAMzL,EAAE+O,KAAK,8BAAgCjO,EAAI,KAAK,EAAE2K,KAAK,EAAG9J,EAAEiJ,KAAK,EAAG5K,EAAE2J,KAAK,MAAM,IAAM1J,GAAKD,EAAE4K,KAAK,EAAGnK,EAAEoJ,OAAO,iBAAmB7J,EAAE0J,KAAK,MAAM,EAAI,IAAI,EAAEkB,KAAK,EACjR,CACA,IAAI3K,EAAIO,EAAEgI,IAAI,EACd,IAAMvI,EAAE0J,KAAK,MAAM,EAChBrF,EAAEuF,OAAO,uBAAuB,EAAE9H,KAAK,WACvC/B,EAAEQ,EAAEgI,IAAI,EAAG,CAAC,CACZ,CAAC,EACDxI,EAAEC,EAAG0B,CAAC,CACT,CAAC,EACDgM,EAAIxE,EAAEc,KAAKtJ,CAAC,EAAIkE,EAAEuG,QAAQ,QAAQ,IACjCZ,EAAE7J,EAAG,CAAA,CAAE,EAAG8D,EAAEoF,OAAO,eAAiB2D,EAAEuB,KAAK,UAAU,EAAEpF,KAAK,MAAM,EAAI,yBAAyB,EAAEiB,KAAK,EAAGpK,EAAE,YAAaiE,CAAC,EAAEmG,KAAK,EAAGnK,EAAEmK,KAAK,EAAGzB,EAAEc,KAAKtJ,CAAC,EAC1J,CAAC,EACAwK,GAAG,eAAgB,WAClBxK,EAAE2O,YAAc,CAAA,EAAK3O,EAAE4E,OAAOqE,YAAY,SAAS,EAAGjJ,EAAEyK,QAAQ,MAAM,CACxE,CAAC,EACHrK,EAAEyF,WACD7F,EAAEwK,GAAG,WAAY,SAAUnL,EAAGC,GAC7BA,EAAEsR,UAAY,GAAKtR,EAAEuR,OAASvR,EAAEsQ,eAAe,EAAG5O,EAAEuN,MAAM,GAAK,IAAMjP,EAAEuR,QAAUvR,EAAEsQ,eAAe,EAAG5O,EAAE2H,GAAG,SAAS,IAAK3I,EAAEqP,WAAW,EACtI,CAAC,EACFxL,EAAEzD,EAAE0Q,MAAM,GACT,CAACjR,EAAEsH,cAAc/G,EAAE0Q,MAAM,GACzBjR,EAAEuB,KAAKhB,EAAE0Q,OAAQ,SAAUzR,EAAGC,GAC7BU,EAAEwK,GAAGnL,EAAEyC,QAAQ,KAAM,GAAG,EAAGxC,CAAC,CAC7B,CAAC,EACFc,EAAEqE,eACCqI,EAAI,WACL,IAAIzN,EAAI,CAAE8G,SAAU/F,EAAE8F,aAAaC,SAAUC,UAAWhG,EAAE8F,aAAaE,SAAU,EAK7EpF,GAJJZ,EAAEyF,YACAxG,EAAE0R,UAAY,SAAU1R,EAAGC,GAC3B,GAAI,CAACD,EAAEuR,SAAW,IAAMvR,EAAEwR,MAAO,OAAOvR,EAAE0R,SAC3C,GACOnR,EAAEoR,IAAIhR,EAAEoC,aAAc,SAAUhD,EAAGC,GAC1C,MAAO,CAACc,EAAE0F,mBAAqB,eAAe2K,KAAKnR,CAAC,EAAI,KAAOA,CAChE,CAAC,GACD0B,EAAEkQ,KAAK,EACN/Q,EAAE+F,aACD,CACC,CACChF,GAAIN,EACJuQ,MAAO,iBACPvM,OAAQ,SAAUtF,EAAGD,GACpBA,EACCQ,EAAEoR,IAAIjQ,EAAG,SAAU3B,GAClB,OAAO,IAAMA,EAAE+Q,QAAQ9Q,CAAC,EAAID,EAAI,IACjC,CAAC,CACF,CACD,EACA+R,SAAU,SAAU/R,GACnB,OAAO+C,EAAE/C,EAAGW,EAAEwH,aAAa,EAAI,IAAMnI,EAAEyC,QAAQ,KAAM,EAAE,CACxD,EACAA,QAAS,SAAUzC,GAClB,OAAO+C,EAAE/C,EAAGW,EAAEwH,aAAa,CAC5B,EACA2E,MAAO,CAAA,EACPkF,MAAO,CACR,GAEDhS,CACD,EACAe,EAAE8F,aAAaE,WAAa,UAAYvG,EAAEM,EAAE6I,KAAK,cAAc,EAAEsI,OAAO7H,QAAQ,EAAEtB,IAAI,UAAU,GAAKtI,EAAEM,EAAE6I,KAAK,cAAc,EAAEsI,OAAO7H,QAAQ,EAAEtB,IAAI,WAAY,UAAU,CAC3K,EACC4E,EAAI,WACJ,IAAI1N,EACJW,EAAEuR,SAICvR,EAAEwK,GAAG,UAHHnL,EAAI,WACNW,EAAEwR,IAAI,UAAWnS,CAAC,EAAGyN,EAAE,CACvB,CACiB,EACjBA,EAAE,CACN,EACAjN,EAAEsE,GAAG+B,aAAe6G,EAAE,EAAIlN,EAAEmM,KAAK,CAAEC,IAAK,uFAAwFC,SAAU,SAAUC,MAAO,CAAA,EAAIC,QAASW,CAAE,CAAC,GAC5K/M,EAAE2F,SACA+F,EAAEb,SAAS1B,EAAE,SAAU,CAAA,CAAE,CAAC,EAC3BnJ,EAAEwK,GAAG,WAAY,SAAUnL,EAAGC,GAC7B,IAAMA,EAAEuR,OAASvR,EAAEsQ,eAAe,CACnC,CAAC,GACF,WAAWa,KAAKgB,UAAUC,SAAS,GAAKtS,SAASuS,YAAY,uBAAwB,CAAA,EAAI,CAAA,CAAE,EAC1F3R,EAAE4R,QAAU,CAAA,EACb5R,EAAEyK,QAAQ,SAAUtK,CAAC,EACrBH,EAAEyK,QAAQ,QAAStK,CAAC,CAoDpB,CAAC,CACH,CACCb,EAAEuS,UAAUrH,GAAK,SAAUnL,EAAG2B,GAC9B,IAAIlB,EACJ,OACCT,GACCQ,EAAE2J,WAAWxI,CAAC,IACZlB,EAAI+H,KACNhI,EAAEuB,KAAK/B,EAAE4B,YAAY,EAAEkJ,MAAM,GAAG,EAAG,SAAU9K,EAAGC,GAC/C,IAAWQ,EAAGK,EAETd,EAFMS,EAcRA,EAbFK,GADaA,EAcRb,GAbCwC,QAAQ,KAAM,EAAE,EAClBzC,EAAIS,EAAEoB,GACVlB,EAAEX,GAAGc,KACHN,EAAEuB,KAAKpB,EAAEX,GAAGc,GAAI,SAAUd,EAAG2B,GAC7BnB,EAAEuB,KAAKvB,EAAE2B,QAAQR,EAAE,EAAE,EAAIA,EAAE,GAAK,CAACA,EAAE,IAAK,SAAU3B,EAAGC,GACpDO,EAAEP,CAAC,EAAEkL,GAAGxJ,EAAE,GAAI,WACb,IAAI3B,EAAIqB,EAAE4I,KAAKC,SAAS,EACvBjK,EAAIO,EAAE2J,WAAWxI,EAAE,EAAE,EAAIA,EAAE,GAAGK,MAAMvB,EAAG,CAACK,GAAG2R,OAAOzS,CAAC,CAAC,EAAI2B,EAAE,GAC3D1B,GAAKyB,EAAEjB,EAAGK,EAAG,CAACb,GAAGwS,OAAOzS,CAAC,CAAC,CAC3B,CAAC,CACF,CAAC,CACF,CAAC,EACAW,EAAEX,GAAGc,GAAK,OAEXJ,EAAED,EAAEoB,IAAI5B,KAAOS,EAAED,EAAEoB,IAAI5B,GAAK,KAAKe,KAAKW,CAAC,CAC1C,CAAC,GACF6G,IAEF,EACEvI,EAAEuS,UAAUL,IAAM,SAAUnS,EAAGS,GAC/B,IAAIK,EACJ,OACCd,IACGc,EAAI0H,KAAK3G,GACXrB,EAAEuB,KAAK/B,EAAE4B,YAAY,EAAEa,QAAQ,KAAM,GAAG,EAAEqI,MAAM,GAAG,EAAG,SAAU9K,EAAG2B,GAClEjB,EAAEI,GAAGa,IACJ,CAAC,KAAKyP,KAAKzP,CAAC,IACXlB,EACED,EAAEuB,KAAKrB,EAAEI,GAAGa,GAAI,SAAU3B,EAAGC,GAC7BA,IAAMQ,IAAMC,EAAEI,GAAGa,GAAKjB,EAAEI,GAAGa,GAAGqP,OAAOhR,EAAG,CAAC,EACzC,CAAC,EACAU,EAAEI,GAAGa,GAAK,GAChB,CAAC,GACF6G,IAEF,EACCvI,EAAEuS,UAAUpH,QAAU,WACtB,IAAIpL,EAAIqB,EAAE4I,KAAKC,SAAS,EACvBjK,EAAI,CAACuI,MAAMiK,OAAOzS,EAAEsB,MAAM,EAAG,CAAC,CAAC,EAChC,OAAOrB,EAAEe,KAAKhB,EAAEsB,MAAM,CAAC,CAAC,EAAGI,EAAEM,MAAMwG,KAAMvI,CAAC,CAC3C,EACCA,EAAEuS,UAAUE,SAAW,WACvB,IAAI1S,EAAIwI,KACR,OACCzH,EAAE,WACDf,EAAEyI,OAAOmH,MAAM,CAChB,CAAC,EACD5P,CAEF,EACCC,EAAEuS,UAAUxD,QAAU,SAAUhP,GAChC,IAAIC,EAAIuI,KACR,OACCzH,EAAE,WACDd,EAAEwI,OAAOoG,KAAK5G,EAAEjI,EAAGC,CAAC,CAAC,EAAIA,EAAEoR,QAAUpR,EAAEwI,OAAOoG,KAAK,EAAInN,EAAEzB,EAAG,SAAU,CAACA,EAAEwI,OAAO,EAAGF,EAAEvG,MAAM/B,CAAC,CAC7F,CAAC,EACDA,CAEF,EACCA,EAAEuS,UAAUvD,QAAU,WACtB,OAAO7G,EAAEI,KAAKC,OAAOoG,KAAK,EAAGrG,IAAI,CAClC,EACCvI,EAAEuS,UAAUvC,WAAa,WACzB,IAAIjQ,EAAIwI,KACR,OACCxI,EAAE2S,WAAa/S,OAAOgT,aAAa5S,EAAE2S,SAAS,EAC9C3S,EAAEiJ,OAAOW,YAAY,QAAQ,EAC5B5J,EAAE2S,UAAY/S,OAAOkR,WAAW,WAChC9Q,EAAE6I,OAAO2C,SAAS,QAAQ,CAC3B,EAAG,EAAE,EACL9J,EAAE1B,EAAG,cAAe,CAACA,EAAEiJ,OAAO,EAC9BjJ,CAEF,EACCC,EAAEuS,UAAUxC,WAAa,WACzB,IAAIhQ,EAAIwI,KACR,OACCxI,EAAE2S,WAAa/S,OAAOgT,aAAa5S,EAAE2S,SAAS,EAC9C3S,EAAE6I,OAAOe,YAAY,QAAQ,EAC5B5J,EAAE2S,UAAY/S,OAAOkR,WAAW,WAChC9Q,EAAEiJ,OAAOuC,SAAS,QAAQ,CAC3B,EAAG,GAAG,EACN9J,EAAE1B,EAAG,cAAe,CAACA,EAAEiJ,OAAO,EAC9BjJ,CAEF,EACCC,EAAEuS,UAAUK,OAAS,WACrB,SAAS7S,IACPC,EAAEiS,SAAW,CAAA,EAAKjS,EAAEwI,OAAOqK,KAAK,kBAAmB,CAAA,CAAE,EAAG7S,EAAE4I,OAAO+B,KAAK,EACvE,IAAI5K,EAAIC,EAAEA,EAAEyG,WAAa,SAAW,UACpC1G,EAAE+S,OAAO,EAAEnJ,YAAY,sBAAsB,EAAGlI,EAAEzB,EAAG,UAAW,CAACD,EAAE,CACpE,CACA,IAAIC,EAAIuI,KACR,OAAOvI,EAAEsS,QAAUvS,EAAE,EAAIC,EAAEkL,GAAG,QAASnL,CAAC,EAAGC,CAC5C,EACCA,EAAEuS,UAAUvE,QAAU,WACtB,IAAIhO,EAAIuI,KAER,SAASxI,IACRC,EAAEwI,OAAOqK,KAAK,kBAAmB,CAAA,CAAE,EAAG7S,EAAE+P,WAAW,EAAG/P,EAAE4I,OAAO4C,KAAK,EACpE,IAAIzL,EAAIC,EAAEA,EAAEyG,WAAa,SAAW,UACpC1G,EAAE+S,OAAO,EAAEvH,SAAS,sBAAsB,EAAG9J,EAAEzB,EAAG,WAAY,CAACD,EAAE,CAClE,CACA,OANAC,EAAEiS,SAAW,CAAA,EAMNjS,EAAEsS,QAAUvS,EAAE,EAAIC,EAAEkL,GAAG,QAASnL,CAAC,EAAGC,CAC5C,EACCO,EAAEsE,GAAGC,aAAe,SAAU/E,GAC9B,OAAOwI,KAAKzG,KAAK,WAChB,OAAOyG,KAAKzD,eAAiBvE,EAAEmJ,KAAKnB,KAAM,eAAiBA,KAAKzD,aAAe,IAAI9E,EAAEO,EAAEgI,IAAI,EAAGxI,CAAC,CAAE,EAAGwI,KAAKzD,aAC1G,CAAC,CACF,EACCvE,EAAEsE,GAAGC,aAAaC,SAAWrD,EAAE,EAC/BnB,EAAEsE,GAAGkO,iBAAmB,SAAUhT,GAClCA,EAAI6H,EAAE7H,CAAC,EACP,IAAIC,EAAIuI,KACP7G,EAAI,CAAEkE,WAAY,CAAC7F,GAAK,KAAA,IAAWA,EAAE6F,YAAc7F,EAAE6F,WAAYsC,cAAe,oCAAsCnI,GAAKA,EAAE8F,QAAUtE,EAAI,EAAI,gBAAkBJ,EAAI,qBAAuB,KAAM,EACnM,OACCsL,EAAE1M,CAAC,EACHe,EAAE,WACDd,EAAE8B,KAAK,WACN,IAAI/B,EAAIQ,EAAEgI,IAAI,EACd,OAAOxI,EAAEsL,SAAS,mBAAmB,GAAKtL,EAAEwL,SAAS,mBAAmB,EAAEqD,KAAK5G,EAAEjI,EAAEsJ,GAAG,UAAU,GAAKtJ,EAAEsJ,GAAG,OAAO,EAAItJ,EAAEsR,IAAI,EAAItR,EAAE8O,KAAK,EAAGnN,CAAC,CAAC,EAAG3B,CAC/I,CAAC,CACF,CAAC,EACDwI,IAEF,CACF,EAAG5I,MAAM"}