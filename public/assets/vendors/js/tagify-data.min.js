"use strict";var tomailmodal=document.querySelector("input[name=tomailmodal]"),ccmailmodal=document.querySelector("input[name=ccmailmodal]"),bccmailmodal=document.querySelector("input[name=bccmailmodal]"),tomailcontent=document.querySelector("input[name=tomailcontent]"),ccmailcontent=document.querySelector("input[name=ccmailcontent]"),bccmailcontent=document.querySelector("input[name=bccmailcontent]"),userlist=[{value:1,name:"theme_ocean",avatar:"https://en.gravatar.com/userimage/217650896/f011e8341437035d5063e32a126cb70d.png",email:"<EMAIL>"},{value:2,name:"<PERSON><PERSON><PERSON>",avatar:"https://i.pravatar.cc/80?img=1",email:"<EMAIL>"},{value:3,name:"<PERSON><PERSON> <PERSON><PERSON><PERSON>",avatar:"https://i.pravatar.cc/80?img=2",email:"<EMAIL>"},{value:4,name:"Ardeen Batisse",avatar:"https://i.pravatar.cc/80?img=3",email:"<EMAIL>"},{value:5,name:"Graeme Yellowley",avatar:"https://i.pravatar.cc/80?img=4",email:"<EMAIL>"},{value:6,name:"Dido Wilford",avatar:"https://i.pravatar.cc/80?img=5",email:"<EMAIL>"},{value:7,name:"Celesta Orwin",avatar:"https://i.pravatar.cc/80?img=6",email:"<EMAIL>"},{value:8,name:"Sally Main",avatar:"https://i.pravatar.cc/80?img=7",email:"<EMAIL>"},{value:9,name:"Grethel Haysman",avatar:"https://i.pravatar.cc/80?img=8",email:"<EMAIL>"},{value:10,name:"Marvin Mandrake",avatar:"https://i.pravatar.cc/80?img=9",email:"<EMAIL>"},{value:11,name:"Corrie Tidey",avatar:"https://i.pravatar.cc/80?img=10",email:"<EMAIL>"},{value:12,name:"Antons Esson",avatar:"https://i.pravatar.cc/80?img=12",email:"<EMAIL>"},{value:13,name:"Archie Cantones",avatar:"https://i.pravatar.cc/80?img=13",email:"<EMAIL>"},{value:14,name:"Holmes Cherryman",avatar:"https://i.pravatar.cc/80?img=14",email:"<EMAIL>"},{value:15,name:"Malanie Hanvey",avatar:"https://i.pravatar.cc/80?img=15",email:"<EMAIL>"},{value:16,name:"Kenneth Hune",avatar:"https://i.pravatar.cc/80?img=16",email:"<EMAIL>"},{value:17,name:"Antons Esson",avatar:"https://i.pravatar.cc/80?img=17",email:"<EMAIL>"},{value:18,name:"Jesse Ross",avatar:"https://i.pravatar.cc/80?img=18",email:"<EMAIL>"},{value:19,name:"Madsen Daniel",avatar:"https://i.pravatar.cc/80?img=19",email:"<EMAIL>"},{value:20,name:"Valentine Maton",avatar:"https://i.pravatar.cc/80?img=20",email:"<EMAIL>"}];$(document).ready(function(){var t=new Tagify(tomailmodal,{tagTextProp:"name",enforceWhitelist:!0,skipInvalid:!0,dropdown:{closeOnSelect:!1,enabled:0,classname:"users-list",searchKeys:["name","email"]},templates:{tag:function(a){return`
			<tag title="${a.email}"
					contenteditable='false'
					spellcheck='false'
					tabIndex="-1"
					class="tagify__tag ${a.class||""}"
					${this.getAttributes(a)}>
				<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
				<div>
					<div class='tagify__tag__avatar-wrap'>
						<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
					</div>
					<span class='tagify__tag-text'>${a.name}</span>
				</div>
			</tag>
		`},dropdownItem:function(a){return`
			<div ${this.getAttributes(a)}
				class='tagify__dropdown__item ${a.class||""}'
				tabindex="0"
				role="option">
				${a.avatar?`
				<div class='tagify__dropdown__item__avatar-wrap'>
					<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
				</div>`:""}
				<strong>${a.name}</strong>
				<span class="text-gray-500">${a.email}</span>
			</div>
		`},dropdownHeader:function(a){return`
			<div class="${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll">
				<strong>${this.value.length?"Add Remaining "+a.length:"Add All"}</strong>
				<span>${a.length} members</span>
			</div>
		`}},whitelist:userlist});t.on("dropdown:select",function(a){console.log(this),console.log(a.detail.elm.className,a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")),a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")&&t.dropdown.selectAll()})}),$(document).ready(function(){var t=new Tagify(ccmailmodal,{tagTextProp:"name",enforceWhitelist:!0,skipInvalid:!0,dropdown:{closeOnSelect:!1,enabled:0,classname:"users-list",searchKeys:["name","email"]},templates:{tag:function(a){return`
			<tag title="${a.email}"
					contenteditable='false'
					spellcheck='false'
					tabIndex="-1"
					class="tagify__tag ${a.class||""}"
					${this.getAttributes(a)}>
				<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
				<div>
					<div class='tagify__tag__avatar-wrap'>
						<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
					</div>
					<span class='tagify__tag-text'>${a.name}</span>
				</div>
			</tag>
		`},dropdownItem:function(a){return`
			<div ${this.getAttributes(a)}
				class='tagify__dropdown__item ${a.class||""}'
				tabindex="0"
				role="option">
				${a.avatar?`
				<div class='tagify__dropdown__item__avatar-wrap'>
					<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
				</div>`:""}
				<strong>${a.name}</strong>
				<span class="text-gray-500">${a.email}</span>
			</div>
		`},dropdownHeader:function(a){return`
			<div class="${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll">
				<strong>${this.value.length?"Add Remaining "+a.length:"Add All"}</strong>
				<span>${a.length} members</span>
			</div>
		`}},whitelist:userlist});t.on("dropdown:select",function(a){console.log(this),console.log(a.detail.elm.className,a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")),a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")&&t.dropdown.selectAll()})}),$(document).ready(function(){var t=new Tagify(bccmailmodal,{tagTextProp:"name",enforceWhitelist:!0,skipInvalid:!0,dropdown:{closeOnSelect:!1,enabled:0,classname:"users-list",searchKeys:["name","email"]},templates:{tag:function(a){return`
			<tag title="${a.email}"
					contenteditable='false'
					spellcheck='false'
					tabIndex="-1"
					class="tagify__tag ${a.class||""}"
					${this.getAttributes(a)}>
				<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
				<div>
					<div class='tagify__tag__avatar-wrap'>
						<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
					</div>
					<span class='tagify__tag-text'>${a.name}</span>
				</div>
			</tag>
		`},dropdownItem:function(a){return`
			<div ${this.getAttributes(a)}
				class='tagify__dropdown__item ${a.class||""}'
				tabindex="0"
				role="option">
				${a.avatar?`
				<div class='tagify__dropdown__item__avatar-wrap'>
					<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
				</div>`:""}
				<strong>${a.name}</strong>
				<span class="text-gray-500">${a.email}</span>
			</div>
		`},dropdownHeader:function(a){return`
			<div class="${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll">
				<strong>${this.value.length?"Add Remaining "+a.length:"Add All"}</strong>
				<span>${a.length} members</span>
			</div>
		`}},whitelist:userlist});t.on("dropdown:select",function(a){console.log(this),console.log(a.detail.elm.className,a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")),a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")&&t.dropdown.selectAll()})}),$(document).ready(function(){var t=new Tagify(tomailcontent,{tagTextProp:"name",enforceWhitelist:!0,skipInvalid:!0,dropdown:{closeOnSelect:!1,enabled:0,classname:"users-list",searchKeys:["name","email"]},templates:{tag:function(a){return`
			<tag title="${a.email}"
					contenteditable='false'
					spellcheck='false'
					tabIndex="-1"
					class="tagify__tag ${a.class||""}"
					${this.getAttributes(a)}>
				<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
				<div>
					<div class='tagify__tag__avatar-wrap'>
						<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
					</div>
					<span class='tagify__tag-text'>${a.name}</span>
				</div>
			</tag>
		`},dropdownItem:function(a){return`
			<div ${this.getAttributes(a)}
				class='tagify__dropdown__item ${a.class||""}'
				tabindex="0"
				role="option">
				${a.avatar?`
				<div class='tagify__dropdown__item__avatar-wrap'>
					<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
				</div>`:""}
				<strong>${a.name}</strong>
				<span class="text-gray-500">${a.email}</span>
			</div>
		`},dropdownHeader:function(a){return`
			<div class="${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll">
				<strong>${this.value.length?"Add Remaining "+a.length:"Add All"}</strong>
				<span>${a.length} members</span>
			</div>
		`}},whitelist:userlist});t.on("dropdown:select",function(a){console.log(this),console.log(a.detail.elm.className,a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")),a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")&&t.dropdown.selectAll()})}),$(document).ready(function(){var t=new Tagify(ccmailcontent,{tagTextProp:"name",enforceWhitelist:!0,skipInvalid:!0,dropdown:{closeOnSelect:!1,enabled:0,classname:"users-list",searchKeys:["name","email"]},templates:{tag:function(a){return`
			<tag title="${a.email}"
					contenteditable='false'
					spellcheck='false'
					tabIndex="-1"
					class="tagify__tag ${a.class||""}"
					${this.getAttributes(a)}>
				<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
				<div>
					<div class='tagify__tag__avatar-wrap'>
						<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
					</div>
					<span class='tagify__tag-text'>${a.name}</span>
				</div>
			</tag>
		`},dropdownItem:function(a){return`
			<div ${this.getAttributes(a)}
				class='tagify__dropdown__item ${a.class||""}'
				tabindex="0"
				role="option">
				${a.avatar?`
				<div class='tagify__dropdown__item__avatar-wrap'>
					<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
				</div>`:""}
				<strong>${a.name}</strong>
				<span class="text-gray-500">${a.email}</span>
			</div>
		`},dropdownHeader:function(a){return`
			<div class="${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll">
				<strong>${this.value.length?"Add Remaining "+a.length:"Add All"}</strong>
				<span>${a.length} members</span>
			</div>
		`}},whitelist:userlist});t.on("dropdown:select",function(a){console.log(this),console.log(a.detail.elm.className,a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")),a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")&&t.dropdown.selectAll()})}),$(document).ready(function(){var t=new Tagify(bccmailcontent,{tagTextProp:"name",enforceWhitelist:!0,skipInvalid:!0,dropdown:{closeOnSelect:!1,enabled:0,classname:"users-list",searchKeys:["name","email"]},templates:{tag:function(a){return`
			<tag title="${a.email}"
					contenteditable='false'
					spellcheck='false'
					tabIndex="-1"
					class="tagify__tag ${a.class||""}"
					${this.getAttributes(a)}>
				<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
				<div>
					<div class='tagify__tag__avatar-wrap'>
						<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
					</div>
					<span class='tagify__tag-text'>${a.name}</span>
				</div>
			</tag>
		`},dropdownItem:function(a){return`
			<div ${this.getAttributes(a)}
				class='tagify__dropdown__item ${a.class||""}'
				tabindex="0"
				role="option">
				${a.avatar?`
				<div class='tagify__dropdown__item__avatar-wrap'>
					<img onerror="this.style.visibility='hidden'" src="${a.avatar}">
				</div>`:""}
				<strong>${a.name}</strong>
				<span class="text-gray-500">${a.email}</span>
			</div>
		`},dropdownHeader:function(a){return`
			<div class="${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll">
				<strong>${this.value.length?"Add Remaining "+a.length:"Add All"}</strong>
				<span>${a.length} members</span>
			</div>
		`}},whitelist:userlist});t.on("dropdown:select",function(a){console.log(this),console.log(a.detail.elm.className,a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")),a.detail.elm.classList.contains(t.settings.classNames.dropdownItem+"__addAll")&&t.dropdown.selectAll()})});
//# sourceMappingURL=tagify-data.min.js.map
