{"version": 3, "file": "full-screen-helper.min.js", "sources": ["full-screen-helper.min.js"], "sourcesContent": ["/* full-screen-helper.js 1.0.5 | Copyright (c) 2020 Guilherme Nascimento (<EMAIL>) | Released under the MIT license */\r\n(function (a) {\r\n\tfunction b(a, b) {\r\n\t\tif (\"function\" == typeof a) {\r\n\t\t\tif (!b) return void O.push(a);\r\n\t\t\tif (!b) return void O.push(a);\r\n\t\t\tfor (var c = [], d = 0, e = O.length; d < void 0; d++) O[d] !== a && c.push(a);\r\n\t\t\t(O = c), (c = null);\r\n\t\t}\r\n\t}\r\n\tfunction c(b) {\r\n\t\treturn !A && (b === a || b === w ? w.body : \"string\" == typeof b ? w.querySelector(b) : !!(!z.HTMLElement || (b && b instanceof z.HTMLElement && b.ownerDocument === w)) && !!(b && 1 === b.nodeType && b.ownerDocument === w) && b);\r\n\t}\r\n\tfunction e(a, b, c) {\r\n\t\ta.addEventListener ? a.addEventListener(b, c) : a.attachEvent(\"on\" + b, c);\r\n\t}\r\n\tfunction f() {\r\n\t\treturn m() && (z.outerWidth || z.innerWidth || u.clientWidth) == z.screen.width;\r\n\t}\r\n\tfunction g() {\r\n\t\tvar a = w.fullscreenElement || w.mozFullScreenElement || w.webkitFullscreenElement || w.msFullscreenElement;\r\n\t\treturn !!a && (a !== A && (A = a), !0);\r\n\t}\r\n\tfunction h() {\r\n\t\tif (B) return B;\r\n\t\tif (!1 === y || z.ActiveXObject === a) y = !1;\r\n\t\telse if (y === a)\r\n\t\t\ttry {\r\n\t\t\t\t(y = new z.ActiveXObject(\"WScript.Shell\")), (B = !0), e(z, \"resize\", l);\r\n\t\t\t} catch (a) {\r\n\t\t\t\ty = !1;\r\n\t\t\t}\r\n\t\treturn B;\r\n\t}\r\n\tfunction j(a) {\r\n\t\t(a = a || z.event), 27 == (a.wich || a.keyCode) && s();\r\n\t}\r\n\tfunction k() {\r\n\t\to(B ? f() : g());\r\n\t}\r\n\tfunction l() {\r\n\t\tclearTimeout(x), (x = setTimeout(k, B ? 100 : 10));\r\n\t}\r\n\tfunction m() {\r\n\t\treturn !!u || ((v = w.body), (u = w.documentElement || (v && v.parentNode)), !!u);\r\n\t}\r\n\tfunction n(a) {\r\n\t\treturn C || ((C = !0), e(w, \"keydown\", j)), h() ? void (f() || ((A = a), o(!0), y.SendKeys(\"{F11}\"))) : void ((D = E), D && r(a));\r\n\t}\r\n\tfunction o(a) {\r\n\t\treturn F !== a && A ? (m() ? void (a ? (!M.test(u.className) && (u.className += \" fsh-infullscreen\"), !N.test(A.className) && (A.className += \" full-screen-helper\")) : ((u.className = u.className.replace(M, \" \")), (A.className = A.className.replace(N, \" \")), (A = null)), (F = a), setTimeout(p, 1)) : void (A = null)) : void 0;\r\n\t}\r\n\tfunction p() {\r\n\t\tfor (var a = 0; a < O.length; a++) O[a]();\r\n\t}\r\n\tfunction q() {\r\n\t\treturn Q || h();\r\n\t}\r\n\tfunction r(a) {\r\n\t\tif (((a = c(a)), !!a)) {\r\n\t\t\tif (G) a.requestFullscreen();\r\n\t\t\telse if (H) a.mozRequestFullScreen();\r\n\t\t\telse if (I) a.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);\r\n\t\t\telse if (L) a.msRequestFullscreen();\r\n\t\t\telse if (!D) return void n(a);\r\n\t\t\t(A = a), o(!0);\r\n\t\t}\r\n\t}\r\n\tfunction s() {\r\n\t\tif (A) {\r\n\t\t\tif (G) w.exitFullscreen();\r\n\t\t\telse if (H) w.mozCancelFullScreen();\r\n\t\t\telse if (K) w.webkitExitFullscreen();\r\n\t\t\telse if (J) w.webkitCancelFullScreen();\r\n\t\t\telse if (L) w.msExitFullscreen();\r\n\t\t\telse if (!D) return void (f() && B && (o(!1), y.SendKeys(\"{F11}\")));\r\n\t\t\to(!1);\r\n\t\t}\r\n\t}\r\n\tfunction t(a) {\r\n\t\tA === (a || w.body) ? s() : r(a);\r\n\t}\r\n\tvar u,\r\n\t\tv,\r\n\t\tx,\r\n\t\ty,\r\n\t\tz = \"undefined\" == typeof window ? {} : window,\r\n\t\tw = z.document || {},\r\n\t\td = z.$ || {},\r\n\t\tA = null,\r\n\t\tB = !1,\r\n\t\tC = !1,\r\n\t\tD = !1,\r\n\t\tE = !0,\r\n\t\tF = !1,\r\n\t\tG = !!w.exitFullscreen,\r\n\t\tH = !!w.mozCancelFullScreen,\r\n\t\tI = !!(w.webkitExitFullscreen || w.webkitCancelFullScreen),\r\n\t\tJ = !!w.webkitCancelFullScreen,\r\n\t\tK = !!w.webkitExitFullscreen,\r\n\t\tL = !!w.msExitFullscreen,\r\n\t\tM = /(^|\\s+)fsh-infullscreen($|\\s+)/i,\r\n\t\tN = /(^|\\s+)full-screen-helper($|\\s+)/i,\r\n\t\tO = [],\r\n\t\tP = [\"webkitfullscreenchange\", \"mozfullscreenchange\", \"fullscreenchange\", \"MSFullscreenChange\"],\r\n\t\tQ = G || H || I || L;\r\n\tif (Q) {\r\n\t\tfor (var R = P.length - 1; 0 <= R; R--) e(w, P[R], l);\r\n\t\te(z, \"resize\", l);\r\n\t}\r\n\tif (\r\n\t\td &&\r\n\t\td.extend &&\r\n\t\td.expr &&\r\n\t\t((d.fn.fullScreenHelper = function (b) {\r\n\t\t\tvar c = this[0];\r\n\t\t\tc && (\"toggle\" === b ? t(c) : \"request\" === b || b === a ? r(c) : void 0);\r\n\t\t}),\r\n\t\t(d.fullScreenHelper = function (a) {\r\n\t\t\tswitch (a) {\r\n\t\t\t\tcase \"exit\":\r\n\t\t\t\t\ts();\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"supported\":\r\n\t\t\t\t\treturn q();\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"state\":\r\n\t\t\t\t\treturn F;\r\n\t\t\t}\r\n\t\t}),\r\n\t\t(d.expr[\":\"].fullscreen = function (a) {\r\n\t\t\treturn N.test(a.className);\r\n\t\t}),\r\n\t\t!(\"onfullscreenchange\" in w))\r\n\t) {\r\n\t\tvar S = d(w);\r\n\t\tb(function () {\r\n\t\t\tS.trigger(\"fullscreenchange\");\r\n\t\t});\r\n\t}\r\n\tz.FullScreenHelper = {\r\n\t\tsupported: q,\r\n\t\trequest: r,\r\n\t\ttoggle: t,\r\n\t\texit: s,\r\n\t\tcurrent: function () {\r\n\t\t\treturn A;\r\n\t\t},\r\n\t\tstate: function () {\r\n\t\t\treturn F;\r\n\t\t},\r\n\t\tviewport: function (a) {\r\n\t\t\tE = !!a;\r\n\t\t},\r\n\t\ton: function (a) {\r\n\t\t\tb(a);\r\n\t\t},\r\n\t\toff: function (a) {\r\n\t\t\tb(a, !0);\r\n\t\t},\r\n\t};\r\n})();\r\n"], "names": ["a", "b", "O", "push", "c", "d", "length", "e", "addEventListener", "attachEvent", "f", "m", "z", "outerWidth", "innerWidth", "u", "clientWidth", "screen", "width", "h", "B", "y", "ActiveXObject", "l", "j", "event", "wich", "keyCode", "s", "k", "o", "w", "fullscreenElement", "mozFullScreenElement", "webkitFullscreenElement", "msFullscreenElement", "A", "clearTimeout", "x", "setTimeout", "v", "body", "documentElement", "parentNode", "F", "M", "test", "className", "N", "replace", "p", "q", "Q", "r", "querySelector", "HTMLElement", "ownerDocument", "nodeType", "G", "requestFullscreen", "H", "mozRequestFullScreen", "I", "webkitRequestFullscreen", "Element", "ALLOW_KEYBOARD_INPUT", "L", "msRequestFullscreen", "D", "C", "SendKeys", "E", "exitFullscreen", "mozCancelFullScreen", "K", "webkitExitFullscreen", "J", "webkitCancelFullScreen", "msExitFullscreen", "t", "S", "window", "document", "$", "P", "R", "extend", "expr", "fn", "fullScreenHelper", "this", "fullscreen", "trigger", "FullScreenHelper", "supported", "request", "toggle", "exit", "current", "state", "viewport", "on", "off"], "mappings": "AACA,CAAA,SAAWA,GACV,SAASC,EAAED,EAAGC,GACb,GAAI,YAAc,OAAOD,EAAG,CAC3B,GAAI,CAACC,EAAG,OAAYC,EAAEC,KAAKH,CAAC,EAC5B,GAAI,CAACC,EAAG,OAAYC,EAAEC,KAAKH,CAAC,EACvB,IAAII,EAAI,GAAIC,EAAI,EAArB,IAA4BH,EAAEI,OAAQD,EAAI,KAAA,EAAQA,CAAC,GAAIH,EAAEG,KAAOL,GAAKI,EAAED,KAAKH,CAAC,EAC5EE,EAAIE,CACN,CACD,CAIA,SAASG,EAAEP,EAAGC,EAAGG,GAChBJ,EAAEQ,iBAAmBR,EAAEQ,iBAAiBP,EAAGG,CAAC,EAAIJ,EAAES,YAAY,KAAOR,EAAGG,CAAC,CAC1E,CACA,SAASM,IACR,OAAOC,EAAE,IAAMC,EAAEC,YAAcD,EAAEE,YAAcC,EAAEC,cAAgBJ,EAAEK,OAAOC,KAC3E,CAKA,SAASC,IACR,GAAIC,CAAAA,EACJ,GAAI,CAAA,IAAOC,GAAKT,EAAEU,gBAAkBtB,EAAGqB,EAAI,CAAA,OACtC,GAAIA,IAAMrB,EACd,IACEqB,EAAI,IAAIT,EAAEU,cAAc,eAAe,EAAKF,EAAI,CAAA,EAAKb,EAAEK,EAAG,SAAUW,CAAC,CAGvE,CAFE,MAAOvB,GACRqB,EAAI,CAAA,CACL,CACD,OAAOD,CACR,CACA,SAASI,EAAExB,GACU,MAAnBA,EAAIA,GAAKY,EAAEa,OAAiBC,MAAQ1B,EAAE2B,UAAYC,EAAE,CACtD,CACA,SAASC,IAlBT,IACK7B,EAkBJ8B,EAAEV,EAAIV,EAAE,EAjBD,CAAC,EADJV,EAAI+B,EAAEC,mBAAqBD,EAAEE,sBAAwBF,EAAEG,yBAA2BH,EAAEI,uBACzEnC,IAAMoC,IAAMA,EAAIpC,GAAI,CAAA,EAiBpB,CAChB,CACA,SAASuB,IACRc,aAAaC,CAAC,EAAIA,EAAIC,WAAWV,EAAGT,EAAI,IAAM,EAAE,CACjD,CACA,SAAST,IACR,MAAO,CAAC,CAACI,IAAOyB,EAAIT,EAAEU,KAAuD,CAAC,EAAhD1B,EAAIgB,EAAEW,iBAAoBF,GAAKA,EAAEG,YAChE,CAIA,SAASb,EAAE9B,GACH4C,IAAM5C,GAAKoC,IAAKzB,EAAE,GAAUX,GAAM6C,EAAEC,KAAK/B,EAAEgC,SAAS,IAAMhC,EAAEgC,WAAa,qBAAuBC,EAAEF,KAAKV,EAAEW,SAAS,IAAMX,EAAEW,WAAa,yBAA4BhC,EAAEgC,UAAYhC,EAAEgC,UAAUE,QAAQJ,EAAG,GAAG,EAAKT,EAAEW,UAAYX,EAAEW,UAAUE,QAAQD,EAAG,GAAG,EAAKZ,EAAI,MAASQ,EAAI5C,EAAIuC,WAAWW,EAAG,CAAC,GAAWd,EAAI,KACxT,CACA,SAASc,IACR,IAAK,IAAIlD,EAAI,EAAGA,EAAIE,EAAEI,OAAQN,CAAC,GAAIE,EAAEF,GAAG,CACzC,CACA,SAASmD,IACR,OAAOC,GAAKjC,EAAE,CACf,CACA,SAASkC,EAAErD,GACV,GAjDUC,EAiDED,EAANA,EAhDC,CAACoC,IAAMnC,IAAMD,GAAKC,IAAM8B,EAAIA,EAAEU,KAAO,UAAY,OAAOxC,EAAI8B,EAAEuB,cAAcrD,CAAC,EAAI,EAAIW,EAAE2C,aAAe,EAACtD,GAAKA,aAAaW,EAAE2C,aAAetD,EAAEuD,gBAAkBzB,KAAO,EAAG9B,CAAAA,GAAK,IAAMA,EAAEwD,UAAYxD,EAAEuD,gBAAkBzB,IAAM9B,GAgD3M,CACtB,GAAIyD,EAAG1D,EAAE2D,kBAAkB,OACtB,GAAIC,EAAG5D,EAAE6D,qBAAqB,OAC9B,GAAIC,EAAG9D,EAAE+D,wBAAwBC,QAAQC,oBAAoB,OAC7D,GAAIC,EAAGlE,EAAEmE,oBAAoB,OAC7B,GAAI,CAACC,EAAG,OAlBJpE,EAkBkBA,EAjBrBqE,IAAOA,EAAI,CAAA,EAAK9D,EAAEwB,EAAG,UAAWP,CAAC,GAiBnB,KAjBuBL,EAAE,EAAUT,EAAE,IAAO0B,EAAIpC,EAAI8B,EAAE,CAAA,CAAE,EAAGT,EAAEiD,SAAS,OAAO,IAAaF,EAAIG,IAASlB,EAAErD,CAAC,GAkB7HoC,EAAIpC,EAAI8B,EAAE,CAAA,CAAE,CACd,CApBD,IApCW7B,CAyDX,CACA,SAAS2B,IACR,GAAIQ,EAAG,CACN,GAAIsB,EAAG3B,EAAEyC,eAAe,OACnB,GAAIZ,EAAG7B,EAAE0C,oBAAoB,OAC7B,GAAIC,EAAG3C,EAAE4C,qBAAqB,OAC9B,GAAIC,EAAG7C,EAAE8C,uBAAuB,OAChC,GAAIX,EAAGnC,EAAE+C,iBAAiB,OAC1B,GAAI,CAACV,EAAG,OAAO,KAAM1D,EAAE,GAAKU,IAAMU,EAAE,CAAA,CAAE,EAAGT,EAAEiD,SAAS,OAAO,IAChExC,EAAE,CAAA,CAAE,CACL,CACD,CACA,SAASiD,EAAE/E,GACVoC,KAAOpC,GAAK+B,EAAEU,MAAQb,EAAE,EAAIyB,EAAErD,CAAC,CAChC,CACA,IAAIe,EACHyB,EACAF,EACAjB,EAkDI2D,EAjDJpE,EAAI,aAAe,OAAOqE,OAAS,GAAKA,OACxClD,EAAInB,EAAEsE,UAAY,GAClB7E,EAAIO,EAAEuE,GAAK,GACX/C,EAAI,KACJhB,EAAI,CAAA,EACJiD,EAAI,CAAA,EACJD,EAAI,CAAA,EACJG,EAAI,CAAA,EACJ3B,EAAI,CAAA,EACJc,EAAI,CAAC,CAAC3B,EAAEyC,eACRZ,EAAI,CAAC,CAAC7B,EAAE0C,oBACRX,EAAI,EAAG/B,CAAAA,EAAE4C,sBAAwB5C,CAAAA,EAAE8C,wBACnCD,EAAI,CAAC,CAAC7C,EAAE8C,uBACRH,EAAI,CAAC,CAAC3C,EAAE4C,qBACRT,EAAI,CAAC,CAACnC,EAAE+C,iBACRjC,EAAI,kCACJG,EAAI,oCACJ9C,EAAI,GACJkF,EAAI,CAAC,yBAA0B,sBAAuB,mBAAoB,sBAC1EhC,EAAIM,GAAKE,GAAKE,GAAKI,EACpB,GAAId,EAAG,CACN,IAAK,IAAIiC,EAAID,EAAE9E,OAAS,EAAG,GAAK+E,EAAGA,CAAC,GAAI9E,EAAEwB,EAAGqD,EAAEC,GAAI9D,CAAC,EACpDhB,EAAEK,EAAG,SAAUW,CAAC,CACjB,CAEClB,GACAA,EAAEiF,QACFjF,EAAEkF,OACAlF,EAAEmF,GAAGC,iBAAmB,SAAUxF,GACnC,IAAIG,EAAIsF,KAAK,GACbtF,IAAM,WAAaH,EAAI8E,EAAE3E,CAAC,EAAI,YAAcH,GAAKA,IAAMD,GAAIqD,EAAEjD,CAAC,EAC/D,EACCC,EAAEoF,iBAAmB,SAAUzF,GAC/B,OAAQA,GACP,IAAK,OACJ4B,EAAE,EACF,MACD,IAAK,YACJ,OAAOuB,EAAE,EAEV,IAAK,QACJ,OAAOP,CACT,CACD,EACCvC,EAAEkF,KAAK,KAAKI,WAAa,SAAU3F,GACnC,OAAOgD,EAAEF,KAAK9C,EAAE+C,SAAS,CAC1B,EACA,EAAE,uBAAwBhB,MAEtBiD,EAAI3E,EAAE0B,CAAC,EACX9B,EAAE,WACD+E,EAAEY,QAAQ,kBAAkB,CAC7B,CAAC,GAEFhF,EAAEiF,iBAAmB,CACpBC,UAAW3C,EACX4C,QAAS1C,EACT2C,OAAQjB,EACRkB,KAAMrE,EACNsE,QAAS,WACR,OAAO9D,CACR,EACA+D,MAAO,WACN,OAAOvD,CACR,EACAwD,SAAU,SAAUpG,GACnBuE,EAAI,CAAC,CAACvE,CACP,EACAqG,GAAI,SAAUrG,GACbC,EAAED,CAAC,CACJ,EACAsG,IAAK,SAAUtG,GACdC,EAAED,EAAG,CAAA,CAAE,CACR,CACD,CACA,EAAE"}