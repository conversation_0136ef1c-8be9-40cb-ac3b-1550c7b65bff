/*
<--!----------------------------------------------------------------!-->
<--! Form SCSS Start !-->
<--!----------------------------------------------------------------!-->
*/

input,
.custom-file,
.custom-select,
.form-select,
.form-control {
	color: $dark;
	padding: 12px 15px;
	border-color: $border-color;
	border-radius: $radius-sm;
	&.active,
	&.focus,
	&:active,
	&:focus,
	&:active,
	&:focus {
		outline: 0;
		color: $dark;
		border-color: $primary !important;
		box-shadow: none !important;
	}
	&::placeholder {
		color: $gray-500 !important;
	}
	&::-ms-placeholder {
		color: $gray-500 !important;
	}
	&::-moz-placeholder {
		color: $gray-500 !important;
	}
	&::-webkit-placeholder {
		color: $gray-500 !important;
	}
}
.form-label,
.col-form-label {
	color: $dark !important;
	font-size: $font-12 !important;
	font-weight: $font-600 !important;
}
.form-select {
	color: $dark;
	background-size: 10px;
	filter: invert(0);
	.form-select-sm,
	&.form-select-sm {
		background-size: 6px;
	}
}
.input-group-text {
	color: $dark;
	padding: 0 15px;
	font-weight: $font-600;
	border-radius: $radius-sm;
	border-color: $border-color;
	background-color: darken($gray-100, 1);
}
input[type="search"]::-webkit-search-cancel-button {
	display: none;
}
/*
<--!----------------------------------------------------------------!-->
<--! Custom Checkbox !-->
<--!----------------------------------------------------------------!-->
*/
input[type="checkbox"],
input[type="radio"] {
	padding: 0;
	box-sizing: border-box;
}
.custom-control {
	display: block;
	min-height: 1.8px;
	position: relative;
	padding-left: 0.75rem;
}
.custom-control-input {
	left: 10px;
	opacity: 0;
	z-index: -1;
	width: 1.25rem;
	height: 1.375rem;
	position: absolute;
}
.custom-control-label {
	left: 15px;
	cursor: pointer;
	margin-bottom: 0;
	position: relative;
	color: $brand-dark;
	font-size: $font-13;
	vertical-align: top;
	font-weight: $font-500;
	text-transform: inherit;
}
.custom-checkbox {
	.custom-control-label {
		&:before {
			content: "";
			width: 18px;
			height: 18px;
			top: 1px;
			left: -28px;
			display: block;
			border-width: 3px;
			position: absolute;
			border-radius: $radius-xs;
			border: 1px solid $border-color;
			background-color: darken($gray-100, 1);
		}
		&:after {
			top: 1px;
			content: "";
			left: -1.65rem;
			display: block;
			width: 0.75rem;
			height: 0.75rem;
			position: absolute;
			background-size: 0;
			background-repeat: no-repeat;
			background: no-repeat 50%/50% 50%;
			transition: background-size 0.15s ease-in-out;
		}
	}
	.custom-control-input:checked ~ .custom-control-label::after {
		background-size: 100%;
		transition: background-size 0.15s ease-in-out;
	}
	.custom-control-input:checked ~ .custom-control-label::after {
		top: 1px;
		left: -26px;
		color: $white;
		content: "\f272";
		position: absolute;
		font-family: bootstrap-icons !important;
	}
	.custom-control-input:checked ~ .custom-control-label::before {
		color: $white;
		border-color: $primary;
		background-color: $primary;
	}
}
.custom-control-label:before,
.custom-file-label,
.custom-select {
	-webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-size 5s ease-in-out;
	-webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, background-size 5s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, background-size 5s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
	-o-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -o-background-size 5s ease-in-out;
	-moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-size 5s ease-in-out, -moz-box-shadow 0.15s ease-in-out, -moz-background-size 5s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-size 5s ease-in-out;
	transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-size 5s ease-in-out, -webkit-box-shadow 0.15s ease-in-out, -moz-box-shadow 0.15s ease-in-out, -moz-background-size 5s ease-in-out, -o-background-size 5s ease-in-out;
}
.custom-radio {
	.custom-control-label {
		&:before {
			border-radius: $radius-circle;
		}
	}
	.custom-control-input:checked ~ .custom-control-label::after {
		border: 3px solid $white;
		border-radius: $radius-circle;
	}
}
.form-switch {
	.form-check-input:checked {
		border-color: $primary;
		background-color: $primary;
	}
}
@mixin switch($res: "sm") {
	$index: 1rem;
	$mainVal: 1rem;
	@if $res == "md" {
		$index: 2rem;
		$mainVal: 1.5rem;
	} @else if $res == "lg" {
		$index: 3rem;
		$mainVal: 2rem;
	} @else if $res == "xl" {
		$index: 4rem;
		$mainVal: 2.5rem;
	}
	.form-check-input {
		height: $mainVal;
		width: calc(#{$index} + 0.75rem);
		border-radius: $mainVal * 2;
	}
}
.form-check-input {
	clear: left;
}
.form-switch.form-switch-sm {
	@include switch();
}
.form-switch.form-switch-md {
	@include switch("md");
}
.form-switch.form-switch-lg {
	@include switch("lg");
}
.form-switch.form-switch-xl {
	@include switch("xl");
}
