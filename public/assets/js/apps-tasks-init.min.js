"use strict";$(document).ready(function(){$("#tasksSearch").bind("keyup",function(){var e=$(this).val().toLowerCase();$(".single-task-list").filter(function(){$(this).toggle(-1<$(this).text().toLowerCase().indexOf(e))})})}),$(document).on("change","[data-checked-action='task-action']",function(){$(this).prop("checked")?$(this).closest(".single-task-list").addClass("task-completed"):$(this).closest(".single-task-list").removeClass("task-completed")}),$(document).ready(function(){var e=document.getElementById("taskDateRange"),t=document.getElementById("taskDateRangeAdd");new DateRangePicker(e,{clearBtn:!0,allowOneSidedRange:!0}),new DateRangePicker(t,{clearBtn:!0,allowOneSidedRange:!0})}),$(document).ready(function(){new Quill("#notesEditor",{placeholder:"Compose an epic...@mention, #tag",theme:"snow"}),new Quill("#notesEditorAdd",{placeholder:"Compose an epic...@mention, #tag",theme:"snow"})});