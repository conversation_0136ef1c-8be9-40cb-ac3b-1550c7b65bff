:root {
    --cc-font-size: 10px;
    --cc-primary: #256BDE;
    --cc-secondary: #FF6129;
    --cc-teritory: #711C79;
    --cc-white: #fff;
    --cc-forest: #006366;
    --cc-stone: #01506E;
    --cc-bright-blue: #0071F1;
    --cc-teal: #029DA1;
    --cc-apple: #00AC34;
    --cc-orange: #FF4F01;
    --cc-pink: #FF3E6B;
    --cc-aubergine: #7E2373;
    --cc-font-size: 10px;
    --bs-font-sans-serif: 'Inter';
    --bs-body-font-size: 1.6rem;
    --bs-body-font-weight: 400;
    --bs-body-line-height: 1.5;
    --bs-body-color: #303030;
    --bs-body-bg: #fff;
    --bs-heading-color: #141414;
    --gjs-color-blue: #256BDE;
    --gjs-font-size: 1.4rem;
    --gjs-main-font: var(--bs-font-sans-serif);
    --gjs-secondary-dark-color: transparent;
    --gjs-quaternary-color: var(--gjs-color-blue);
    --gjs-tertiary-color: var(--gjs-color-blue);
}

@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 100;
    font-display: swap;
    src: url("../fonts/Inter-Thin.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 100;
    font-display: swap;
    src: url("../fonts/Inter-ThinItalic.woff2") format("woff2");
} 
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 200;
    font-display: swap;
    src: url("../fonts/Inter-ExtraLight.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 200;
    font-display: swap;
    src: url("../fonts/Inter-ExtraLightItalic.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url("../fonts/Inter-Light.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 300;
    font-display: swap;
    src: url("../fonts/Inter-LightItalic.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url("../fonts/Inter-Regular.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 400;
    font-display: swap;
    src: url("../fonts/Inter-Italic.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url("../fonts/Inter-Medium.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 500;
    font-display: swap;
    src: url("../fonts/Inter-MediumItalic.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url("../fonts/Inter-SemiBold.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 600;
    font-display: swap;
    src: url("../fonts/Inter-SemiBoldItalic.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url("../fonts/Inter-Bold.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 700;
    font-display: swap;
    src: url("../fonts/Inter-BoldItalic.woff2") format("woff2");
} 
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 800;
    font-display: swap;
    src: url("../fonts/Inter-ExtraBold.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 800;
    font-display: swap;
    src: url("../fonts/Inter-ExtraBoldItalic.woff2") format("woff2");
} 
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 900;
    font-display: swap;
    src: url("../fonts/Inter-Black.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter';
    font-style: italic;
    font-weight: 900;
    font-display: swap;
    src: url("../fonts/Inter-BlackItalic.woff2") format("woff2");
}

/* -------------------------------------------------------
  Variable font.
  Usage:
  
    html { font-family: 'Inter', sans-serif; }
    @supports (font-variation-settings: normal) {
      html { font-family: 'Inter var', sans-serif; }
    }
*/
@font-face {
    font-family: 'Inter var';
    font-weight: 100 900;
    font-display: swap;
    font-style: normal;
    font-named-instance: 'Regular';
    src: url("../fonts/Inter-roman.var.woff2") format("woff2");
}
@font-face {
    font-family: 'Inter var';
    font-weight: 100 900;
    font-display: swap;
    font-style: italic;
    font-named-instance: 'Italic';
    src: url("../fonts/Inter-italic.var.woff2") format("woff2");
}
  
  
/* --------------------------------------------------------------------------
  [EXPERIMENTAL] Multi-axis, single variable font.
  
  Slant axis is not yet widely supported (as of February 2019) and thus this
  multi-axis single variable font is opt-in rather than the default.
  
  When using this, you will probably need to set font-variation-settings
  explicitly, e.g.
  
    * { font-variation-settings: "slnt" 0deg }
    .italic { font-variation-settings: "slnt" 10deg }
  
*/
@font-face {
    font-family: 'Inter var experimental';
    font-weight: 100 900;
    font-display: swap;
    font-style: oblique 0deg 10deg;
    src: url("../fonts/Inter.var.woff2") format("woff2");
}

.skip-to-main-content-link { position: absolute; top: 1rem; left: 1rem; z-index: 100; }
:focus-visible { outline: .125rem solid #fff; -webkit-box-shadow: 0 0 0 .125rem #0b1f42; box-shadow: 0 0 0 .125rem #0b1f42; border-radius: .5rem; outline-offset: 0.125rem; }

html { font-size: 62.5%; background-color: var(--bs-body-bg); }
a{cursor:pointer;outline:none;text-decoration:none; color: #2870ed;}
a:hover { text-decoration: underline; }
.no-script-msg{font:0.750em Arial, verdana, Helvetica, sans-serif;background:#FFFFCC url(gfx/icon-noScript.gif) no-repeat 0.5rem 1rem;width:auto;padding:0.625em 0.625em 0.625em 1.5em;margin:0.5em;border:0.1rem solid #CD3D18;font-weight:bold;height:auto;font-size:1.1rem;color:#000000;line-height:1.5em;clear:both;}
input[type="text"], input[type="email"], input[type="password"],input[type="number"], input[type="tel"], input[type="button"],input[type="reset"],input[type="submit"], button{-webkit-font-smoothing:antialiased;-moz-font-smoothing:antialiased; font-family: 'Inter'; -webkit-appearance:none; -moz-appearance:none; appearance:none; font-weight:normal;}

body,html{position:relative;}
html.mobile, html.mobile body{overflow-x:hidden;}

body{ -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; text-rendering: geometricPrecision; overflow-x:hidden; }

body.body { min-width: 320px; }

::-webkit-input-placeholder {color: rgba(0, 0, 0, 0.50);}
:-moz-placeholder {color: rgba(0, 0, 0, 0.50);}
::-moz-placeholder {color: rgba(0, 0, 0, 0.50);}
:-ms-input-placeholder {color: rgba(0, 0, 0, 0.50);}
 
.conceal{border: 0;clip: rect(0 0 0 0);height: 0.1rem;margin: -0.1rem 0 0 0;overflow: hidden;padding: 0;width: 100%;text-indent: -99999px; }

img { max-width: 100%; }

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 { -webkit-margin-after: 2rem; margin-block-end: 2rem;font-weight: 700; line-height: 1.25em; -webkit-margin-before: 0; margin-block-start: 0; font-style: normal; }


h1,
.h1 { font-size: 3.1rem; line-height:1.212; }
h2,
.h2 { font-size: 2.2rem; line-height: 1.200; }
h3,
.h3 { font-size: 2.2rem; line-height: 1.250; }
h4,
.h4 { font-size: 1.8rem; line-height: 1.227; }
h5,
.h5 { font-size: 1.6rem; }
h6,
.h6 { font-size: 1.6rem; }


.blockquote,
  blockquote { font-size: 1.8rem; -webkit-margin-after: 2rem; margin-block-end: 2rem;font-weight: 700;font-style: italic; position: relative; }

p { margin-bottom: 2rem; }

.p1 {font-size: 1.2rem; }
.p2 {font-size: 1rem; }

.p1 > :last-child,
.p2 > :last-child { -webkit-margin-after: 0; margin-block-end: 0; }

hr { display: block; margin: 0 0 2.4rem 0; border: 0; height: 0.1rem; width: 100%; background-color: #E0E0E0; opacity: 1; }

ul, ol { display: block; margin: 0; -webkit-margin-after: 2rem; margin-block-end: 2rem; padding: 0; -webkit-padding-start: 3.4rem; padding-inline-start: 3.4rem; list-style: none; list-style: none; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; gap: 0.8rem; margin: 1.6rem 0; text-align: left;}

ul:first-child:not(.no-firstchild),
ol:first-child:not(.no-firstchild) { -webkit-margin-before: 0; margin-block-start: 0; }

ul:last-child:not(.no-lastchild),
ol:last-child:not(.no-lastchild) { -webkit-margin-after: 0; margin-block-end: 0; }

ul > li:last-child,
ol > li:last-child,
ul > li > :last-child,
ol > li > :last-child { -webkit-margin-after: 0; margin-block-end: 0; }

ul ul,
ul ol,
ol ol,
ol ul { margin: 0; }

li > ul,
li > ol { padding-top: 0.75rem; }

li { position: relative; }

ul > li { display: block; list-style: none; }
ul > li::before { content: ""; display: block; position: absolute; top: calc(( 2.4rem - 2.4rem) / 2); left: -3.4rem; width: 2.4rem; height: 2.4rem; background-color: #2870ed; border-radius: 50%; }
ul > li::after { content: "";display: inline-block;height: 0.9rem;width: 0.5rem;position: absolute;top: 0.6rem;left: -2.4rem;border-bottom: 0.2rem solid #fff;border-right: 0.2rem solid #fff;-webkit-transform: rotate(45deg);-ms-transform: rotate(45deg);transform: rotate(45deg); }

ul > li > ul > li::before { content: ""; display: block; position: absolute; top: calc(( 2.4rem - 2.4rem) / 2); left: -3.4rem; width: 2.4rem; height: 2.4rem; background-color: rgba(40, 112, 237, 0.25); border-radius: 50%; }
ul > li > ul > li::after { content: "";display: inline-block;height: 0.9rem;width: 0.5rem;position: absolute;top: 0.6rem;left: -2.4rem;border-bottom: 0.2rem solid #2870ed;border-right: 0.2rem solid #2870ed;-webkit-transform: rotate(45deg);-ms-transform: rotate(45deg);transform: rotate(45deg); }

ol { counter-reset: li; }
ol > li { display: block; list-style: none outside none; z-index:0; }
ol > li::before { content: counter(li, decimal); counter-increment: li; left: -3.4rem; position: absolute; text-align: right; top: 0; width: 2.6rem; font-weight: 400;font-size: 1.6rem;color: #ffffff;width: 2.4rem;text-align: center; }
ol > li:after { content: "";position: absolute;left: -3.4rem;top: calc(( 2.4rem - 2.4rem) / 2);width: 2.4rem;height: 2.4rem;background-color: #2870ed;border-radius: 50%;z-index: -1; }
ol > li > ol > li::before { content: counter(li, lower-alpha); font-size: 1.4rem; color: #2870ed; }
ol > li > ol > li::after { background-color: rgba(40, 112, 237, 0.25); }

dl { -webkit-margin-after: 2rem; margin-block-end: 2rem; }
dd { margin: 0; }

html select option { font-family: Arial, Helvetica, sans-serif; }


/* button */
.btn-spacer { font-size: 1em; line-height: normal; display:-webkit-box; display:-ms-flexbox; display:flex; -ms-flex-wrap: wrap; flex-wrap: wrap; }
.btn{ background-color: rgba(0, 0, 0, 0);border: 0.2rem solid rgba(255, 255, 255, 0.5);border-radius: 10rem;color: #fff;cursor: pointer;font-size: 1.6rem;font-weight: 600;line-height: 2.4rem;padding: 0.95rem 3.2rem;display: -webkit-box;display: -ms-flexbox;display: flex;-webkit-box-align: center;-ms-flex-align: center;align-items: center;-webkit-box-pack: center;-ms-flex-pack: center;justify-content: center;text-align: center;-webkit-transition: background-color .25s,border .25s,color .25s,-webkit-box-shadow .25s;transition: background-color .25s,border .25s,color .25s,-webkit-box-shadow .25s;-o-transition: background-color .25s,border .25s,box-shadow .25s,color .25s;transition: background-color .25s,border .25s,box-shadow .25s,color .25s;transition: background-color .25s,border .25s,box-shadow .25s,color .25s,-webkit-box-shadow .25s; font-family: 'Inter'; }

.btn:focus, 
.btn:active:focus, 
.btn.active:focus, 
.btn.focus, 
.btn:active.focus, 
.btn.active.focus { outline: none; outline-offset: 0; }



.btn i { font-size: 1.25em; margin: 0 1.2rem; line-height: 1; }
.btn-text { display: block; margin: 0 1.2rem; position: relative; }
.btn i + .btn-text,
.btn .btn-text + i { margin-left: 0; }


.btn-sm { font-size: 1.4rem; line-height: 1.33; padding: 0.3rem 1.5rem; }
.btn-sm i { font-size: 1.125em; margin: 0 0.4rem; line-height: 1; }
.btn-sm i + .btn-text,
.btn-sm .btn-text + i { margin-left: 0; }

.btn > :first-child { margin-left: 0; }
.btn > :last-child { margin-right: 0; }

.btn.large{font-size:1.125em;font-weight: 600;padding: 0.770em 1.500em;}

.btn:hover, .btn:focus, .btn:active{border-bottom-color: #eeeeee;-webkit-box-shadow: none;box-shadow: none;text-decoration: none;outline:0;}
.btn:active {border-top-color: #cccccc;-webkit-box-shadow: inset 0 0.2rem 0 0 #cccccc;box-shadow: inset 0 0.2rem 0 0 #cccccc;}

.btn-primary{ color:#fff !important;background-color: #256BDE;border-color: #2870ed;-webkit-box-shadow: none;box-shadow: none; }
.btn-primary:hover,
.btn-primary:active { background-color: #064ea1; border-color: #064ea1; -webkit-box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(26, 103, 210, 0.6); box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(26, 103, 210, 0.6); text-decoration: none;outline:0;}

.btn-primary:focus { background-color: #064ea1; border-color: #064ea1; text-decoration: none; outline:0; }
.btn-primary:active { background-color: #1658b3 !important; border-color: rgba(0, 0, 0, 0) !important; -webkit-box-shadow: 0 0.4rem 0.4rem -0.4rem rgba(26, 103, 210, 0.6) !important; box-shadow: 0 0.4rem 0.4rem -0.4rem rgba(26, 103, 210, 0.6) !important;}

.btn-white{ color: #2870ed !important;background-color: #fff;border-color: transparent;-webkit-box-shadow: none;box-shadow: none; }
.btn-white:hover,
.btn-white:active { background-color: rgba(255, 255, 255, 0.85); border-color: transparent; -webkit-box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.45); box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.45); text-decoration: none; outline:0; }
.btn-white:focus { background-color: rgba(255, 255, 255, 0.85); border-color: transparent; text-decoration: none; outline:0; }

.btn-white:active { background-color: rgba(255, 255, 255, 0.8) !important; border-color: transparent !important; -webkit-box-shadow: 0 0.4rem 0.4rem -0.4rem rgba(0, 0, 0, 0.45) !important; box-shadow: 0 0.4rem 0.4rem -0.4rem rgba(0, 0, 0, 0.45) !important;}


.btn-white.disabled, .btn-white[disabled]{ opacity: 1 !important; background-color: rgba(255, 255, 255, 0.3) !important; color: rgba(255, 255, 255, 0.45) !important; border-color: transparent; }

.btn-outline-primary { color: #256BDE;border-color: #256BDE;background-color: transparent;-webkit-box-shadow:none;box-shadow:none;}
.btn-outline-primary:hover, .btn-outline-primary:focus{background-color:rgba(255, 255, 255, .2); color: #2970ED;border-color:#2970ED; -webkit-box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.3); box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.3);}
.btn-outline-primary:active{background-color:rgba(255, 255, 255, .4) !important; color: #2970ED !important;border-color:#2970ED !important; -webkit-box-shadow: 0 .4rem .4rem -.4rem rgba(0, 0, 0, 0.3) !important; box-shadow: 0 .4rem .4rem -.4rem rgba(0, 0, 0, 0.3) !important;}

.btn-secondary-alt { color: #2870ed;border-color: #a3b5c9;background-color: rgba(0, 0, 0, 0);-webkit-box-shadow:none;box-shadow:none;}
.btn-secondary-alt:hover{color: #2870ed !important; background-color: rgba(26, 103, 210, 0.06); border-color:#94a9c0; -webkit-box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.3); box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.3);}

.btn-secondary-alt:focus{color: #2870ed !important; background-color: rgba(26, 103, 210, 0.06); border-color:#94a9c0; }
.btn-secondary-alt:active{color: #2870ed !important; background-color: rgba(33, 65, 108, 0.1) !important; border-color:#94a9c0 !important; -webkit-box-shadow: 0 .4rem .4rem -.4rem rgba(0, 0, 0, 0.3) !important; box-shadow: 0 .4rem .4rem -.4rem rgba(0, 0, 0, 0.3) !important;}

.btn-outline-white{color: #fff;border-color: rgba(255, 255, 255, 0.5);background-color: rgba(0, 0, 0, 0);-webkit-box-shadow:none;box-shadow:none;}
.btn-outline-white:hover {color: #fff !important;background-color: rgba(0, 0, 0, 0.1); border-color:rgba(255, 255, 255, 0.5); -webkit-box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.3); box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(0, 0, 0, 0.3);}
.btn-outline-white:focus{color: #fff !important;background-color: rgba(0, 0, 0, 0.1); border-color:rgba(255, 255, 255, 0.5);}
.btn-outline-white:active{ color: #fff !important;background-color: rgba(0, 0, 0, 0.15) !important; border-color:rgba(255, 255, 255, 0.5) !important; -webkit-box-shadow: 0 .4rem .4rem -.4rem rgba(0, 0, 0, 0.3) !important; box-shadow: 0 .4rem .4rem -.4rem rgba(0, 0, 0, 0.3) !important;}

.btn-link { --bs-btn-font-weight: 500; --bs-btn-disabled-color: rgba(37, 107, 222, 0.5); color: #256BDE; text-decoration: none; outline: none; border: 0.2rem solid transparent; font-size: 1.8rem; padding: 0.2rem; border-radius: 0.5rem; -webkit-box-shadow: none !important; box-shadow: none !important; }
.btn-link:not(:hover):focus { border-color: #256BDE; }

.btn.disabled, .btn[disabled]{cursor:not-allowed; opacity:.5; pointer-events: none; }

.btn-cta { color:#fff; background-color: #ff6129; border-color: #ff6129; -webkit-box-shadow: none; box-shadow: none; }
.btn-cta:hover, .btn-cta:active { color: #fff; background-color: #d25200; border-color: #d25200; -webkit-box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(210, 82, 0, 0.6); box-shadow: 0 0.4rem 1.2rem -0.4rem rgba(210, 82, 0, 0.6); text-decoration: none;outline:0;}
.btn-cta:focus { color: #fff; background-color: #d25200; border-color: #d25200; text-decoration: none; outline:0; }
.btn-cta:active { --bs-btn-active-color: #fff; --bs-btn-active-bg: #d95229; --bs-btn-active-border-color: #d95229; background-color: #d95229; border-color: #d95229; -webkit-box-shadow: 0 0.4rem 0.4rem -0.4rem rgba(210, 82, 0, 0.6) !important; box-shadow: 0 0.4rem 0.4rem -0.4rem rgba(210, 82, 0, 0.6) !important;}

.btn-cta.disabled, .btn-cta[disabled]{cursor:not-allowed; opacity: 1; pointer-events: none; background: #8D8D8D; color: #fff; border-color: transparent !important; -webkit-box-shadow: none !important; box-shadow: none !important; }
  
[class*="cc_icon-"] { font-size: 1em; width: 1em; max-width: 1em; -webkit-box-flex: 0; -ms-flex: 0 0 1em; flex: 0 0 1em; display: block; position: relative; }
[class*="cc_icon-"]::before { content: ""; display: block; padding-top: 100%; position: relative; width: 100%; z-index: 1;}
[class*="cc_icon-"] svg,
[class*="cc_icon-"] img { display: block; width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: 0; }
 
/*layout*/
.layout{position:relative; z-index:0;}

.container,
.row { --bs-gutter-x: 4rem; }

.cc-section-bg { position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 0; display: block; }
.cc-section-bg picture,
.cc-section-bg img { display: block; width: 100%; height: 100%; -o-object-fit: cover; object-fit: cover; }

.ratio img, .ratio video { -o-object-fit: cover; object-fit: cover; display: block; width: 100%; height: 100%; }
.ratio.contain img,
.ratio.contain video { -o-object-fit: contain; object-fit: contain; }

/* text-a */
.text-a { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; color: var(--cc-black-200-static); font-weight: 400; }
.text-a.text-center { -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-margin-start: auto; margin-inline-start: auto; -webkit-margin-end: auto; margin-inline-end: auto; }
.text-a > .btn-spacer { -webkit-margin-before: var(--cc-font-size); margin-block-start: var(--cc-font-size); }
.text-a > :first-child { margin-top: 0; }
.text-a > :last-child { margin-bottom: 0; }

/* form */
textarea { resize: none; }
textarea { scrollbar-width: thin; scrollbar-color: #2970ED #fff; }
textarea::-webkit-scrollbar { width: 1rem; }
textarea::-webkit-scrollbar-track { background: #fff; }
textarea::-webkit-scrollbar-thumb { background: #2970ED; }
textarea::-webkit-scrollbar-thumb:hover { background: #2970ED; }

.form-group { margin-bottom: 2.4rem; }
.form-control-wrapper { position: relative; }
.form-lbl { color: #141414;font-size: 1.6rem;font-style: normal;font-weight: 500;line-height: 1.188; display:block; }
.form-control { display: -webkit-box; display: -ms-flexbox; display: flex;width: 100%;height: 3.2rem;padding: 0.6rem 1rem; border-radius: 0.3rem;border: 0.1rem solid #D0D0D0;background-color: #FFF; color: #141414;font-size: 1.4rem;font-style: normal;font-weight: 500;line-height: normal; }
.form-group.has-error .form-control,
.form-control.is-invalid { border-color: #E91515; background-image: none !important; }
.required-lbl { display:block; margin: 0.5rem 0 0 0; color: #0A285C;font-size: 1.3rem;font-style: normal;font-weight: 400;line-height: normal; }
.error-txt { display:none; margin: 0.5rem 0 0 0; position: absolute; color: #E91515;font-size: 1.3rem;font-style: normal;font-weight: 400;line-height: normal; }
.error-txt a { text-decoration: underline; color: #2870ED; font-weight: 700; }
.error-txt a:hover { text-decoration: none; }
.form-group.has-error .error-txt,
.form-control.is-invalid ~ .error-txt { display:block; }
textarea.form-control { height: 25rem; resize: none; }

.form-control:-webkit-autofill:-webkit-autofill, 
.form-control:-webkit-autofill:-webkit-autofill:hover, 
.form-control:-webkit-autofill:-webkit-autofill:active, 
.form-control:-webkit-autofill:-webkit-autofill:focus { background-color: #fff !important;color: #303030 !important;-webkit-box-shadow: 0 0 0 1000px #fff inset !important;-webkit-text-fill-color: #303030 !important; font-size: 1.5rem !important; }
.form-control:-webkit-autofill:-webkit-autofill, .form-control:-webkit-autofill:-webkit-autofill:hover, .form-control:-webkit-autofill:-webkit-autofill:active, .form-control:-webkit-autofill:-webkit-autofill:focus { background-color: #fff !important;color: #303030 !important;-webkit-box-shadow: 0 0 0 100rem #fff inset !important;-webkit-text-fill-color: #303030 !important; font-size: 1.5rem !important; }
.form-control:autofill:-webkit-autofill,
.form-control:autofill:-webkit-autofill:hover,
.form-control:autofill:-webkit-autofill:active,
.form-control:autofill:-webkit-autofill:focus { background-color: #fff !important;color: #303030 !important;-webkit-box-shadow: 0 0 0 100rem #fff inset !important;-webkit-text-fill-color: #303030 !important; font-size: 1.5rem !important; }
.form-control:disabled { background-color: #E8E8E8; }

hr { margin: 3rem 0 1.1rem 0; }
.or-lbl { margin: 0 0 1.1rem 0; width: 100%; color: #0A285C;font-size: 1.4rem;font-style: normal;font-weight: 400;line-height: 1.714; text-align: center; display:block; }

.cc-dashboard { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; }
.cc-sidebar { visibility: hidden; width: 100%; background-color: #fff; min-width: 320px; position: fixed; top: 0;bottom: 0; left: 0; z-index: 111; white-space: nowrap; overflow: hidden;overflow-y: auto; -webkit-box-pack: start; -ms-flex-pack: start; justify-content: flex-start; -webkit-transition: all .45s ease; -o-transition: all .45s ease; transition: all .45s ease;opacity: 1;visibility: hidden;-webkit-transform: translateX(-100%);-ms-transform: translateX(-100%);transform: translateX(-100%); display: block; }
html.nav-expanded .cc-sidebar { visibility: visible;-webkit-transform: translateX(0px);-ms-transform: translateX(0px);transform: translateX(0px); }


.cc-sidebar-content { height: 100%; max-height: 100%; overflow: hidden; overflow-y: auto; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.cc-sidebar-content::-webkit-scrollbar { width: 0.6rem; }
.cc-sidebar-content::-webkit-scrollbar-track { background: #fff; border-radius: 0.6rem; }
.cc-sidebar-content::-webkit-scrollbar-thumb { background: #D0D0D0; border-radius: 0.6rem; }
.cc-sidebar-content::-webkit-scrollbar-thumb:hover { background-color: #848484; }

.cc-dashboard-body { width: 100%; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-transition: padding 0.3s ease; -o-transition: padding 0.3s ease; transition: padding 0.3s ease; }

.cc-body-content { min-height: 100vh; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.cc-body-header { background-color: #F9F9F9; border-bottom: 0.1rem solid #E8E8E8; padding: 0.8rem 2rem 0.7rem 2rem; min-height: 10rem; }
.cc-body-header-row { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -ms-flex-wrap: wrap; flex-wrap: wrap; }
.cc-body-header-l { -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; width: 1%; }
.cc-body-header-r { -ms-flex-negative: 0; flex-shrink: 0; }
.cc-body-header-l .text-a { font-size: 1.8rem; font-weight: 500; }
.cc-body-header-btns { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -ms-flex-wrap: wrap; flex-wrap: wrap; margin: 0 -0.6rem; }
.cc-body-header-btns-cell { padding: 0 0.6rem; }
.cc-body-content-row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; }
.cc-body-content-left { width: 1%; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; padding: 2rem; position: relative; }
.cc-body-content-right { -ms-flex-negative: 0; flex-shrink: 0; width: 32rem; background-color: #F9F9F9; border-left: 0.1rem solid #E8E8E8; }
.cc-body-content-scroll { position: relative; z-index: 11; overflow: hidden; overflow-y: auto; max-height: calc(100vh - 14rem); }
.cc-body-content-right .cc-body-content-scroll { max-height: calc(100vh - 10rem); min-height: calc(100vh - 10rem); }
.cc-body-content-left-content { padding: 0 4rem 0 0.8rem; position: relative; }
.cc-body-content-scroll::-webkit-scrollbar { width: 0.6rem; }
.cc-body-content-scroll::-webkit-scrollbar-track { background: #fff; border-radius: 0.6rem; }
.cc-body-content-scroll::-webkit-scrollbar-thumb { background: #D0D0D0; border-radius: 0.6rem; }
.cc-body-content-scroll::-webkit-scrollbar-thumb:hover { background-color: #848484; }
.cc-accordion { --bs-accordion-color: #141414; --bs-accordion-bg: #F9F9F9; --bs-accordion-border-color: #E8E8E8; --bs-accordion-btn-padding-x: 2.4rem; --bs-accordion-btn-padding-y: 1.6rem; --bs-accordion-body-padding-x: 0; --bs-accordion-body-padding-y: 0; --bs-accordion-border-radius: 0; --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(37, 107, 222, 0.12); --bs-accordion-active-bg: transparent; }
.cc-accordion .accordion-button { font-size: 2rem; font-weight: 600; line-height: 1.2; padding-right: 4.5rem; }
.cc-accordion .accordion-button::after { display: none; }
.cc-fap-icon { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; width: 1.6rem; height: 1.6rem; position: absolute;
top: 50%; -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); transform: translateY(-50%); right: 2rem; font-size: 1.6rem; -webkit-transition: -webkit-transform 0.2s ease-in-out; transition: -webkit-transform 0.2s ease-in-out; -o-transition: transform 0.2s ease-in-out; transition: transform 0.2s ease-in-out; transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out; }
.accordion-button:not(.collapsed) .cc-fap-icon { -webkit-transform: translateY(-50%) rotate(-180deg); -ms-transform: translateY(-50%) rotate(-180deg); transform: translateY(-50%) rotate(-180deg); }
.cc-visa-card-nav { padding: 0; margin: 0; gap: 0; }
.cc-visa-card-nav > li::before { display: none; }
.cc-visa-card-nav-btn { display: block; width: 100%; background-color: transparent; border: none; border-bottom: 0.1rem solid #E8E8E8; outline: none; padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x); text-align: left; font-size: 1.6rem; line-height: 1.222; font-weight: 500; }
.cc-visa-card-nav-btn:hover { background-color: rgba(37, 107, 222, 0.15); }
.cc-visa-card-nav-btn:not(:hover):focus { background-color: rgba(37, 107, 222, 0.12); }
.cc-visa-card-nav-btn.active { background-color: rgba(37, 107, 222, 0.10) !important; }
.cc-visa-card-nav-btn:focus:not(:focus-visible) { background-color: transparent; }
.cc-visa-card-faq-content { position: relative; }
.cc-visa-card-faq-content::before { display: block; content: ''; position: absolute; top: 0; right: -1.5rem; border-radius: 2rem; width: 0.5rem; height: 100%; background-color: rgba(37, 107, 222, 0.15); }
.cc-visa-card-content-box { border-radius: 0.3rem; background: rgba(37, 107, 222, 0.15); padding: 1rem; margin-bottom: 2.4rem; }
.cc-visa-card-content-box > :first-child { margin-top: 0; }
.cc-visa-card-content-box > :last-child { margin-bottom: 0; }
.cc-visa-card-content-box:last-child { margin-bottom: 0; }
.cc-site-edit-btn { width: 4rem; height: 4rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; font-size: 2.4rem; color: #256BDE; border: none; outline: none; background-color: transparent; padding: 0; position: absolute; top: 1rem; right: 0.5rem; border: 0.1rem solid transparent; border-radius: 0.3rem; -webkit-transition: border-color 0.3s ease; -o-transition: border-color 0.3s ease; transition: border-color 0.3s ease; z-index: 50; }
.cc-site-edit-btn:focus { border-color: #256BDE; }
.cc-choose-cover-row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; height: calc(100vh - 10rem); }
.cc-cover-page-img-cell { -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; width: 1%; }
.cc-cover-select-img-cell { width: 32rem; -ms-flex-negative: 0; flex-shrink: 0; background-color: rgba(33, 37, 41, 0.40); padding: 1.2rem 2rem 0 2.5rem; }
.cc-cover-select-scroll { overflow: hidden; overflow-y: auto; height: calc(100vh - calc(11.8rem)); padding-left: 0.5rem; padding-top: 0.5rem; }
.cc-cover-select-scroll::-webkit-scrollbar { width: 0.6rem; }
.cc-cover-select-scroll::-webkit-scrollbar-track { background: transparent; border-radius: 0.6rem; }
.cc-cover-select-scroll::-webkit-scrollbar-thumb { background: #fff; border-radius: 0.6rem; }
.cc-cover-select-scroll::-webkit-scrollbar-thumb:hover { background-color: #848484; }

.cc-cover-select-con { padding-right: 1.7rem; }
.cc-choose-cover-page-radio { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.cc-choose-cover-page-radio-cell { margin-bottom: 1rem; }
.cc-cover-page-imgbox { display: block; height: 100%; }
.cc-toggle { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: start; -ms-flex-align: start; align-items: flex-start; }
.cc-toggle input { width: 0; height: 0; position: absolute; top: 0; left: 0; opacity: 0; padding: 0; }
.cc-toggle input[disabled] ~ .cc-toggle-text-content { color: #727272; }
.cc-radio-ui-a { --bs-aspect-ratio: 82.11382113821138%; display: block; border-radius: 0.8rem; cursor: pointer; border: 0.1rem solid transparent; -webkit-transition: border-color 0.3s ease; -o-transition: border-color 0.3s ease; transition: border-color 0.3s ease; }
.cc-radio-ui-a > img { border-radius: 0.8rem; }
.cc-radio-ui-a-content { height: auto; width: auto; bottom: 0; left: 0; right: 0; top: auto; padding: 0.8rem 1rem; background-color: rgba(33, 37, 41, 0.40); -webkit-backdrop-filter: blur(2px); backdrop-filter: blur(2px); font-size: 1.4rem; color: #fff; -webkit-transition: background-color 0.3s ease; -o-transition: background-color 0.3s ease; transition: background-color 0.3s ease; border-radius: 0px 0px 0.8rem 0.8rem; }
.cc-toggle input:checked ~ .cc-radio-ui-a { border-color: #256BDE; }
.cc-toggle input:checked ~ .cc-radio-ui-a .cc-radio-ui-a-content { background-color: #256BDE; }
.cc-radio-ui-a::after { top: -0.3rem; right: -0.3rem; bottom: -0.3rem; left: -0.3rem; position: absolute; border: 0.2rem solid #256BDE; border-radius: 1.2rem; content: ''; display: block; opacity: 0; visibility: hidden; -webkit-transition: all .2s ease-in-out; -o-transition: all .2s ease-in-out; transition: all .2s ease-in-out; }
.cc-toggle input:not(:checked):focus ~ .cc-radio-ui-a::after { -webkit-transform: scale(1.015); -ms-transform: scale(1.015); transform: scale(1.015); opacity: 1; visibility: visible; }
.cc-prdocut-download-btn { position: absolute; top: 1rem; right: 13rem; z-index: 11; display: none; }
html.edit-mode .cc-prdocut-download-btn { display: -webkit-box; display: -ms-flexbox; display: flex; }
.cc-tab-covenants-wrap { position: relative; height: var(--fs-height); }
.cc-covenants-box { background-color: #fff; padding: 2rem 3.4rem 0 8rem; position: relative; z-index: 1; width: 73.94736842105263%; height: 100%; }
.cc-covenants-box-scroll { position: relative; z-index: 11; overflow: hidden; overflow-y: auto; height: calc(100vh - 14rem); }
.cc-covenants-box-scroll::-webkit-scrollbar { width: 0.6rem; }
.cc-covenants-box-scroll::-webkit-scrollbar-track { background: transparent; border-radius: 0.6rem; }
.cc-covenants-box-scroll::-webkit-scrollbar-thumb { background: #D0D0D0; border-radius: 0.6rem; }
.cc-covenants-box-scroll::-webkit-scrollbar-thumb:hover { background-color: #848484; }
.cc-covenants-box-content { padding-right: 5.4rem; }
.cc-covenants-header { font-size: 1.8rem; color: #434343; margin-bottom: 2.4rem; }
.cc-covenants-header-row { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin-bottom: 2.4rem; }
.cc-covenants-header-icon { font-size: 2.4rem; width: 2.4rem; height: 2.4rem; -ms-flex-negative: 0; flex-shrink: 0; margin-right: 1.2rem; color: #141414; }
.cc-covenants-header-row > .h4 { -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; width: 1%; margin: 0; }
.cc-covenants-low-box { border-radius: 1.6rem; border: 0.1rem solid #E8E8E8; padding: 1.6rem 2.4rem; background-color: #F9F9F9; margin-bottom: 2.4rem; }
.cc-covenants-low-box .cc-toggle { margin-bottom: 0.8rem; }
.cc-checkbox-ui-a { width: 1.4rem; height: 1.4rem; border:  0.1rem solid #141414; -ms-flex-negative: 0; flex-shrink: 0; font-size: 1rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; margin-right: 1.2rem; position: relative; top: 0.3em; cursor: pointer; }
.cc-checkbox-ui-a > i { opacity: 0; -webkit-transition: opacity 0.3s ease; -o-transition: opacity 0.3s ease; transition: opacity 0.3s ease; color: #fff; }
.cc-toggle-text-content { font-weight: 500; line-height: 1.222; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; width: 1%; color: #141414; }
.cc-toggle-text-content > span:last-child { margin-bottom: 0; }
.cc-toggle input[disabled] ~ .cc-checkbox-ui-a { cursor: default; }
.cc-toggle input:checked ~ .cc-checkbox-ui-a > i { opacity: 1; }
.cc-toggle input:checked ~ .cc-checkbox-ui-a { background-color: #256BDE; border-color: #256BDE; }
.cc-toggle input:focus ~ .cc-checkbox-ui-a { border-color: #256BDE; }
.cc-toggle-sub-text-content > .text-a { font-weight: 500; line-height: 1.222; }
.cc-toggle-sub-text-content { padding-left: 2.6rem; }
.cc-toggle-sub-text-content .text-a > ol { counter-reset: list; margin: 0; }
.cc-toggle-sub-text-content .text-a > ol > li::before { counter-increment: list; content: "(" counter(list, lower-alpha) ") "; color: #141414; font-weight: 500; }
.cc-toggle-sub-text-content .text-a > ol > li::after { display: none; }
.cc-toggle-sub-text-content .text-a > ol > li > ol {  counter-reset: list; }
.cc-toggle-sub-text-content .text-a > ol > li > ol > li::before { counter-increment: list; content: "(" counter(list, lower-roman) ") "; color: #141414; font-weight: 500; font-size: 1.6rem; }
.cc-toggle-sub-text-content .text-a > ol > li > ol > li::after { display: none; }
.cc-toggle-sub-text-content .text-a > * { margin-bottom: 0.8rem; }
.cc-covenants-box-content > :last-child { margin-bottom: 0; }
.cc-offcanvas-end { --bs-offcanvas-width: 76.3rem; min-width: var(--bs-offcanvas-width); --bs-offcanvas-padding-x: 3.2rem; --bs-offcanvas-padding-y: 1.2rem; }
.cc-offcanvas-end .offcanvas-header { padding-top: 3.2rem; }
.cc-offcanvas-end .offcanvas-body::-webkit-scrollbar { width: 0.6rem; }
.cc-offcanvas-end .offcanvas-body::-webkit-scrollbar-track { background: transparent; border-radius: 0.6rem; }
.cc-offcanvas-end .offcanvas-body::-webkit-scrollbar-thumb { background: #D0D0D0; border-radius: 0.6rem; }
.cc-offcanvas-end .offcanvas-body::-webkit-scrollbar-thumb:hover { background-color: #848484; }
.cc-send-offer-form-row { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin-bottom: 2.4rem; }
.cc-send-offer-form-l { width: 21rem; -ms-flex-negative: 0; flex-shrink: 0; }
.cc-send-offer-form-r { width: 1%; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; }
.cc-send-offer-form-row .form-group { margin-bottom: 0; width: 100%; }
.cc-send-thank-row { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin-bottom: 2.4rem; }
.cc-send-thank-gif { --bs-aspect-ratio: 135.4838709677419%; width: 6.2rem; display: block; margin-right: 3.6rem; -ms-flex-negative: 0; flex-shrink: 0; }
.cc-send-thank-text { font-size: 1.8rem; font-weight: 500; }
.cc-send-thank-btn { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: end; -ms-flex-pack: end; justify-content: flex-end; margin: 0 -0.8rem; padding-top: 2.4rem; -ms-flex-wrap: wrap; flex-wrap: wrap; }
.cc-send-thank-btn-cell { padding: 0 0.8rem; }
.cc-offcanvas-close { top: 2.5rem; position: absolute; left: -1.7rem; }
.cc-stepper-list { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -ms-flex-direction: row; flex-direction: row; padding: 0 0 1rem 0; margin: 0 -0.4rem; gap: 0; }
.cc-stepper-list-item { padding: 0 0.4rem; display: -webkit-box; display: -ms-flexbox; display: flex; }
.cc-stepper-list-ui { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; width: 4rem; height: 4rem; -ms-flex-negative: 0; flex-shrink: 0; border-radius: 50%; margin-right: 0.8rem; }
.cc-stepper-list-item-count { border: 0.1rem solid transparent; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; background-color: #0B1F42; border-radius: 50%; width: 2.4rem; height: 2.4rem; color: #fff; }
.cc-stepper-list-item-done { font-size: 2.4rem; width: 2.4rem; display: block; }
.cc-stepper-list-item-count-text { display: none; font-size: 1.6rem; font-weight: 600; line-height: normal; }
.cc-stepper-list-item-content { display: block; padding-top: 0.45rem; border-bottom: 0.1rem solid #0B1F42; min-width: 19rem; }
.cc-stepper-list-name { display: block; color: #141414; font-size: 1.4rem; font-weight: 500; line-height: 1.214; }
.cc-stepper-list > li::after,
.cc-stepper-list > li::before { display: none; }
.cc-stepper-list-item.active .cc-stepper-list-ui { background-color: rgba(11, 31, 66, 0.20); }
/* .cc-stepper-list-item.active .cc-stepper-list-item-count { background-color: #0B1F42; color: #fff; } */
.cc-stepper-list-item.active .cc-stepper-list-item-done { display: none; }
.cc-stepper-list-item.active .cc-stepper-list-item-count-text { display: block; }
.cc-stepper-list-item.active ~ .cc-stepper-list-item .cc-stepper-list-item-done { display: none; }
.cc-stepper-list-item.active ~ .cc-stepper-list-item .cc-stepper-list-item-count-text { display: block; }
.cc-stepper-list-item.active ~ .cc-stepper-list-item .cc-stepper-list-item-count { background-color: #fff; border-color: #E7E7E7; color: #434343; }
.cc-submit-btn { padding: 0.3rem 2.3rem; font-size: 1.6rem; font-weight: 600; }

.cc-get-token-wrap { width: 100%; min-height: 100vh; padding: 2rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; background-color: #006366; }
.cc-get-token-container { max-width: 59.2rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.cc-erst-logo { display: block; margin-bottom: 3rem; }
.cc-erst-logo > img { width: auto; max-width: 100%; height: 5.9rem; -o-object-fit: contain; object-fit: contain; display: block; }
.cc-could-get-token-content .text-a { color: #fff; --bs-heading-color: #fff; font-weight: 600; }
.cc-could-get-token-content .text-a .h3 { font-weight: 600; }
.cc-could-get-token-content .text-a > * { margin-bottom: 2rem; }
.cc-could-get-token-content .text-a a { color: #fff; text-decoration: underline; font-size: 1.4rem; }
.cc-could-email-error { width: 100%; text-align: center; margin-top: 2.4rem; font-size: 1.6rem; color: #fff; font-weight: 500; position: static; }
.cc-could-get-token-form { max-width: 45.4rem; margin: 0 auto; }
.cc-could-enter-token-row { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin: 0 -0.4rem; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; padding-bottom: 2.4rem; }
.cc-could-enter-token-cell { padding: 0 0.4rem; }
.cc-could-enter-token-cell .form-group { margin: 0; width: 4rem; }
.cc-could-enter-token-cell .form-control { padding: 0.5rem; text-align: center; }
.cc-could-enter-token-form .cc-could-email-error { margin: 0; padding-bottom: 2.4rem; }
.cc-code-pblem { margin-top: 2.4rem; }
.cc-code-pblem .text-a > :last-child { margin-bottom: 0; }

.cc-customer-cvr-row { min-height: 100vh; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; background-color: #006366; }
.cc-customer-cvr-l { width: 100%; position: relative; }
.cc-customer-cvr-l::before { padding-top: 47.2%; width: 100%; content: ''; display: block; }
.cc-customer-cvr-r { width: 100%; }
.cc-customer-cvr-content { padding: 2rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.cc-could-get-token-content { text-align: center; }
.cc-could-get-token-content > .btn-spacer { -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.cc-customer-intro-wrap { padding-top: 7.5rem; padding-bottom: 7rem; position: relative; }
.cc-customer-intro-wrap .cc-section-bg { height: 7.5rem; animation-name: opacityAni; animation-duration: 0.3s; animation-timing-function: ease-out; animation-fill-mode: forwards; }
.cc-customer-intro-wrap .cc-section-bg img { -o-object-position: top left; object-position: top left; }
.cc-customer-intro-content { padding: 0; background-color: #fff; position: relative; animation-name: translateYAni; animation-duration: 0.6s; animation-timing-function: ease-out; animation-fill-mode: forwards; animation-delay: 0.3s; opacity: 0; }
.cc-customer-intro-content::before { left: 0.7rem; right: 0.7rem; top: -1.2rem; position: absolute; z-index: 1; height: 4.3rem; border-radius: 0.5rem;background-color: #FFF; content: ''; display: block; }
.cc-customer-intro-content .cc-visa-card-content-box { background-color: transparent; }
.cc-customer-intro-content .cc-visa-card-faq-content::before { display: none; }
.cc-customer-intro-content-in > .cc-tbl-a-title-row { margin-bottom: 3.1rem; }
.cc-customer-intro-content-in > .cc-tbl-a-title-row .cc-tbl-a-title-icon { margin: 0 1.4rem 0 0; }
.cc-customer-need-lbl { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -ms-flex-wrap: wrap; flex-wrap: wrap; margin-bottom: 2.4rem; }
.cc-customer-need-lbl > strong { margin-bottom: 1rem; display: block; }
.cc-customer-need-lbl .text-a { font-size: 1.4rem; font-weight: 500; }
.cc-customer-intro-content-in { padding: 0 2rem; position: relative; z-index:11; }

@keyframes opacityAni {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

@keyframes translateYAni {
    0% {
      transform: translateY(6rem);
      opacity: 0;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

@media(min-width: 375px){
    .cc-erst-logo { width: 20rem; }
    .cc-could-get-token-content .text-a > * { margin-bottom: 3rem; }
    .cc-could-get-token-content .text-a { font-size: 1.8rem; line-height: 1.333; }
    .cc-could-get-token-content .text-a > .h3 { font-size: 2.4rem; }
    .cc-could-get-token-content .text-a > .h4 { font-size: 2rem; }
    .cc-customer-cvr-content { padding: 3rem 2rem; }
}

.ed-product-wrap { padding: 1rem; height: calc(100vh - 14.6rem); }
.ed-product-row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; margin: 0 -1.6rem; height: 100%; }
.ed-product-left { padding: 0 1.6rem; }
.ed-product-right { padding: 0 1.6rem; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; width: 1%; }
.ed-product-dash-row { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; height: 100%; margin: 0 -1.6rem; }
.ed-product-collateral { padding: 0 1.6rem; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; width: 1%; }
.ed-product-financing { padding: 0 1.6rem; }
.ed-product-footer { height: 8rem; padding: 1.6rem 2.4rem; border-top: 0.1rem solid #E8E8E8; background-color: #F9F9F9; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: justify; -ms-flex-pack: justify; justify-content: space-between; }
.ed-product-footer .text-a { font-size: 1.8rem; color: #141414; }
.ed-product-group-box { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; border: 0.1rem solid #E8E8E8; background-color: #F9F9F9; border-radius: 2.4rem; height: 100%; width: 25rem; overflow: hidden; }
.ed-product-financing .ed-product-group-box { width: 42rem; }
.ed-product-collateral .ed-product-group-box { width: 100%; }
.ed-product-group-header { padding: 2.4rem 2.4rem 2rem 2.4rem; min-height: 9.5rem; border-bottom: 0.1rem solid #E8E8E8; }
.ed-product-group-body { -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; height: calc(100vh - 27rem); overflow: hidden; overflow-y: auto; padding: 2.4rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.ed-product-left .ed-product-group-body { padding: 0; }
.ed-product-group-body::-webkit-scrollbar { width: 0.6rem; }
.ed-product-group-body::-webkit-scrollbar-track { background: transparent; border-radius: 0.6rem; }
.ed-product-group-body::-webkit-scrollbar-thumb { background: rgba(0, 0, 0, 0.12); border-radius: 0.6rem; }
.ed-product-group-body::-webkit-scrollbar-thumb:hover { background-color: #848484; }
.ed-product-group-header .h5 { margin-bottom: 0.8rem; font-weight: 600; line-height: 1.2; }
.ed-product-group-header .h6 { line-height: 1.333; margin-bottom: 0.8rem; font-size: 1.8rem; }
.ed-pr-tab-navlink { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; width: 100%; }
.ed-product-tab { --bs-nav-pills-border-radius: 0; --bs-nav-link-padding-x: 2.4rem; --bs-nav-link-padding-y: 1.6rem; --bs-nav-pills-link-active-bg: linear-gradient(0deg, rgba(37, 107, 222, 0.10) 0%, rgba(37, 107, 222, 0.10) 100%); gap: 0; }
.ed-product-tab .nav-link { border: 0.1rem solid transparent; }
.ed-product-tab .nav-link.active { background: var(--bs-nav-pills-link-active-bg); border-color: #E8E8E8; }
.ed-product-tab > li::after,
.ed-product-tab > li::before { display: none; }
.ed-product-tab > li > .nav-link { width: 100%; -webkit-box-shadow: none !important; box-shadow: none !important; }
.ed-product-tab > li > .nav-link:focus { background: -o-linear-gradient(bottom, rgba(37, 107, 222, 0.12) 0%, rgba(37, 107, 222, 0.12) 100%); background: -webkit-gradient(linear, left bottom, left top, from(rgba(37, 107, 222, 0.12)), to(rgba(37, 107, 222, 0.12))); background: linear-gradient(0deg, rgba(37, 107, 222, 0.12) 0%, rgba(37, 107, 222, 0.12) 100%); }
.ed-pr-tab-navlink-icon { width: 2.4rem; height: 2.4rem; font-size: 2.4rem; color: #141414; margin-right: 1.2rem; -ms-flex-negative: 0; flex-shrink: 0; }
.ed-pr-tab-navlink-content > strong { font-size: 1.6rem; margin-bottom: 0.5rem; line-height: 1.188; display: block; color: #141414; }
.ed-pr-tab-navlink-content > span { display: block; color: #434343; font-size: 1.4rem; font-weight: 400; text-align: left; }
.ed-product-checkbox-group .cc-checkbox-ui-a { width: 1.9rem; height: 1.9rem; border-radius: 0.3rem; top: 0; border: 0.2rem solid #D0D0D0; }
.ed-product-checkbox-nested-group { padding-left: 3.6rem; }
.ed-product-checkbox-nested-group > .form-group { margin-bottom: 1.4rem; margin-top: 1.4rem; }
.ed-choose-covenants { margin-top: auto; padding-top: 2rem; }



#gjs { height: calc(100vh - 14rem) !important;}
.gjs-cv-canvas { width: 100%; top: 0; height: 100%; }
.cc-edditor-wrap { padding: 3.1rem 0; }
.cc-edditor-wrap-box { padding: 1.2rem 2.8rem; border-bottom: 0.1rem solid #E8E8E8; }
.cc-edditor-wrap .gjs-title { background-color: #F9F9F9; letter-spacing: normal; border-bottom: 0.1rem solid transparent; padding: 1.2rem 2.8rem 1.2rem 6rem; font-size: 1.6rem; font-style: normal; font-weight: 500; line-height: 1.25; color: #141414; }
.gjs-one-bg { background-color: #F9F9F9; }
.cc-edditor-lbl,
.gjs-block-label,
.gjs-layer-name { font-size: 1.6rem; font-style: normal; font-weight: 500; line-height: 1.25; color: #141414; font-family: 'Inter'; }
.gjs-blocks-c { padding: 0.8rem 0.6rem 0.8rem 4rem; display: -webkit-box !important; display: -ms-flexbox !important; display: flex !important; }
.gjs-title > i { display: none !important; }
.gjs-block-category .gjs-title::before { background-size: 2.4rem; background-repeat: no-repeat; position: absolute; top: 50%; left: 2.8rem; -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); transform: translateY(-50%); display: block; content: ''; width: 2.4rem; height: 2.4rem; }
.gjs-block-category:nth-child(1) .gjs-title::before { background-image: url('../gfx/text_fields.svg'); }
.gjs-block-category:nth-child(2) .gjs-title::before { background-image: url('../gfx/title_filed.svg'); }
.gjs-block-category:nth-child(3) .gjs-title::before { background-image: url('../gfx/image-line.svg'); }
.gjs-block-category:nth-child(4) .gjs-title::before { background-image: url('../gfx/table_chart.svg'); }
.gjs-block-category:nth-child(5) .gjs-title::before { background-image: url('../gfx/select_all.svg'); }
.gjs-blocks-c:empty { display: none !important; }
.gjs-layer-item { padding: 1.2rem 2.8rem; }
.gjs-layer-count { display: none; }
.gjs-layer.gjs-selected .gjs-layer-item { background: rgba(37, 107, 222, 0.15); }
.cc-edditor-wrap .gjs-two-color { color: #141414; }
.gjs-block { border: 1px solid transparent; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; margin: 0; width: auto;; -webkit-box-align: center;; -ms-flex-align: center;; align-items: center; min-width: auto; min-height: 4rem; padding: 0.7rem; -webkit-box-shadow: none !important; box-shadow: none !important; margin: 0; }
.gjs-block-category { border-bottom: 0.1rem solid #E8E8E8 !important; }
.gjs-block-label > i { font-size: 2.4rem; }
.gjs-block[title=H1] .gjs-block-label { font-weight: 600; font-size: 3.2rem; }
.gjs-block[title=H2] .gjs-block-label { font-weight: 600; font-size: 2.4rem; }
.gjs-block[title=H3] .gjs-block-label { font-weight: 600; font-size: 2rem; }
.gjs-block[title=H4] .gjs-block-label { font-weight: 600; font-size: 1.8rem; }
.gjs-block[title=H5] .gjs-block-label { font-weight: 600; font-size: 1.6rem; }
.gjs-block[title=H6] .gjs-block-label { font-weight: 600; font-size: 1.4rem; }
.gjs-rte-toolbar.gjs-rte-toolbar-ui { border-color: #256BDE; background-color: #fff; }
.gjs-rte-toolbar.gjs-rte-toolbar-ui .gjs-rte-action { border-color: #256BDE; color: #256BDE; font-size: 1.4rem; font-weight: 600; width: 3rem; }
.gjs-rte-toolbar.gjs-rte-toolbar-ui .gjs-rte-action .fa{ font-size: 1.6rem; }
.toolbar-submenu { padding: 0; margin: 0; gap: 0; }
.table-toolbar-submenu-run-command { padding: 6px !important; font-size: 1.4rem; }
.toolbar-submenu li::before,
.toolbar-submenu li::after { display: none; }
.gjs-toolbar-items .fa .fa-cog.gjs-toolbar-item { display: none !important; }

[data-gjs-type="wrapper"] h1,
[data-gjs-type="wrapper"] h2,
[data-gjs-type="wrapper"] h3,
[data-gjs-type="wrapper"] h4,
[data-gjs-type="wrapper"] h5,
[data-gjs-type="wrapper"] h6 { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -ms-flex-wrap: wrap; flex-wrap: wrap; }
.heading { padding-right: 5px; padding-left: 5px; display: block; }
h1 > .heading:first-child, h2 > .heading:first-child, h3 > .heading:first-child, h4 > .heading:first-child, h5 > .heading:first-child, h6 > .heading:first-child { padding-left: 0; }
h1 > .heading:last-child, h2 > .heading:last-child, h3 > .heading:last-child, h4 > .heading:last-child, h5 > .heading:last-child, h6 > .heading:last-child { padding-right: 0; }
h1 span + .heading { padding-left: 0 !important; }
h2 span + .heading { padding-left: 0 !important; }
h3 span + .heading { padding-left: 0 !important; }
h4 span + .heading { padding-left: 0 !important; }
h5 span + .heading { padding-left: 0 !important; }
h6 span + .heading { padding-left: 0 !important; }
h1 span:nth-child(1) { margin-left: 0 !important; }

.cc-body-content-left-content.gjs-selected,
.cc-body-content-left-content.gjs-hovered,
.cc-body-content-scroll.gjs-selected,
.cc-body-content-scroll.gjs-hovered { outline: none !important; }
/* [data-gjs-type="wrapper"] .cc-body-content-scroll { max-height: 100vh; } */
.gjs-pn-panel { display: block; width: 100%; position: static;  }
.gjs-pn-views { padding: 1.2rem 2.8rem; border-bottom: 0.1rem solid #E8E8E8; }
.gjs-pn-views-container { padding: 0; -webkit-box-shadow: none; box-shadow: none; }
.gjs-pn-btn.gjs-pn-active { -webkit-box-shadow: none; box-shadow: none; background-color: #256BDE; color: #fff; border-radius: 0.5rem; font-size: 1.6rem; }
.gjs-sm-sector-title { padding: 1.2rem 2.8rem; font-weight: 400; border-color: #E8E8E8; }
.gjs-sm-sector { font-weight: 400; }
.gjs-sm-sector.gjs-sm-open .gjs-sm-sector-title { background-color: rgba(37, 107, 222, 0.15); }
.gjs-sm-sector-caret { width: auto; height: auto; }
.gjs-sm-clear { height: auto; }
.gjs-field { border-radius: 0.3rem; border: 0.1rem solid #D0D0D0;background-color: #FFF; color: #141414;font-size: 1.6rem;font-style: normal;font-weight: 500;line-height: normal; }
.gjs-sm-properties { padding: 1rem 2rem; }
.gjs-sm-field.gjs-sm-composite { border-color: #E8E8E8; }
.gjs-clm-tag > span { color: #fff; }
.gjs-sm-header { font-weight: 400; }
.gjs-block.cc-block-obj { width: 100%; }

.mob-ch-header { padding: 0.7rem; min-width: 320px; width: 100%; top: 0; left:0; position: fixed; z-index: 112; display: block;  animation-name: HeaderAni; animation-duration: 0.3s; animation-timing-function: ease-out; animation-fill-mode: forwards; }
.mob-ch-header-content { border-radius: 0.4rem;background-color: #256BDE; }
.mob-ch-header-row { padding: 0.4rem 0.8rem 0.4rem 1.1rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.mob-ch-header-right { margin-left: auto; display: -webkit-box; display: -ms-flexbox; display: flex; }
.mob-ch-header-logo { padding: 0.5rem; display: block; }
.mob-ch-header-logo > span { --bs-aspect-ratio: 126.3157894736842%; width: 1.9rem; display: block; }
.mob-ch-header-nav { margin: 0; padding: 0; list-style: none; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -ms-flex-direction: row; flex-direction: row; gap: 0; }
.mob-ch-header-nav > li { margin: 0 0 0 0.3rem; padding: 0; list-style: none; display: block; }
.mob-ch-header-nav > li::before,
.mob-ch-header-nav > li::after { display: none; }

@keyframes HeaderAni {
    0% {
      transform: translateY(-100%);
      opacity: 0;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

/* nav toggle btn */
.nav-toggle-btn { width: 40px;height: 40px;display: -webkit-box;display: -ms-flexbox;display: flex;-webkit-box-align: center;-ms-flex-align: center;align-items: center;-webkit-box-pack: center;-ms-flex-pack: center;justify-content: center;cursor: pointer; color: #fff; padding: 0; background-color: transparent; outline: none; border: none; }
.navtoggle-bars { position: relative;display: block;width: 18px;height: 12px; }
.navtoggle-bars .bar { -webkit-transition:all .25s ease;-o-transition:all .25s ease;transition:all .25s ease;height:2px;background:#fff;position:absolute; }
.navtoggle-bars .top { top:0;width:50%; }
.navtoggle-bars .middle { width:100%;top:5px; left: 0; }
.navtoggle-bars .bottom { width:50%;bottom:0; }
.navtoggle-bars .left { left:0; }
.navtoggle-bars .right { right:0; }
.navtoggle-bars .top.bar.left { -webkit-transform-origin:0 0;-ms-transform-origin:0 0;transform-origin:0 0; }
.navtoggle-bars .top.bar.right { -webkit-transform-origin:100% 0;-ms-transform-origin:100% 0;transform-origin:100% 0; }
.navtoggle-bars .bottom.bar.left { -webkit-transform-origin:0 100%;-ms-transform-origin:0 100%;transform-origin:0 100%; }
.navtoggle-bars .bottom.bar.right { -webkit-transform-origin:100% 100%;-ms-transform-origin:100% 100%;transform-origin:100% 100%; }
.nav-expanded .nav-toggle-btn .top.bar.left { -webkit-transform:translate(3px,-1px) rotate(45deg) scaleX(1.25);-ms-transform:translate(3px,-1px) rotate(45deg) scaleX(1.25);transform:translate(3px,-1px) rotate(45deg) scaleX(1.25); }
.nav-expanded .nav-toggle-btn .top.bar.right { -webkit-transform:translate(-3px,-1px) rotate(-45deg) scaleX(1.25);-ms-transform:translate(-3px,-1px) rotate(-45deg) scaleX(1.25);transform:translate(-3px,-1px) rotate(-45deg) scaleX(1.25); }
.nav-expanded .nav-toggle-btn .bottom.bar.left { -webkit-transform:translate(3px,2px) rotate(-45deg) scaleX(1.25);-ms-transform:translate(3px,2px) rotate(-45deg) scaleX(1.25);transform:translate(3px,2px) rotate(-45deg) scaleX(1.25); }
.nav-expanded .nav-toggle-btn .bottom.bar.right { -webkit-transform:translate(-3px,2px) rotate(45deg) scaleX(1.25);-ms-transform:translate(-3px,2px) rotate(45deg) scaleX(1.25);transform:translate(-3px,2px) rotate(45deg) scaleX(1.25); }
.nav-expanded .nav-toggle-btn .middle.bar { -webkit-transform:scaleX(0);-ms-transform:scaleX(0);transform:scaleX(0); }

.cc-customer-footer { padding: 1.1rem;width: 100%; min-width: 320px; bottom: 0; left: 0; position: fixed; z-index: 11; border-top: 0.1rem solid #E8E8E8;background-color: #FFF; display: block; }
.cc-customer-footer .btn-spacer { -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }

.mo-acc-a-item { margin-bottom: 0.5rem; border-radius: 0.5rem;border: 0.1rem solid #E8E8E8;background-color: #F9F9F9; }
.mo-acc-a-button { width: 100%; min-height: 5.4rem; padding: 1rem 3rem 1rem 1.5rem; border-radius: 0.5rem 0.5rem 0 0;background-color: rgba(37, 107, 222, 0.15); border: none; text-align: left; position:relative; color: #141414;font-size: 1.4rem;font-style: normal;font-weight: 600;line-height: normal; }
.mo-acc-a-button > i { font-size: 1.6rem; top: 50%; -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); transform: translateY(-50%); right: 1.5rem; position: absolute; z-index: 11; display: block; }

.bg-forest { background-color: var(--cc-forest) !important; }
.bg-stone { background-color: var(--cc-stone) !important; }
.bg-bright-blue { background-color: var(--cc-bright-blue) !important; }
.bg-teal { background-color: var(--cc-teal) !important; }
.bg-apple { background-color: var(--cc-apple) !important; }
.bg-orange { background-color: var(--cc-orange) !important; }
.bg-pink { background-color: var(--cc-pink) !important; }
.bg-aubergine { background-color: var(--cc-aubergine) !important; }

.covenant-input-u { border-bottom: 1px solid #333; }
.cc-td-right-border { border-right: 0.1rem solid #D0D0D0 !important; }


.page-break { page-break-after: always !important; background-color: transparent; border: 0; padding: 0.5rem; background-color: #000; color: #fff; text-align: center; margin-bottom: 2.4rem; font-weight: 700; }
.cc-customer-offer .page-break { page-break-after: always !important; height: 0 !important; overflow: hidden !important; background-color: transparent !important; border: 0 !important; padding: 0 !important; margin: 0 !important; }