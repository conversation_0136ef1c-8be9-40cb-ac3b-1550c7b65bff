<?php /** @noinspection ALL */

// Load .env.dev file
if(file_exists(__DIR__ . '/.env.dev')){
    $authToken = parse_ini_file(__DIR__ . '/.env.dev')['AUTH_TOKEN'] ?? '';
}
else{
    die('Environment file not found.');
}

class Router{
    private $routes = [];

    public function addRoute($method, $route, $callback){
        $this->routes[] = [
            'method'   => $method,
            'pattern'  => '#^' . preg_replace('#\{(\w+)\}#', '(\w+)', $route) . '$#',
            'callback' => $callback
        ];
    }

    public function dispatch($method, $uri){
        foreach($this->routes as $route){
            if($method === $route['method'] && preg_match($route['pattern'], $uri, $matches)){
                array_shift($matches); // Remove the full match
                return call_user_func_array($route['callback'], $matches);
            }
        }
        header("HTTP/1.0 404 Not Found");
        echo "404 Not Found";
    }
}

function checkAuthToken(){
    global $authToken;
    $headers = getallheaders();
    if(!isset($headers['Authorization']) || $headers['Authorization'] !== "Bearer $authToken"){
        file_put_contents(__DIR__ . '/.fail_auth.log', json_encode([
                'date'      => date('Y-m-d H:i:s'),
                'authToken' => $authToken,
            ], JSON_THROW_ON_ERROR) . PHP_EOL, FILE_APPEND);
        header("HTTP/1.0 401 Unauthorized");
        echo "401 Unauthorized";
        exit;
    }
}

function isValidEndpoint($endpoint){
    $allowedHosts = [
        '************',
        '************',
        '************',
        'esb.dev.imcplus.net',
        'esb.fat.imcplus.net',
        'esb.mona.imcplus.net',
        'esb.uat.imcplus.net',
        'esb.imcplus.net',
        'idp-a.alfa.s-mxs.net', // DEV | ************ ?
        'idp-p.alfa.s-mxs.net', // Production | ************
        'idp-q.alfa.s-mxs.net',
        'idp-a.alfa.s-mxs.net',
        'idp-e.alfa.s-mxs.net',
        'customercontacts-api.kdbapi.microf.ecp.erste.cloud',
        'erste.cloud',
    ];
    foreach($allowedHosts as $host){
        if(stripos($endpoint, $host) !== false){
            return true;
        }
    }
    return false;
}

function interactWithApi($method = 'POST', $url = "", $data = [], $headers = []): string{
    file_put_contents(__DIR__ . '/.out_requests.log', PHP_EOL . date("Y-m-d H:i:s") . PHP_EOL, FILE_APPEND);


    $ch = curl_init();
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    curl_setopt($ch, CURLOPT_STDERR, fopen(__DIR__ . '/.out_requests.log', 'ab'));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $method      = strtoupper($method);
    $requestBody = "";
    if($method === 'GET'){
        if($data){
            $url = sprintf("%s?%s", $url, http_build_query($data));
        }
        curl_setopt($ch, CURLOPT_HTTPGET, true);
        file_put_contents(__DIR__ . '/.out_requests.log', "Get Query Parameters" . PHP_EOL, FILE_APPEND);
    }
    else{
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        if(in_arrayi('x-www-form-urlencoded', $headers)){ // Content-Type: application/x-www-form-urlencoded
            $requestBody = http_build_query($data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
            file_put_contents(__DIR__ . '/.out_requests.log', "Form / Urlencoded Body" . PHP_EOL, FILE_APPEND);
        }
        else{
            $requestBody = json_encode($data, JSON_THROW_ON_ERROR);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
            file_put_contents(__DIR__ . '/.out_requests.log', "JSON Body" . PHP_EOL, FILE_APPEND);
        }
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_URL, $url);

    file_put_contents(__DIR__ . '/.out_requests.log', "---START---" . PHP_EOL, FILE_APPEND);
    file_put_contents(__DIR__ . '/.out_requests.log', "Request Body: " . $requestBody . PHP_EOL, FILE_APPEND);
    file_put_contents(__DIR__ . '/.out_requests.log', "Headers Sent: " . json_encode($headers, JSON_THROW_ON_ERROR) . PHP_EOL, FILE_APPEND);

    $response = curl_exec($ch);

    file_put_contents(__DIR__ . '/.out_requests.log', "Response Body: " . $response . PHP_EOL, FILE_APPEND);
    file_put_contents(__DIR__ . '/.out_requests.log', "---END---" . PHP_EOL, FILE_APPEND);

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return json_encode(['code' => $httpCode, 'response' => $response], JSON_THROW_ON_ERROR);
}

function in_arrayi($needle, $haystack): bool{
    $haystack = $haystack ?? [];
    foreach($haystack as $item){
        if(stripos($item, $needle) !== false){
            return true;
        }
    }
    return false;
}

$router = new Router();
$router->addRoute('GET', '/api_proxy_test/', function (){
    echo "-";
});
$router->addRoute('POST', '/api_proxy_test/api/protected', function (){
    checkAuthToken();
    $input       = file_get_contents('php://input');
    $requestData = json_decode($input, true, 512, JSON_THROW_ON_ERROR);

    $method  = $requestData['method'] ?? '';
    $url     = $requestData['url'] ?? '';
    $data    = $requestData['data'] ?? [];
    $headers = $requestData['headers'] ?? [];

    if(!isValidEndpoint($url)){
        header("HTTP/1.0 400 Bad Request");
        echo "400 Bad Request: Invalid API endpoint.";
        exit;
    }

    file_put_contents(__DIR__ . '/requests.log', json_encode([
            'date'    => date('Y-m-d H:i:s'),
            'method'  => $method,
            'url'     => $url,
            'data'    => $data,
            "headers" => $headers,
        ], JSON_THROW_ON_ERROR) . PHP_EOL, FILE_APPEND);

    echo interactWithApi($method, $url, $data, $headers);
});

$router->dispatch($_SERVER['REQUEST_METHOD'], parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));
