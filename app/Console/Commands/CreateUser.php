<?php

namespace App\Console\Commands;

use App\Code\GoogleAuthenticator;
use App\Framework\src\Http\Models\Admin;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateUser extends Command{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-user {user_name} {user_password} {user_email=none}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates a user';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(){
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(){
        // Unpack arguments
        $userName     = $this->argument("user_name");
        $userPassword = $this->argument("user_password");
        $userEmail    = $this->argument("user_email");

        // Fetch or prepare create new admin
        $admin = Admin::selectRaw("*," . AESdecypt('email'))->where('username', $userName)->first();
        if(!$admin){
            $newUser = true;
            $admin   = new Admin;
        }
        else $newUser = false;

        // Save admin user fields
        $admin->username = sanitizeStr($userName);
        if($userEmail != "none") $admin->email = sanitizeStr($userEmail);
        else if(filter_var($userName, FILTER_VALIDATE_EMAIL)) $admin->email = sanitizeStr($userName);
        $admin->password  = Hash::make($userPassword);
        $admin->user_type = "admin";

        // Generate user gauth fields
        $ga               = new GoogleAuthenticator();
        $admin->ga_secret = $ga->createSecret();
        $admin->qrcode    = $ga->getQRCodeGoogleUrl(config("app.google_auth_identifier") . "_" . slugify_string($userName), $admin->ga_secret);
        $admin->save();

        // Info
        $this->info(sprintf("User '%s' %s! " . PHP_EOL . "GAuth-QR-Url: %s", $admin->username, ($newUser ? "created" : "updated"), $admin->qrcode));
    }
}
