<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class HealthCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $result = $next($request);
        /* @var Response $result */
        if($result instanceof Response){
            if($result->getStatusCode() > 200) return new Response("unhealthy", 500);
            else return new Response("healthy", 200);
        }
        return $result;
    }
}
