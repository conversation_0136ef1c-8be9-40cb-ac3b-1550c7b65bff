<?php

namespace App\Http\Middleware;

use App\Framework\src\Code\GeoLocation;
use Closure;
use GeoIp2\Exception\AddressNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use MaxMind\Db\Reader\InvalidDatabaseException;

class RequestLanguage{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next){
        $userLanguage = session()->has("user_language") ? session()->get("user_language") : false;
        if(!$userLanguage){
            // Handle request language
            try{
                $location   = app(GeoLocation::class)->get();
                $regionCode = $location ? $location->country->isoCode : "GB"; // DE || GB etc.
                $lang = @$_SERVER['HTTP_ACCEPT_LANGUAGE'] ? substr(@$_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2) : "de";
                $locale = $lang . "_" . $regionCode;
                setUserLanguage($locale);
            }
            catch(AddressNotFoundException | InvalidDatabaseException $e){
                Log::error($e);
            }
        }
        else setUserLanguage($userLanguage);
        return $next($request);
    }
}
