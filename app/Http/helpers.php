<?php
use App\Framework\src\Code\RequestLanguage;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Http\Models\Product;
use App\Framework\src\Http\Models\DataRevision;
use App\Http\Models\Institute;
use App\Http\Models\Offer;
use App\Http\Models\User;
use App\Http\Models\UserGroupProduct;
use App\Http\Models\UserPermission;
use App\Mail\SendEmail;
use Illuminate\Support\Facades\Mail;

function getMobileDetect() : Mobile_Detect{
    return app(Mobile_Detect::class);
}

function adminContainerClass($active_menu)  {

    if($active_menu === "settings" || $active_menu === "app_texts")
        return 'apps-container';
    else if($active_menu === "institute_setup")
        return 'apps-email';

    return '';
}

function fix_user_institute($ins){
	$institute_id = $ins != null ? str_pad(preg_replace("/[^0-9]/", "", $ins), 4, "0", STR_PAD_LEFT) : '';
	return $institute_id;
}

function ccReplaceVariables($string, $data)  {

    foreach($data as $field => $val)  {
        $string = str_replace("[".$field."]", $val, $string);
    }

    return $string;
}

function phoneToFax($original){

    $parts = explode(' ', $original);
    if($parts)
    {
        $parts[count($parts) - 1] = '9' . $parts[count($parts) - 1];
        $original = implode(' ', $parts);
    }
    return $original;
}

function replaceNameVars($string, $offer, $user, $institute, $customer_name='')  {

    $institute_name = $institute->pdf_institute_name ?? $institute->institute_name;
    $customer_name = $offer ? $offer->customer_name : $customer_name;

    $user_fax = phoneToFax($user->saml_phone);

    $findArr = ["^(P.Person.Briefanrede^)", "^(P.Person.BetreuerVorname^!LZL^)", "^(P.Person.BetreuerNachname^)", "^(P.Person.BetreuerEmail^)", "^(P.Person.BetreuerKlappe^)", "^(INST.FIRMENWORTLAUTLANG1^) ^(INST.FIRMENWORTLAUTLANG2^)", "^(Inst.Homepage^)", "^(Inst.BLZ^)"];
    $findArr2 = ["[customer_name]", "[employee_first_name]", "[employee_last_name]", "[employee_email]", "[employee_phone]", "[institute_name]", "[institute_website]", "[institute_phone]","[employee_fax]"];
    $replaceArr = [$customer_name, $user->first_name, $user->last_name, $user->email, $user->saml_phone, $institute_name, $institute->website, $institute->phone,$user_fax];

    $return_string = str_replace($findArr, $replaceArr, $string);
    $return_string = str_replace($findArr2, $replaceArr, $return_string);

    return $return_string;
}

function addValueInputs($string, $name, $addSpan=true, $recId=false)  {

    if($addSpan)  {

        /*$input = '<span class="form-control-wrapper input-width-a"><span class="form-control">&nbsp;</span></span>';
        $inputM = '<span class="form-control-wrapper input-width-b"><span class="form-control">&nbsp;</span></span>';
        $inputS = '<span class="form-control-wrapper input-width-c"><span class="form-control">&nbsp;</span></span>';
        $inputA = '<span class="form-control-wrapper input-width-d"><span class="form-control">&nbsp;</span></span>';

        $input = '</span>'.$input.'<span class="compo-un-list-text">';
        $inputM = '</span>'.$inputM.'<span class="compo-un-list-text">';
        $inputS = '</span>'.$inputS.'<span class="compo-un-list-text">';
        $inputA = '</span>'.$inputA.'<span class="compo-un-list-text">';*/

        $input = '<span class="form-control-wrapper input-width-d"><span class="form-control">&nbsp;</span></span>';
        $input = '</span>'.$input.'<span class="compo-un-list-text">';
    }
    else  {

        /*
        $input = '<span class="form-control-wrapper input-width-a"><input type="text" name="'.$name.'['.$recId.'][]" class="form-control editor-input" autocomplete="off"></span>';
        $inputM = '<span class="form-control-wrapper input-width-b"><input type="text" name="'.$name.'['.$recId.'][]" class="form-control editor-input" autocomplete="off"></span>';
        $inputS = '<span class="form-control-wrapper input-width-c"><input type="text" name="'.$name.'['.$recId.'][]" class="form-control editor-input" autocomplete="off"></span>';
        $inputA = '<span class="form-control-wrapper input-width-d"><input type="text" name="'.$name.'['.$recId.'][]" class="form-control editor-input" autocomplete="off"></span>';
        */

        $input = '<span class="form-control-wrapper input-width-a"><input type="text" name="'.$name.'['.$recId.'][]" class="form-control editor-input" autocomplete="off"></span>';
    }

    $string = str_replace(["[INPUT]", "[INPUTM]", "[INPUTS]", "[INPUTA]"], [$input, $input, $input, $input], $string);

    if($addSpan)  {
        $string = '<span class="compo-un-list-text">'.$string.'</span>';
        $string = str_replace('<span class="compo-un-list-text"></span>', '', $string);
    }

    return $string;
}

function module_text_prefixes()  {

    $prefixArr = [
        //'module1' => 'a_module1',    //e.g. a_module1,z_module9
        //'module2' => 'b_module2',
    ];

    return $prefixArr;
}

function module_types($module=false, $field=false)  {

    $module_types = [
        /*'module1' => [
            'title' => 'Module 1',
        ],
        'module2' => [
            'title' => 'Module 2',
        ]*/
    ];

    if($module !== false)  {

        $module_data = isset($module_types[$module]) ? $module_types[$module] : false;

        if($field !== false)  {
            return isset($module_data[$field]) ? $module_data[$field] : "";
        }

        return $module_data;
    }

    return $module_types;
}

function siteView($view, $is_mobile) {

    if($is_mobile)  {
        return "site.mobile." . $view;
	}

    return "site.desktop." . $view;
}

function setUserLanguage($locale = ""){
    app(RequestLanguage::class)->setLanguageCode($locale);
    session()->put("user_language", getRequestLocale());
}

function getLanguageCodeGroups(){
    return [];
}

function getLanguageGroupFor($locale = ""){ // de_CH
    $locale         = @trim($locale);
    $locale         = $locale ?: getRequestLocale();
    $languageGroups = getLanguageCodeGroups();
    foreach($languageGroups as $languageGroup) if(in_array($locale, $languageGroup)) return $languageGroup;
    return [];
}

function hasLanguageGroups($locale = ""){ // de_CH
    $locale = $locale ?: getRequestLocale();
    return (bool)getLanguageGroupFor($locale);
}

function getRequestLanguageCode(){
    return app(RequestLanguage::class)->getLanguageCode();
}

function getRequestLocale(){
    return app(RequestLanguage::class)->getLanguageLocale();
}

function getRequestRegionCode(){
    return app(RequestLanguage::class)->getLanguageRegion();
}

function getLanguageId($langCode){
    return app(RequestLanguage::class)->getLanguageId($langCode);
}

function getLanguageLocaleIds()  {
    $allowedLanguageCodes = app(RequestLanguage::class)->languageMap;
    return $allowedLanguageCodes;
}

function getAllowedLanguageCodes(){
    return ["de_DE"];
}

function getDefaultLanguageCode(){
    return "de_DE";
}

function devModeActive(){
    $serverName = @$_SERVER["SERVER_NAME"] ?? "";
    return config("app.debug") && in_array($serverName, ["localhost", "kolt.contentcreators.at"]);
}

function is_localhost(){
    if(devModeActive())
        return @$_SERVER['SERVER_NAME'] == "localhost";
    else
        return false;
}

function test_mode(){
    // @TODO: disable!
    return dev_test_mode();
}

function dev_test_mode(){
    $serverName = @$_SERVER["SERVER_NAME"] ?? "";
    return in_array($serverName, ["localhost", "kolt.contentcreators.at"]);
}

function readCsvFile($filePath = "", $columnAmount = 2){
    $retData = [];
    if(($handle = fopen($filePath, 'r')) !== false){
        $header = fgetcsv($handle); // First row -> headers
        while(($data = fgetcsv($handle)) !== false){
            $retData[] = $data;
            unset($data);
        }
        fclose($handle);
    }
    return $retData;
}

function reverseSanitizeString($text){

    return html_entity_decode($text,ENT_QUOTES);
}

function sendSMTPEmail($mail_name, $mail_data)  {

    $mail_host = "mail.contentcreators.sk";
    $mail_port = 587;
    $mail_encryption = "TLS";
    $mail_username = "<EMAIL>";
    $mail_password = "ber37FQCc6ak";

    config([
        'mail.mailers.custom_smtp' => [
            'transport' => 'smtp',
            'host' => $mail_host,
            'port' => $mail_port,
            'username' => $mail_username,
            'password' => $mail_password,
            'encryption' => $mail_encryption,
        ],
    ]);

    Mail::mailer('custom_smtp')->send(new SendEmail($mail_name, $mail_data));
}

function convertExcelDate($dateValue, $format='Y-m-d H:i:s') {

    $unixDate = ceil(($dateValue - 25569) * 86400);
    return gmdate($format, $unixDate);
}


function pagination_links($pagination)  {

    $currentPage = $pagination->currentPage();
    $totalPages = $pagination->lastPage();
    $link = $pagination->path();

    $adjacents = 2;

    $pagination_links = '<ul class="cm-pagination">';
    $max_links_to_show = (($adjacents*2) + 1);

    if ($totalPages <= $max_links_to_show) {

        for ($counter = 1; $counter <= $totalPages; $counter++) {

            if ($counter == $currentPage) {
                $pagination_links.= "<li class='active'><a role='button'>".$counter."</a></li>";
            }
            else    {
                $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
            }
        }
    }
    elseif ($totalPages > $max_links_to_show)  {

        if($currentPage <= 4) {

            for ($counter = 1; $counter <= ($currentPage + $adjacents); $counter++)  {

                if ($counter == $currentPage) {
                    $pagination_links.= "<li class='active'><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }
                else {
                    $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }
            }

            $pagination_links.= "<li>...</li>";
            $pagination_links.= "<li><a role='button' href='".$link."?page=".$totalPages."'>".$totalPages."</a></li>";
        }
        elseif($currentPage > 2 && $currentPage < $totalPages - ($adjacents + 1)) {

            $pagination_links.= "<li><a role='button' href='?=1'>1</a></li>";
            $pagination_links.= "<li>...</li>";

            for($counter = $currentPage - $adjacents; $counter <= $currentPage + $adjacents; $counter++) {
                if ($counter == $currentPage) {
                    $pagination_links.= "<li class='active'><a role='button'>".$counter."</a></li>";
                }
                else{
                    $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }
            }

            $pagination_links.= "<li>...</li>";
            $pagination_links.= "<li><a role='button' href='".$link."?page=".$totalPages."'>".$totalPages."</a></li>";
        }
        else {

            $pagination_links.= "<li><a role='button' href='".$link."?page=1'>1</a></li>";
            $pagination_links.= "<li>...</li>";

            for ($counter = $totalPages - 5; $counter <= $totalPages; $counter++) {

                if ($counter == $currentPage) {
                    $pagination_links.= "<li class='active'><a role='button'>".$counter."</a></li>";
                }
                else{
                    $pagination_links.= "<li><a role='button' href='".$link."?page=".$counter."'>".$counter."</a></li>";
                }
            }
        }
    }

    $pagination_links.= "</ul>";

    return $pagination_links;
}

function deFormatToStandard($date)  {

    $dateParts = explode(".", $date);
    return $dateParts[2]."-".$dateParts[1]."-".$dateParts[0];
}

function getAdminDefaultImage($username){

    $first_letter = strtoupper($username[0]);
    $bg_classes = ['bg-warning', 'bg-primary', 'bg-success', 'bg-danger', 'bg-info'];
    $index = ord($first_letter) % count($bg_classes);
    $bg_class = $bg_classes[$index];

    return '<div class="avatar-image avatar-lg ' . $bg_class . ' text-white">' . $first_letter . '</div>';

}

function langField($val, $langCode=false)  {

    if(!$langCode)
        $langCode = getRequestLanguageCode();

    $langValues = $val ? @json_decode($val, true) : false;

    return $langValues ? @$langValues[$langCode] : $val;
}

function langColumnValues($val)  {

    $langArray = $val ? @json_decode($val, true) : [];

    if($langArray)  {

        $str = '';

        foreach($langArray as $key => $name)  {
            $str .= strtoupper($key). ' : ' .$name.'<br>';
        }

        return $str;
    }
    else  {
        return $val;
    }
}

function cc_json_encode($arr, $type='text')  {

    foreach($arr as $key => $val)  {

        if($type == 'editor')
            $arr[$key] = sanitizeHTMLStr($val);
        else
            $arr[$key] = sanitizeStr($val);

    }

    return json_encode($arr);
}

function langLabel($str, $langCode)  {

    $langNames = [
        "de" => "German",
        "en" => "English",
        "pl" => "Polish",
    ];

    return $str . " (".$langNames[$langCode].")";
}

function ensureDatabaseCacheLock(){
    if(!Schema::hasTable("cache_locks"))
        Schema::create('cache_locks', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->string('owner');
            $table->integer('expiration');
        });
}

function ensureDatabaseQueue(){
    if(!Schema::hasTable("jobs"))
        Schema::create('jobs', static function (Blueprint $table){
            $table->bigIncrements('id');
            $table->string('queue')->index();
            $table->longText('payload');
            $table->unsignedTinyInteger('attempts');
            $table->unsignedInteger('reserved_at')->nullable();
            $table->unsignedInteger('available_at');
            $table->unsignedInteger('created_at');
        });
}

function ensureDatabaseFailedQueue(){
    if(!Schema::hasTable("failed_jobs"))
        Schema::create('failed_jobs', static function (Blueprint $table){
            $table->id();
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });
}

function allInstitutes(){

    $institutes = Institute:: orderBy('institute_id')->get();
    return $institutes;
}

function getAllowedProductIds($user_id, $institute_id, $user_group)  {

    $userPermission = UserPermission::where('user_id', $user_id)->where('institute_id', $institute_id)->first();

    if($userPermission)  {

        $product_ids = $userPermission->products ? explode(",", $userPermission->products) : [];
    }
    else  {

        $group_name = $user_group == "KUO-ADMIN" || $user_group == "KUO-BACKEND" ? 'KUO-FRONTEND' : $user_group;

        $group_product = UserGroupProduct::where('institute_id', $institute_id)->where('group_name', $group_name)->first();
        $product_ids = $group_product && $group_product->products ? explode(",", $group_product->products) : [];
    }

    $sess_products = session()->has('o_products') ? session()->get('o_products') : [];

    if(!empty($sess_products))  {

        $sess_product_ids = array_keys($sess_products);

        foreach($sess_product_ids as $productId)  {

            if(!in_array($productId, $product_ids))
                $product_ids[] = $productId;
        }
    }

    return $product_ids;
}

function getInstituteId($instituteId){

    $login_from = Request::session()->get('login_from');

    if($login_from == "admin")
        return $instituteId;
    else
        return Request::session()->get('id_qualifier');
}

function userGroups()  {

    return [
        'KUO-FRONTEND',
        'KUO-KOMMERZ',
        'KUO-RETAIL',
    ];
}

function getOENameData($institute_id)  {

    $oe_num_list = array();
    $oe_name_list = array();

    $allUsers = User::where('saml_num_oe', '!=', '')->get();

    foreach ($allUsers as $au) {

        if (fix_user_institute($au->saml_name_id_qualifier) != $institute_id)
            continue;

        if (!in_array($au->saml_num_oe, $oe_num_list))
            array_push($oe_num_list, $au->saml_num_oe);

        if ($au->saml_oe_name != "")
            $oe_name_list['ins_'.$au['saml_num_oe']] = $au->saml_oe_name;
    }

    return [$oe_num_list, $oe_name_list];
}

function usedCategoryCounts($userProductsData)  {

    $catCounts = [];

    foreach($userProductsData as $product)  {

        $catId = $product['category_id'];

        if(!isset($catCounts[$catId]))  {
            $catCounts[$catId] = $product['usedCount'];
        }
        else  {
            $catCounts[$catId] += $product['usedCount'];
        }
    }

    return $catCounts;
}

function getProductData($id, $instituteId){

    return Product::where('id', $id)
                    ->where(function($query) use ($instituteId)  {
                        if($instituteId !== false)
                            $query->where('institute_id', $instituteId);
                    })
                    ->first();
}

function getCacheRevision(){
    return DataRevision::getRevision('cache.revision');
}

function coverImageColorClass(){

    return [ 'bg-forest'    => 'BG Forest',
             'bg-stone'     => 'BG Stone',
             'bg-bright-blue'=> 'BG Bright Blue',
             'bg-teal'      => 'BG Teal',
             'bg-apple'     => 'BG Apple',
             'bg-orange'    => 'BG Orange',
             'bg-pink'      => 'BG Pink',
             'bg-aubergine' => 'BG Aubergine'
            ];
}


function isEmail($email) {
    if (empty($email)) {
        return false;
    }

    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function recreateLaravelSession($sessionId, $laravelSession) {
    // Force new PHP session
    @session_destroy();
    @session_start();

    // Recreate Laravel session
    $handler = app('session');
    $handler->setId($sessionId);
    $handler->start();

    // Restore previous data
    foreach($laravelSession as $key => $value) {
        session([$key => $value]);
    }

    // Force save
    session()->save();

    \Illuminate\Support\Facades\Log::info("Session recreated: " . $sessionId);
}

function getRequestIp(){
    $ip = "-";
    try{
        $ip = @request()?->ip() ?? '';
    }
    catch(Exception $e){
        \Illuminate\Support\Facades\Log::error("Error getting IP: " . $e->getMessage());
    }
    return $ip;
}

function extractSamlAssertion($samlResponse){
    $pattern = '/<saml:Assertion\b[^>]*>(.*?)<\/saml:Assertion>/s';
    preg_match($pattern, $samlResponse, $matches);
    return @trim($matches[0] ?? '');
}
function guidv4(): string {
    // Generate 16 bytes (128 bits) of random data
    $data = random_bytes(16);
    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
    // Output the 36 character UUID
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

function maskEmail($email) {
    // Split the email into username and domain parts
    list($username, $domain) = explode('@', $email);

    // Mask the username (keep the first character and replace the rest with asterisks)
    $maskedUsername = $username[0] . str_repeat('*', strlen($username) - 1);

    // Mask the domain (replace all characters except the last 3 after the dot)
    $domainParts = explode('.', $domain);
    $mainDomain = str_repeat('*', strlen($domainParts[0]));
    $extension = end($domainParts);

    // Combine the masked parts and return
    return $maskedUsername . '@' . $mainDomain . '.' . $extension;
}

function generate_offer_code($customer_kukurz)  {

    $oi = 0;

    do {

        //$offerCode = date('Ymd', time())."-".$customer_kukurz.($oi > 0 ? '-'.$oi : '');
        $offerCode = $customer_kukurz.($oi > 0 ? '-'.$oi : '');

        $offer_exists = Offer::where('offer_code', $offerCode)->first();

        $oi++;

    } while($offer_exists);

    \Illuminate\Support\Facades\Log::info("Generated offer code: " . $offerCode);

    return $offerCode;
}

function getEnvelopeAddress($resultAddress) {

    $envelope_lines = [];

    foreach ($resultAddress as $address) {

        if(
            isset($address['addressUsage']['mainContact']) && $address['addressUsage']['mainContact'] &&
            isset($address['address']['envelope']) && is_array($address['address']['envelope'])
        ) {

            foreach ($address['address']['envelope'] as $line) {

                $line = trim($line);

                if (!empty($line)) {
                    $envelope_lines[] = $line;
                }
            }
            break;
        }
    }

    return implode("\n", $envelope_lines); // or use "<br>" for HTML
}
