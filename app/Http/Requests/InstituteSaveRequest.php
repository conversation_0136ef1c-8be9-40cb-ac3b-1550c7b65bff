<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class InstituteSaveRequest extends FormRequest{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(){
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()  {

        $rules = [
            'institute_name' => 'required',
            'institute_logo' => 'required',
            'institute_logo_black' => 'required',
            //'contact_text'   => 'required',
        ];

        if (!$this->route('institute_id')) { 
            $rules['institute_id'] = 'required|unique:institutes,institute_id';
        } 

        return $rules;
    }

    /**
     * Custom message for validation
     *
     * @return array
     */
    public function messages(){
        return [
            //'reward_name.required' 	=> 'Reward Name is required!',
        ];
    }
}
