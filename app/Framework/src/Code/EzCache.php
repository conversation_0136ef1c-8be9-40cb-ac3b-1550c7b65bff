<?php

namespace App\Framework\src\Code;

use App\Framework\src\Http\Models\DataRevision;
use Exception;
use Illuminate\Contracts\Cache\Repository;
use Psr\SimpleCache\InvalidArgumentException;

class EzCache{
    /**
     * @param string $key
     * @param mixed  $default
     * @param string $cacheStore
     *
     * @return Repository|mixed|string|null
     */
    public static function get(string $key = "", $default = "", string $cacheStore = "default"){
        try{
            $key_new   = DataRevision::getRevision() . "_" . $key;
            $cache = cache();
            if($cacheStore !== "default") $cache = $cache->store($cacheStore);
			return $cache->has($key_new) ? $cache->get($key_new) : static::set($key, $default, 'default', true);
        }
        catch(Exception | InvalidArgumentException $e){
			return $default;
        }
    }

    /**
     * @param string $key
     * @param mixed  $value
     * @param string $cacheStore
     *
     * @return mixed|string|null
     */
    public static function set(string $key = "", $value = "", string $cacheStore = "default", $prepend=false){
        try{
            if($prepend)
                $key   = DataRevision::getRevision() . "_" . $key;
            
            $cache = cache();
            if($cacheStore !== "default") $cache = $cache->store($cacheStore);
            if(is_callable($value)) $value = tryElse($value, null);
            $cache->set($key, $value);
			
			$forgetKey   = (DataRevision::getRevision()-1) . "_" . $key;
			static::forget($forgetKey);
			
			return $value;
        }
        catch(Exception | InvalidArgumentException $e){
            return $value;
        }
    }

    /**
     * @param string $key
     * @param string $cacheStore
     *
     * @throws Exception
     */
    public static function forget(string $key = "", string $cacheStore = "default"): void{
        $cache = cache();
        if($cacheStore !== "default") $cache = $cache->store($cacheStore);
        $cache->forget($key);
    }
}
