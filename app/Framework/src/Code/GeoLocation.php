<?php

namespace App\Framework\src\Code;

use Exception;
use GeoIp2\Database\Reader;
use GeoIp2\Exception\AddressNotFoundException;
use GeoIp2\Model\Country;
use Illuminate\Support\Facades\Log;
use MaxMind\Db\Reader\InvalidDatabaseException;

class GeoLocation{
    protected $initDone = false;
    protected $reader;

    /**
     * @throws InvalidDatabaseException
     */
    public function init(){
        if($this->initDone) return;
        $this->reader   = new Reader(app_path("Framework/data/GeoLite2-City.mmdb"));
        $this->initDone = true;
    }

    /**
     * @param string $ipAddress
     *
     * @return false|Country
     * @throws AddressNotFoundException
     * @throws InvalidDatabaseException
     */
    public function get(string $ipAddress = ""){
        $this->init();
        try{
            $ipAddress     = $ipAddress ?: Request()->ip();
            if($ipAddress === "127.0.0.1" || $ipAddress === "::1"){
                return false;
            }
            return $this->reader->city($ipAddress);
        }
        catch(Exception $e){
            return false;
        }
    }
}
