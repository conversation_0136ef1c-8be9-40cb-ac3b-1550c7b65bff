<?php

namespace App\Framework\src\Http\Traits;

use Doctrine\DBAL\Types\Type;
use Exception;
use Illuminate\Database\Connection;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

trait ManageableModelTrait{
    protected static $_NON_ID_FIELDS = [
        "created_at" => [],
        "updated_at" => [],
    ];

    protected static $_NON_ID_INDICES = [

    ];

    protected static $_NON_ID_UNIQUES = [

    ];

    public function migrateIndices(){
        // Skip without table
        if(!Schema::hasTable($this->table)) return;

        // Fetch existing indices
        /* @var Connection $connection */
        $connection    = DB::connection();
        $indices       = $connection->getDoctrineSchemaManager()->listTableIndexes($this->table);
        $indexNames    = array_map(function ($index){
            return $index->getName();
        }, $indices);
        $neededIndices = static::$NON_ID_INDICES ?? static::$_NON_ID_INDICES;

        // Add missing indices
        Schema::table($this->getTable(), function (Blueprint $table) use ($neededIndices, $indexNames){
            foreach($neededIndices as $indexName => $indexColumns){
                // Skip already defined indices
                if(in_array($indexName, $indexNames)) continue;

                // Add index
                $table->index(is_string($indexColumns) ? [$indexColumns] : $indexColumns, $indexName);
            }
        });
    }

    public function migrateUniques(){
        // Skip without table
        if(!Schema::hasTable($this->table)) return;

        // Fetch existing indices
        /* @var Connection $connection */
        $connection    = DB::connection();
        $indices       = $connection->getDoctrineSchemaManager()->listTableIndexes($this->table);
        $indexNames    = array_map(function ($index){
            return $index->getName();
        }, $indices);
        $neededUniques = static::$NON_ID_UNIQUES ?? static::$_NON_ID_UNIQUES;

        // Add missing indices
        Schema::table($this->getTable(), function (Blueprint $table) use ($neededUniques, $indexNames){
            foreach($neededUniques as $uniqueName => $uniqueColumns){
                // Skip already defined indices
                if(in_array($uniqueName, $indexNames)) continue;

                // Add index
                $table->unique(is_string($uniqueColumns) ? [$uniqueColumns] : $uniqueColumns, $uniqueName);
            }
        });
    }

    public function migrateSchema(){
        // Create table
        if(!Schema::hasTable($this->table)) Schema::create($this->table, function (Blueprint $table){
            $table->bigIncrements('id');
        });

        // Add doctrine-dbal timestamp support
        if(Type::hasType('timestamp'))
            Type::overrideType('timestamp', 'MarkTopper\DoctrineDBALTimestampType\TimestampType');
        else
            Type::addType('timestamp', 'MarkTopper\DoctrineDBALTimestampType\TimestampType');

        // Fetch existing columns
        /* @var Connection $connection */
        $connection   = DB::connection();
        $fields       = $connection->getSchemaBuilder()->getColumnListing($this->table);
        $neededFields = isset(static::$NON_ID_FIELDS) ? static::$NON_ID_FIELDS : static::$_NON_ID_FIELDS;

        // Create missing columns
        Schema::table($this->getTable(), function (Blueprint $table) use ($fields, $neededFields){
            foreach($neededFields as $name => $definition){
                // Init field defs
                $existing    = in_array($name, $fields);
                $allowChange = $definition["change_def"] ?? false;
                if($existing && !$allowChange) continue;
                $type = $definition["type"] ?? false;
                if(!$type) continue;
                $type          = strtolower($type);
                $autoIncrement = @$definition["auto_increment"] ? true : false;
                $unsigned      = @$definition["unsigned"] ? true : false;
                $fieldSchema   = null;

                // Text
                if($type === "string" || $type === "varchar")
                    $fieldSchema = $table->string($name, $definition["length"] ?? 128);
                else if($type === "text") $fieldSchema = $table->text($name);
                else if($type === "medium_text" || $type === "mediumtext") $fieldSchema = $table->mediumText($name);
                else if($type === "long_text" || $type === "longtext") $fieldSchema = $table->longText($name);
                else if($type === "char") $fieldSchema = $table->char($name, $definition["length"] ?? 128);
                // Numbers
                else if($type === "tiny_integer" || $type === "tiny" || $type === "tinyint" || $type === "tinyinteger")
                    $fieldSchema = $table->tinyInteger($name, $autoIncrement, $unsigned);
                else if($type === "small_integer" || $type === "short" || $type === "smallinteger" || $type === "smallint")
                    $fieldSchema = $table->smallInteger($name, $autoIncrement, $unsigned);
                else if($type === "medium_integer" || $type === "medium" || $type === "mediuminteger"
                    || $type === "mediumint")
                    $fieldSchema = $table->mediumInteger($name, $autoIncrement, $unsigned);
                else if($type === "integer" || $type === "int")
                    $fieldSchema = $table->integer($name, $autoIncrement, $unsigned);
                else if($type === "big_integer" || $type === "big_int" || $type === "bigint" || $type === "biginteger")
                    $fieldSchema = $table->bigInteger($name, $autoIncrement, $unsigned);
                else if($type === "decimal") $fieldSchema = $table->decimal($name, 12, 2, $unsigned);
                else if($type === "double") $fieldSchema = $table->double($name, null, null, $unsigned);
                else if($type === "float") $fieldSchema = $table->float($name, 12, 2, $unsigned);
                // Structures
                else if($type === "geometry") $fieldSchema = $table->geometry($name);
                else if($type === "linestring") $fieldSchema = $table->lineString($name);
                else if($type === "ip_address" || $type === "ip" || $type === "ipaddress")
                    $fieldSchema = $table->ipAddress($name);
                else if($type === "json") $fieldSchema = $table->json($name);
                else if($type === "jsonb") $fieldSchema = $table->jsonb($name);
                // Time
                else if($type === "date") $fieldSchema = $table->date($name);
                else if($type === "datetime") $fieldSchema = $table->dateTime($name);
                else if($type === "time") $fieldSchema = $table->time($name);
                // Misc
                else if($type === "bool" || $type === "boolean") $fieldSchema = $table->boolean($name);
                else if($type === "binary" || $type === "blob") $fieldSchema = $table->binary($name);
                // Predefined
                else if($type === "deleted_at"){
                    $fieldSchema = $table->softDeletes($name);
                }
                else if($type === "created_at"){
                    $fieldSchema = $table->timestamp($name);
                }
                else if($type === "updated_at"){
                    $fieldSchema = $table->timestamp($name)->useCurrent();
                }
                else{ // Unsupported log
                    Log::warning(sprintf("%s::migrateSchema() - Unsupported field type '%s' for '%s'",
                        $table->getTable(), $type, $name));
                    continue;
                }

                // Additional field attributes
                if(isset($definition["null"])) $fieldSchema = $fieldSchema->nullable($definition["null"] ?? true);
                if(isset($definition["default"])) $fieldSchema = $fieldSchema->default($definition["default"] ?? "");
                if(isset($definition["comment"]))
                    $fieldSchema = $fieldSchema->comment($definition["comment"] ?? "");
                if(isset($definition["index"]) && $definition["index"])
                    $fieldSchema = $fieldSchema->index(($definition["index"] ?? "") ?: $name);
                if(isset($definition["index"]) && $definition["index"]){
                    /* @var Connection $connection */
                    $connection      = DB::connection();
                    $indices         = $connection->getDoctrineSchemaManager()->listTableIndexes($this->table);
                    $indexNames      = array_map(function ($index){
                        return $index->getName();
                    }, $indices);
                    $uniqueIndexName = (isset($definition["index"]) && is_string($definition["index"])) ?
                        ($definition["index"] ?? "") : $name;
                    if(!in_array($uniqueIndexName, $indexNames)){
                        $fieldSchema = $fieldSchema->index($uniqueIndexName);
                    }
                }
                if(isset($definition["unique"]) && $definition["unique"]){
                    /* @var Connection $connection */
                    $connection      = DB::connection();
                    $indices         = $connection->getDoctrineSchemaManager()->listTableIndexes($this->table);
                    $indexNames      = array_map(function ($index){
                        return $index->getName();
                    }, $indices);
                    $uniqueIndexName = (isset($definition["unique"]) && is_string($definition["unique"])) ?
                        ($definition["unique"] ?? "") : $name;
                    if(!in_array($uniqueIndexName, $indexNames)){
                        $fieldSchema = $fieldSchema->unique($uniqueIndexName);
                    }
                }
                if(isset($definition["primary"]) && $definition["primary"]) $fieldSchema = $fieldSchema->primary();
                if(isset($definition["use_current"]) && $definition["use_current"])
                    $fieldSchema = $fieldSchema->useCurrent();

                // Foreign
                if(isset($definition["foreign"])){
                    $foreignData   = $definition["foreign"] ?? [];
                    $foreignField  = $foreignData["field"] ?? "id";
                    $foreignTable  = $foreignData["table"] ?? "";
                    $foreignSchema = $table->foreign($name)->references($foreignField)->on($foreignTable);
                    if($definition["on_delete"] ?? "") $foreignSchema->onDelete($definition["on_delete"] ?? "cascade");
                    if($definition["on_update"] ?? "") $foreignSchema->onUpdate($definition["on_update"] ?? "");
                }

                // Update existing fields
                if($existing) $fieldSchema->change();
            }
        });

        // Add missing indices
        $this->migrateUniques();
        $this->migrateIndices();
    }

    /**
     * @param array $rows
     * @param int   $batchSizes
     *
     * @return bool
     * @throws Exception
     */
    public function bulkInsertOrUpdate(array $rows, int $batchSizes = 500): bool{
        $success      = true;
        $upsertChunks = array_chunk($rows, $batchSizes);
        foreach($upsertChunks as $upsertChunk){
            $success = $this->bulkInsertOrUpdateBatch($upsertChunk);
            if(!$success) throw new Exception(@class_basename(__METHOD__) . ".error!");
        }
        return $success;
    }

    /**
     * @param array $rows
     *
     * @return bool
     */
    public function bulkInsertOrUpdateBatch(array $rows): bool{
        if(!$rows) return true;
        $table   = \DB::getTablePrefix() . with(new self)->getTable();
        $first   = reset($rows);
        $columns = implode(',',
            array_map(static function ($value){ return "$value"; }, array_keys($first))
        );
        $values  = implode(',', array_map(static function ($row){
                return '(' . implode(',',
                        array_map(static function ($value){
                            if(is_bool($value))
                                return $value ? "true" : "false";
                            else{
                                if(!$value) return 'null';
                                else return '"' . str_replace('"', '""', $value) . '"';
                            }
                        }, $row)
                    ) . ')';
            }, $rows)
        );

        $firstWithoutCreatedAt = array_keys($first);
        if(($key = array_search("created_at", $firstWithoutCreatedAt)) !== false)
            unset($firstWithoutCreatedAt[$key]);

        $updates = implode(',',
            array_map(function ($value){ return "$value = VALUES($value)"; }, $firstWithoutCreatedAt)
        );
        $sql     = "INSERT INTO {$table}({$columns}) VALUES {$values} ON DUPLICATE KEY UPDATE {$updates}";
        return \DB::statement($sql);
    }
}