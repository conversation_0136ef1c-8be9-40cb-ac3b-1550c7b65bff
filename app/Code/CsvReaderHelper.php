<?php
namespace App\Code;

class CsvReaderHelper{
    protected $fileName = "";

    public function __construct($fileName = ""){
        $this->fileName = $fileName;
    }

    public function getAmountLines(){
        $file = new \SplFileObject($this->fileName, 'r');
        $file->seek(PHP_INT_MAX);
        return $file->key();
    }

    public function getAmountRows(){
        $count     = 0;
        $delimiter = $this->detectDelimiter();
        $handle    = fopen($this->fileName, 'r');
        while(($data = fgetcsv($handle, 1000, $delimiter)) !== FALSE) ++$count;
        fclose($handle);
        return $count;
    }

    public function getFromPosition($y = 0, $x = 0){
        $delimiter = $this->detectDelimiter();
        $handle    = fopen($this->fileName, 'r');
        $curLine   = -1;
        $outValue  = false;
        while(($data = fgetcsv($handle, 1000, $delimiter)) !== FALSE){
            if(++$curLine == $y){
                $outValue = $data[$x] ?? false;
                break;
            }
        }
        fclose($handle);
        return $outValue;
    }

    public function readAll($withHeaderRow = false){
        $delimiter = $this->detectDelimiter();
        $handle    = fopen($this->fileName, 'r');
        $outValues = [];
        $startRowI = $withHeaderRow ? 0 : 1;
        $i         = -1;
        while(($data = fgetcsv($handle, 1000, $delimiter)) !== FALSE){
            if(++$i < $startRowI) continue;
            $outValues[] = $data;
        }
        fclose($handle);
        return $outValues;
    }

    public function getLine($y = 0){
        return $this->getFromLineTo($y, $y);
    }

    public function getFromLineTo($minLine = 0, $maxLine = 1, $columnsAmount = -1){
        $maxLine   = $maxLine < $minLine ? $minLine : $maxLine;
        $delimiter = $this->detectDelimiter();
        $handle    = fopen($this->fileName, 'r');
        $curLine   = -1;
        $outValues = [];
        while(($data = fgetcsv($handle, 1000, $delimiter)) !== FALSE){
            if(++$curLine >= $minLine && $curLine <= $maxLine)
                $outValues[] = $columnsAmount == -1 ? $data : array_slice($data, 0, $columnsAmount);
            else if($curLine > $maxLine) break;
        }
        fclose($handle);
        return $outValues;
    }

    public function detectDelimiter(){
        $handle     = fopen($this->fileName, 'r');
        $delimiters = ["\t", ";", "|", ","];
        $data_1     = [];
        $data_2     = [];
        $delimiter  = $delimiters[0];
        foreach($delimiters as $d){
            $data_1 = fgetcsv($handle, 4096, $d);
            if(sizeof($data_1) > sizeof($data_2)){
                $delimiter = $d;
                $data_2    = $data_1;
            }
            rewind($handle);
        }
        fclose($handle);
        return $delimiter;
    }
}
