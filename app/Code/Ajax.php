<?php

namespace App\Code;

class Ajax{

    // Properties

    protected $ajaxOut = array();

    // Public

    /**
     * @return array
     */
    public function getAjaxOut(){
        return $this->ajaxOut;
    }

    /**
     * @return string
     */
    public function getOutJson(){
        return json_encode($this->ajaxOut);
    }

    /**
     * @param      $index
     * @param      $value
     * @param bool $append
     */
    public function masterOut($index, $value, $append = false){
        if(is_string($append)){
            if($index == 'html' && (!is_string($value) || $value == '')) unset($this->ajaxOut[$index][$append]);
            $cur                            = (isset($this->ajaxOut[$index][$append]) ? $this->ajaxOut[$index][$append] : '');
            $this->ajaxOut[$index][$append] = $cur . $value;
        }
        elseif($append === true) $this->ajaxOut[$index][] = $value;
        else $this->ajaxOut[$index] = $value;
    }

    /**
     * @param      $data
     * @param bool $append
     */
    public function data($data, $append = true){
        $this->masterOut('data', $data, $append);
    }


    /**
     * @param string $actionName
     * @param        $data
     * @param bool   $append
     */
    public function action($actionName, $data = [], $append = true){
        $this->masterOut('action_queue', [$actionName, $data], $append);
    }
}
