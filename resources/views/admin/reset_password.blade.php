@extends('admin.layouts.auth')
@section('content')

    <!--begin::Body-->
    <div class="d-flex flex-center w-lg-50 p-10">
        <!--begin::Card-->
        <div class="card rounded-3 w-md-550px">
            <!--begin::Card body-->
            <div class="card-body d-flex flex-column p-10 p-lg-20 pb-lg-10">
                <!--begin::Wrapper-->
                <div class="d-flex flex-center flex-column-fluid pb-15 pb-lg-20">

                    @if (session('email_sent') || session('email_resent'))

                        <p>If you have an Account, you will receive an E-Mail shortly with a Password Reset Link. Please check your SPAM or JUNK Filter or <a href="{{ route('admin_resend_email') }}">resend E-Mail</a>.</p>

                    @else

                        <!--begin::Form-->
                        <form class="form w-100" novalidate="novalidate" id="kt_new_password_form" data-kt-redirect-url="{{ route("admin_save_reset_password", ["code" => $code]) }}" action="#" autocomplete="off" method="POST">
                            <!--begin::Heading-->
                            <div class="text-center mb-11">
                                <!--begin::Title-->
                                <h1 class="text-dark fw-bolder mb-3">Reset Password</h1>
                                <!--end::Title-->

                                <!--begin::Subtitle-->
                                @if( $valid )
                                    <div class="text-gray-500 fw-semibold fs-6">Password Rules : Needs to be between 8 and 20 characters long and needs to include at least one capital letter, one lower case letter and one number!</div>
                                @else
                                    <div class="text-danger fw-semibold fs-6">Your reset password link is invalid or expired.</div>
                                @endif
                                
                                <!--end::Subtitle=-->
                            </div>

                            @if( $valid )

                                <!--begin::Heading-->

                                <!--begin::Input group-->
                                <div class="fv-row mb-8" data-kt-password-meter="true">
                                    <!--begin::Wrapper-->
                                    <div class="mb-1">
                                        <!--begin::Input wrapper-->
                                        <div class="position-relative mb-3">
                                            <input class="form-control bg-transparent" type="password" placeholder="Password" name="new_password" autocomplete="off" />
                                            <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2" data-kt-password-meter-control="visibility">
                                                <i class="bi bi-eye-slash fs-2"></i>
                                                <i class="bi bi-eye fs-2 d-none"></i>
                                            </span>
                                        </div>
                                        <!--end::Input wrapper-->
                                        
                                        <!--begin::Meter-->
                                        <div class="d-flex align-items-center mb-3" data-kt-password-meter-control="highlight">
                                            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                            <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
                                        </div>
                                        <!--end::Meter-->

                                        @if (session('used_password'))
                                            <div class="fv-plugins-message-container invalid-feedback">This password has been used or similar to your old passwords.</div>
                                        @endif

                                        @if ($errors->has('new_password'))
                                            <div class="fv-plugins-message-container invalid-feedback">{{ ($errors->first('new_password') == "The new password format is invalid." ? "The new Password needs to include at least one capital letter, one lower case letter and one number." : $errors->first('new_password')) }}</div>
                                        @endif
                                    </div>
                                    <!--end::Wrapper-->

                                    <!--begin::Hint-->
                                    <div class="text-muted">Use 8 or more characters with a mix of letters, numbers & symbols.</div>
                                    <!--end::Hint-->
                                </div>
                                <!--end::Input group=-->

                                <!--end::Input group=-->
                                <div class="fv-row mb-8">
                                    <!--begin::Repeat Password-->
                                    <input type="password" placeholder="Repeat Password" name="confirm_password" autocomplete="off" class="form-control bg-transparent" />
                                    <!--end::Repeat Password-->
                                    @if ($errors->has('confirm_password'))
                                        <div class="fv-plugins-message-container invalid-feedback">{{ $errors->first('confirm_password') }}</div>
                                    @endif
                                </div>
                                <!--end::Input group=-->

                                <!--begin::Action-->
                                <div class="d-grid mb-10">
                                    <button type="button" id="kt_new_password_submit" class="btn btn-primary">
                                        <!--begin::Indicator label-->
                                        <span class="indicator-label">Submit</span>
                                        <!--end::Indicator label-->
                                        <!--begin::Indicator progress-->
                                        <span class="indicator-progress">Please wait...
                                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                        <!--end::Indicator progress-->
                                    </button>
                                </div>
                                <!--end::Action-->

                                <input type="hidden" name="_token" value="{{ csrf_token() }}">

                            @endif

                        </form>
                        <!--end::Form-->

                    @endif

                </div>
                <!--end::Wrapper-->
                
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Body-->
    
    @section('custom_javascript')
        <script src="{{ asset('public/assets') }}/js/custom/authentication/reset-password/new-password.js"></script>
    @endsection

@endsection

