@php
$num_rows = isset($rows) ? $rows : 3;
$id = $id ?? $name;
$id = str_replace(["[", "]", "."], ["-", "", "_"], $id);

$error_name = str_replace("]", "", str_replace("[", ".", $name));
@endphp

<div class="mb-4 cc-form-field-include {{ $additionalClass ?? "" }}">
	
    @if(isset($label) && $label)

        @if(isset($text_form) && $text_form)

            <div class="d-flex justify-content-between align-items-center mb-1">
                <div class="me-4">
                    <label for="{{ $id }}" class="form-label mb-0">
                        <span class="required">{!! $label !!}</span>
                    </label>
                </div>
                <a href="javascript:;" class="avatar-text avatar-md border-0 bg-soft-danger text-danger ms-auto delete-app-text" data-bs-toggle="tooltip" data-bs-original-title="Delete" data-key="{{ $name }}"><i class="feather-trash-2"></i></a>
            </div>

        @else

            <label for="{{ $id }}" class="form-label">
                {!! $label !!}
                
                @if(isset($required) && $required)
                    <span class="text-danger">*</span>
                @endif

            </label>

        @endif

    @endif
    
    <textarea class="form-control @if(isset($editor) && $editor) editor @endif @if(isset($inputCls) && $inputCls) {{ $inputCls }} @endif {{ ($errors->has($error_name)) ? 'error' : '' }}" 
              name="{{ $name }}" id="{{ $id ?? $name }}"
              autocomplete="off"
              rows="{{ $num_rows }}"
              @if(isset($readonly) && $readonly) readonly @endif
              @if(isset($required) && $required) data-validation="required" @endif
              placeholder="{{ $placeholder ?? "" }}"
              @if(isset($language_prefix) && $language_prefix) data-prefix="{{ $language_prefix }}" @endif
              @if(isset($lang_code) && $lang_code) data-lang-code="{{ $lang_code }}" @endif
              {!! isset($max_length) ? 'maxlength="'.$max_length.'"' : '' !!}
    >@isset($value){!! $value !!}@endisset</textarea>
    
    @if(isset($is_sortable) && $is_sortable)

        <div class="cc-sort-handle">
            <i class="feather feather-move"></i>
        </div>

    @endif

    <div class="d-flex justify-content-between mt-2">
        @isset($info)
            <small id="{{ $id ?? $name . "_info" }}" class="form-text text-muted">
                {{ $info }}
            </small>
        @endisset
        
        @if(isset($is_language) && $is_language)
            @if(isset($editor) && $editor)    
            @else
                <button class="btn btn-icon cc-translate-text ms-auto" data-bs-toggle="tooltip" data-bs-original-title="Translate" aria-label="Translate" data-id="{{ $id }}" data-prefix="{{ $language_prefix }}" ><i class="feather-globe"></i></button>    
            @endif    
        @endif
    </div>    

    @if ($errors->has($error_name))
        <label id="{{ $id ?? $name }}-error" class="error" for="{{ $id }}">
            @foreach($errors->get($error_name) as $error)
                {{ $error }} <br>
            @endforeach
        </label>
    @endif
</div>
