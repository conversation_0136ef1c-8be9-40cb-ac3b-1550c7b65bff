@php
$id = $id ?? $name;
$id = str_replace("[", "-", $id);
$id = str_replace("]", "", $id);

$error_name = str_replace("]", "", str_replace("[", ".", $name));
@endphp


<div class="mb-4 cc-form-field-include {{ $additionalClass ?? "" }}">

    @if(isset($label) && $label)

        @if(isset($text_form) && $text_form)

            <div class="d-flex justify-content-between align-items-center mb-1">
                <div class="me-4">
                    <label for="{{ $id }}" class="form-label mb-0">
                        <span class="required">{!! $label !!}</span>
                    </label>
                </div>
                <a href="javascript:;" class="avatar-text avatar-md border-0 bg-soft-danger text-danger ms-auto delete-app-text" data-bs-toggle="tooltip" data-bs-original-title="Delete" data-key="{{ $name }}"><i class="feather-trash-2"></i></a>
            </div>

        @else

            <label for="{{ $id }}" class="form-label">
                {!! $label !!} 
                
                @if(isset($required) && $required)
                    <span class="text-danger">*</span>
                @endif

            </label>

        @endif

    @endif
    
    <div class="input-group">
        <input type="{{ (isset($inputType) ? $inputType : "text") }}"
            class="form-control @if(isset($inputCls) && $inputCls) {{ $inputCls }} @endif {{ ($errors->has($error_name)) ? 'error' : '' }}" name="{{ $name }}"
            id="{{ $id ?? $name }}"
            @if(isset($readonly) && $readonly) readonly @endif
            @if(isset($disabled) && $disabled) disabled @endif
            @if(isset($required) && $required) required aria-required="true" @endif
            placeholder="{{ $placeholder ?? "" }}" @isset($value) value="{!! $value !!}" @endisset autocomplete="off"
            @if(isset($lang_code) && $lang_code) data-lang-code="{{ $lang_code }}" @endif>

            @if(isset($is_language) && $is_language)
                    <button class="btn btn-icon cc-translate-text" data-bs-toggle="tooltip" data-bs-original-title="Translate" aria-label="Translate" data-id="{{ $id }}" data-prefix="{{ $language_prefix }}" ><i class="feather-globe"></i></button>
            @endif
    </div>

    @isset($info)
        <small id="{{ $id ?? $name . "_info" }}" class="form-text text-muted mt-5">{{ $info }}</small>
    @endisset
    
    @if ($errors->has($error_name))
        <label id="{{ $id ?? $name }}-error" class="error" for="{{ $id }}">
            @foreach($errors->get($error_name) as $error)
                {{ $error }} <br>
            @endforeach
        </label>
    @endif

</div>