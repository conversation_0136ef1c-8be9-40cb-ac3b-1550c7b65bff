@php
$id = $id ?? $name;
$id = str_replace("[", "_", $id);
$id = str_replace("]", "", $id);

$allowedExts = isset($onlyExt) ? [".".$onlyExt] : [".pdf"];
$error_name = str_replace("]", "", str_replace("[", ".", $name));
@endphp

<div class="mb-4 cc-form-field-include your-media drop-input-container {{ $additionalClass ?? "" }}">
    
    @if(isset($label) && $label)

        @if(isset($text_form) && $text_form)
                
            <div class="d-flex justify-content-between align-items-center mb-1">
                <div class="me-4">
                    <label for="{{ $id }}" class="form-label mb-0">
                        <span class="required">{!! $label !!}</span>
                    </label>
                </div>
                <a href="javascript:;" class="avatar-text avatar-md border-0 bg-soft-danger text-danger ms-auto delete-app-text" data-bs-toggle="tooltip" data-bs-original-title="Delete" data-key="{{ $name }}"><i class="feather-trash-2"></i></a>
            </div>

        @else
            <label for="{{ $id }}" class="form-label">
                {!! $label !!} 
                
                @if(isset($required) && $required)
                    <span class="text-danger">*</span>
                @endif

            </label>
        @endif
        
    @endif
    
    <div class="cc-upload-img-parent wd-100 ht-100 position-relative overflow-hidden rounded d-flex align-item-center">
        <img src="{{ url('public/assets/images/gallery/upload-icon.png') }}" class="upload-pic cc-upload-img-fit rounded m-auto" alt="">
        
        <div class="position-absolute end-0 bottom-0 h-100 w-100 hstack align-items-center justify-content-center c-pointer upload-button">
            <i class="feather feather-camera" aria-hidden="true"></i>

            <input name="{{ $id ?? $name }}_upload" class="drag-upload-input func-drag-pdf-upload" type="file" 
             accept="{{ implode(",", $allowedExts) }}"
        	 data-field="#{{ $id ?? $name }}" 
             data-form="#pdf_upload_form" 
             data-module="{{ isset($module) ? $module : '' }}"
             data-orig-name-id="{{ isset($origname_id) ? $origname_id : '' }}" />

        </div>
        
        <input name="{{ $name }}" id="{{ $id ?? $name }}" type="hidden"
           @if(isset($required) && $required) data-validation="required" @endif
           @isset($value) value="{{ $value }}" @endisset
           @if(isset($lang_code) && $lang_code) data-lang-code="{{ $lang_code }}" @endif
           >
    
    </div>

    <div class="progress progress-md upload-progress mt-2 d-none">
        <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" role="progressbar">
            <span class="sr-only">0% Complete</span>
        </div>
    </div>

    @if(isset($input_comment))
        <p class="mt-2"><small>{{ $input_comment }}</small></p>
    @endif
	
    <div class="img-upload-wrapper mt-3 {{ $value ? 'd-block' : '' }}">
        <a href="{{ $value != "" ? url('public/uploads/'.$value) : '' }}" target="_blank">{{ $value }}</a>
    </div>
    
    <div class="wd-100 position-relative fs-12 text-muted mt-2 {{ ($value) ? '' : 'd-none' }}"><a class="btn btn-danger btn-sm func-del-img" data-name="{{ $name }}" data-default-img="{{ url('public/assets/images/gallery/upload-icon.png') }}">Löschen</a></div>

    @if ($errors->has($error_name))
        <label id="{{ $id ?? $name }}-error" class="error" for="{{ $id }}">
            @foreach($errors->get($error_name) as $error)
                {{ $error }} <br>
            @endforeach
        </label>
    @endif

</div>