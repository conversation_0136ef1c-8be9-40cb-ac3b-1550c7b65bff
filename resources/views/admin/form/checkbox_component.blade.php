@php
$error_name = str_replace("]", "", str_replace("[", ".", $name));
@endphp

<div class="form-group cc-form-field-include {{ $additionalClass ?? "" }}">
    <input type="checkbox" class="form-check-input" name="{{ $name }}" id="{{ $id ?? $name }}"
           @if(isset($disabled) && $disabled) disabled="disabled" @endif
           placeholder="{{ $placeholder ?? "" }}" value="{{ $value ?? "1" }}"
           @if(isset($checked) && $checked) checked="checked" @endif>
           
    @if(isset($disabled) && $disabled)
        <input type="hidden" class="form-check-input" name="{{ $name }}" id="{{ $id ?? $name }}"
               placeholder="{{ $placeholder ?? "" }}" value="{{ $value ?? "1" }}"
               @if(isset($checked) && $checked) checked="checked" @endif>
    @endif

    @if(isset($label) && $label)
        <label class="form-check-label" for="{{ $id ?? $name }}">
            {{ $label }}
        </label>
    @endif

    @isset($info)
        <small id="{{ $id ?? $name . "_info" }}" class="form-text text-muted">
            {{ $info }}
        </small>
    @endisset
    
    @if ($errors->has($error_name))
        <span class="error help-block invalid-feedback show_error">
            @foreach($errors->get($error_name) as $error)
                {{ $error }} <br>
            @endforeach
        </span>
    @endif
</div>