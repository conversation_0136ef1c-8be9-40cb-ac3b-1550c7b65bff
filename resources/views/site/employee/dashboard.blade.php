@extends('site.layouts.layout')

@section('content')

@include('site.elements.employee-dashboard-header')

@if($institute && $user_group)

    <style>
    .kukurs_submit { border-color: transparent !important; }
    .kukurs_submit:not(:disabled),
    .kukurs_submit:not(.disabled),
    a.kukurs_submit { display: none; }
    .kukurs_submit:not(:disabled) + a.kukurs_submit { display: inline-block; }
    </style>

    @php
    $debug_login = session()->get('debug_login');
    @endphp

    <!--Start [ layout ]-->
    <div class="layout" id="site-content">
        <div class="ed-list">
            <div class="ed-list-content">
                <div class="ed-list-space">
                    <div class="ed-list-row">
                        <div class="ed-list-left">
                            <div class="ed-list-group">
                                <div class="ed-list-left-head text-a">
                                    <h2 class="h5">{!! fT('d_employee.b_dashboard.a_kunden_auswählen', 'Kunden auswählen') !!}</h2>
                                </div>
                                <div class="ed-list-left-in">
                                    <div class="text-a">
                                        {!! auto_ptag(fT('d_employee.b_dashboard.a_kunden_auswählen_description', '<p>Bitte geben Sie hier den Kunden-KUKURZ ein um ein Angebot zu erstellen:</p>')) !!}
                                    </div>
                                    <form name="offer-customer-select-form" id="offer-customer-select-form" class="ed-sc-form-a form-a" method="post" action="#">
                                        <div class="ed-sc-row">
                                            <div class="ed-sc-left">
                                                <div class="form-group mb-0">
                                                    <div class="form-control-wrapper left-icon">
                                                        <input type="text" class="form-control text-uppercase" name="customer_kukurz" value="{{ $debug_login ? 'TEST001' : '' }}" id="customer_kukurz" aria-label="Enter Customer Kukurz" autocomplete="off">
                                                        <span class="form-control-left-icon"><i class="cc_icon-account"><svg role="img"><title>account icon</title><use xlink:href="#cc_icon-account"></use></svg></i></span>
                                                        <span class="error-txt">{!! fT('d_employee.b_dashboard.b_common_error_text', 'Bitte füllen sie dieses feld aus') !!}</span>
                                                        <span class="kukurz-error-txt"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="ed-sc-right">
                                                <button type="submit" class="btn btn-primary"><span class="btn-text">{!! fT('d_employee.b_dashboard.c_weiter', 'Weiter') !!}</span></button>
                                            </div>
                                        </div>
                                        <input type="hidden" name="no_client_kukurz" value="{{ session()->get('debug_login') ? 'TEST001' : 'NK' }}">
                                    </form>
                                    <ul class="ed-sc-list">
                                        {{--
                                        <li><a href="#" class="ed-sc-link active">Ramona Zenses &lt;<EMAIL>&gt;</a></li>
                                        <li><a href="#" class="ed-sc-link">Ramona Becker &lt;<EMAIL>&gt;</a></li>
                                        <li><a href="#" class="ed-sc-link">Ramona Bachmann &lt;<EMAIL>&gt;</a></li>
                                        <li><a href="#" class="ed-sc-link">Ramona Muster &lt;<EMAIL>&gt;</a></li>
                                        --}}
                                    </ul>
                                </div>
                            </div>

                            <div class="ed-list-group mt-auto">
                                <div class="ed-list-left-head text-a">
                                    <h2 class="h5">{!! fT('d_employee.b_dashboard.d_insurance4business_title', 'insurance4business (i4b)') !!}</h2>
                                </div>
                                <div class="ed-list-left-in">
                                    <div class="text-a">
                                        {!! auto_ptag(fT('d_employee.b_dashboard.e_insurance4business_description', '<p>Ihre Kund:in hat einen Versicherungswunsch. Starten Sie mit der Kund:in von hier aus eine Bedarfsanalyse mit dem Ziel, den individuellen Bedarf Ihrer Kund:in zu erheben. Daraus entsteht ein Bedarfsprotokoll für die Einbindung einer Versicherungsexpert:in. Geben Sie jetzt die KUKURZ der Kund:in ein und Sie werden direkt zur Bedarfsanalyse in die Applikation "insurance4business i4b)" geroutet.</p>')) !!}
                                    </div>

                                    @php $formActionUrl = dev_test_mode() ? "https://i4b.stable.i4b.microf.cs.eb.lan.at/start-process" : "https://i4b.stable.i4b.microp.cs.eb.lan.at/start-process";

                                    @endphp

                                    <form name="ed-sc-form-b" class="ed-sc-form-b form-a" id="weiter_zu_i4b_form_id" method="get" action="{!! $formActionUrl !!}">
                                        <div class="ed-sc-row">
                                            <div class="ed-sc-left">
                                                <div class="form-group mb-0">
                                                    <div class="form-control-wrapper">
                                                        <input type="hidden" name="CodApplication" value="KOLT">
                                                        <input type="text" class="form-control kukurs_input text-uppercase" name="CustomerShortCode" placeholder="{!! fT('d_employee.b_dashboard.f_kukurz_eingeben_place_holder', 'KUKURZ eingeben') !!}" id="ed-enter-kukurz" autocomplete="off">
                                                        <span class="error-txt">{!! fT('d_employee.b_dashboard.b_common_error_text', 'Please fill out this field') !!}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="ed-sc-right">
                                                <button disabled type="button" class="btn btn-primary kukurs_submit disabled"><span class="btn-text">{!! fT('d_employee.b_dashboard.g_weiter_zu_i4b', 'Weiter zu i4b') !!}</span></button>

                                                <a class="btn btn-primary kukurs_submit disabled" href="{!! $formActionUrl !!}" target="_blank">
                                                    {!! fT('d_employee.b_dashboard.g_weiter_zu_i4b', 'Weiter zu i4b') !!}
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="ed-list-group">
                                <div class="ed-list-left-head text-a">
                                    <h2 class="h5">{!! fT('d_employee.b_dashboard.h_pdf_export_title', 'PDF Export') !!}</h2>
                                </div>
                                <div class="ed-list-left-in">
                                    <div class="text-a">
                                        {!! auto_ptag(fT('d_employee.b_dashboard.i_pdf_export_description', '<p>Wählen Sie hier das Start- und End-Datum Ihres Export-Zeitraums aus. Sie erhalten dann ein PDF als Download.</p>')) !!}
                                    </div>
                                    <form name="ed-sc-form-c" id="report_pdf_form" class="ed-sc-form-c form-a" method="post" action="{{ route('site_report_pdf') }}">
                                        <div class="ed-sc-row">
                                            <div class="ed-sc-left">
                                                <div class="ed-date-row">
                                                    <div class="ed-date-cell">
                                                        <div class="form-group mb-0">
                                                            <div class="form-control-wrapper right-icon">
                                                                <input type="text" class="form-control ed-start-date-js" name="start_date" placeholder="{!! fT('d_employee.b_dashboard.j_start_date_place_holder', 'Start-Datum') !!}" id="ed-start-date" autocomplete="off" value="{{ date('Y-m-01', time() - (60*86400)) }}">
                                                                <span class="form-control-right-icon"><i class="cc_icon-date"><svg role="img"><title>date icon</title><use xlink:href="#cc_icon-date"></use></svg></i></span>
                                                                <span class="error-txt">{!! fT('d_employee.b_dashboard.b_common_error_text', 'Please fill out this field') !!}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="ed-date-cell">
                                                        <div class="form-group mb-0">
                                                            <div class="form-control-wrapper right-icon">
                                                                <input type="text" class="form-control ed-end-date-js" name="end_date" placeholder="{!! fT('d_employee.b_dashboard.k_end_date_place_holder', 'End-Datum') !!}" id="ed-end-date" autocomplete="off" value="{{ date('Y-m-30', time()) }}">
                                                                <span class="form-control-right-icon"><i class="cc_icon-date"><svg role="img"><title>date icon</title><use xlink:href="#cc_icon-date"></use></svg></i></span>
                                                                <span class="error-txt">{!! fT('d_employee.b_dashboard.b_common_error_text', 'Please fill out this field') !!}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="ed-sc-right">
                                                <button type="submit" class="btn btn-primary"><span class="btn-text">{!! fT('d_employee.b_dashboard.l_report_erstellen', 'Report erstellen') !!}</span></button>
                                            </div>
                                        </div>

                                        @if(session()->get('user_group') == 'KUO-ADMIN')
                                            <div class="form-group mt-5">
                                                <div class="form-control-wrapper">
                                                    <label class="form-label">{!! fT('d_employee.b_dashboard.l_select_oes', 'Gewünschte OEs auswählen:') !!}</label>
                                                    <select class="form-control h-100px" multiple="" name="oe_list[]">
                                                        @foreach ($oe_num_list as $single_oe_num)
                                                            @php
                                                            if ($single_oe_num==9999) continue;
                                                            $show_sn = $single_oe_num =="" ? "Keine OE" : $single_oe_num;
                                                            @endphp

                                                            <option value="{{ $single_oe_num }}">{{ $show_sn }} - {{ $oe_name_list['ins_'.$single_oe_num] }}</option>
                                                        @endforeach
                                                    </select>

                                                    <p class="small">{!! fT('d_employee.b_dashboard.l_select_oes_info', 'Um mehrere OEs auszuwählen, bitte STRG halten und mit Maus auswählen') !!}</p>
                                                    <span class="error-txt">{!! fT('d_employee.b_dashboard.b_common_error_text', 'Please fill out this field') !!}</span>
                                                </div>
                                            </div>
                                        @endif

                                        {!! csrf_field() !!}
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="ed-list-right">
                            <div class="ed-archiv">
                                <div class="ed-archiv-box" id="offers-list">

                                    <div class="ed-archiv-box-head text-a with-search">
                                        <div class="ed-archiv-box-head-row">
                                            <div class="ed-archiv-box-head-l">
                                                <div class="text-a">
                                                    <h3 class="h5 mb-0">Angebote suchen</h3>
                                                    <p>Sie können hier nach Angeboten mittels KUKURZ suchen:</p>
                                                </div>
                                            </div>
                                            <div class="ed-archiv-box-head-r">
                                                <form name="ed-head-search-form" id="offer-search-form" class="ed-head-search-form form-a" method="post" action="#">
                                                    <div class="ed-head-search-form">
                                                        <div class="ed-head-search-wrap">
                                                            <div class="form-group mb-0">
                                                                <div class="form-control-wrapper">
                                                                    <input type="text" class="form-control text-uppercase" name="cc-kukurz-search" placeholder="{!! fT('d_employee.b_dashboard.m_search', 'Suchen') !!}" id="cc-kukurz-search" autocomplete="off">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <button type="submit" name="input-search-submit" class="btn btn-primary" aria-label="search">
                                                            {!! fT('d_employee.b_dashboard.m_search', 'Suchen') !!}
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--End   [ layout ]-->

@else

    @if(!$institute)
        @include('site.elements.institute-setup-error')
    @elseif(!$user_group)
        @include('site.elements.user-group-error')
    @endif

@endif

@endsection
