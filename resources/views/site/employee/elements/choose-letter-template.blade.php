<div class="cc-body-content-row">
    <div class="cc-body-content-left">
        <div class="cc-body-content-scroll">
            <div class="cc-body-content-left-content">
                <div class="gjs1">

                    <ul class="nav nav-pills d-none" id="cc-visa-card-tab" role="tablist">
                        @foreach($intro_categories as $ici => $introCat)
                            @foreach($intro_letters as $ili => $introLetter)

                                @if($introCat->id != $introLetter->letter_template_category_id)
                                    @continue;
                                @endif

                                <li class="nav-item" role="presentation">
                                    <button class="nav-link {{ $introLetter->id == $selected_intro_letter->id ? 'active' : '' }}" id="cc-visa-card-tab-{{ $introLetter->id }}" data-bs-toggle="pill" data-bs-target="#cc-visa-card-tabpane-{{ $introLetter->id }}" type="button" role="tab" aria-controls="cc-visa-card-tabpane-{{ $introLetter->id }}" aria-selected="{{ $introLetter->id == $selected_intro_letter->id ? 'true' : 'false' }}">{!! $introLetter->template_title !!}</button>
                                </li>

                            @endforeach
                        @endforeach
                    </ul>

                    <div class="tab-content" id="cc-visa-card-tabContent" data-remove="true">

                        @foreach($intro_categories as $ici => $introCat)
                            @foreach($intro_letters as $ili => $introLetter)

                                @if($introCat->id != $introLetter->letter_template_category_id)
                                    @continue;
                                @endif

                                <div class="tab-pane fade {{ $introLetter->id == $selected_intro_letter->id ? 'show active' : '' }}" id="cc-visa-card-tabpane-{{ $introLetter->id }}" role="tabpanel" aria-labelledby="cc-visa-card-tab-{{ $introLetter->id }}" data-remove="true">
                                    
                                    @if($offer && $introLetter->id == $selected_intro_letter->id)

                                        @if(isset($offer_intro_text) && $offer_intro_text)
                                            {!! replaceNameVars($offer_intro_text, $offer, $user, $institute, $customer_name) !!}
                                        @else
                                            {!! replaceNameVars($offer->intro_text, $offer, $user, $institute, $customer_name) !!}
                                        @endif

                                    @else

                                        @if(isset($offer_intro_text) && $offer_intro_text && $introLetter->id == $selected_intro_letter->id)
                                            {!! replaceNameVars($offer_intro_text, $offer, $user, $institute, $customer_name) !!}
                                        @else
                                            <div class="cc-visa-card-faq-content">
                                                <div class="cc-visa-card-content-box">
                                                    <h2 class="h4"><span class="heading">{!! $introLetter->template_title !!}</span></h2>
                                                </div>
                                                <div class="cc-visa-card-content-box">
                                                    {!!  replaceNameVars($introLetter->template_desc, $offer, $user, $institute, $customer_name)  !!}
                                                </div>
                                            </div>
                                        @endif

                                    @endif
                                
                                </div>

                            @endforeach
                        @endforeach

                    </div>
                </div>
            </div>
        </div>
        <button type="button" class="cc-site-edit-btn tab-changelink" data-target-tab="#cc-edditor-tabpane-2" aria-label="Toggle Editor">
            <i class="cc_icon-edit"><svg role="img"><title>Edit icon</title><use xlink:href="#cc_icon-edit"></use></svg></i>
        </button>
    </div>

    <div class="cc-body-content-right">
        <div class="cc-body-content-scroll">
            <ul class="nav nav-pills visually-hidden" id="cc-edditor-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="cc-edditor-tab-1" data-bs-toggle="pill" data-bs-target="#cc-edditor-tabpane-1" type="button" role="tab" aria-controls="cc-edditor-tabpane-1" aria-selected="true">111</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cc-edditor-tab-2" data-bs-toggle="pill" data-bs-target="#cc-edditor-tabpane-2" type="button" role="tab" aria-controls="cc-edditor-tabpane-2" aria-selected="false">222</button>
                </li>
            </ul>
            <div class="tab-content" id="cc-edditor-tabContent">
                <div class="tab-pane fade active show" id="cc-edditor-tabpane-1" role="tabpanel" aria-labelledby="cc-edditor-tab-1">
                    <div class="accordion cc-accordion" id="cc-visa-card-faq">

                        @foreach($intro_categories as $ici => $introCat)

                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button {{ $introCat->id == $selected_intro_letter->letter_template_category_id ? '' : 'collapsed' }}" type="button" data-bs-toggle="collapse" data-bs-target="#cc-visa-card-faq-{{ $introCat->id }}" aria-expanded="{{ $introCat->id == $selected_intro_letter->letter_template_category_id ? 'true' : 'false' }}" aria-controls="cc-visa-card-faq-{{ $introCat->id }}">
                                        {!! $introCat->category_name !!}
                                        <span class="cc-fap-icon">
                                            <i class="cc_icon-arrow-down"><svg role="img"><title>Arrow down icon</title><use xlink:href="#cc_icon-arrow-down"></use></svg></i>
                                        </span>
                                    </button>
                                </h2>

                                <div id="cc-visa-card-faq-{{ $introCat->id }}" class="accordion-collapse collapse {{ $introCat->id == $selected_intro_letter->letter_template_category_id ? 'show' : '' }}" data-bs-parent="#cc-visa-card-faq">
                                    <div class="accordion-body">
                                        <ul class="cc-visa-card-nav">

                                            @foreach($intro_letters as $ili => $introLetter)

                                                @if($introCat->id != $introLetter->letter_template_category_id)
                                                    @continue;
                                                @endif

                                                <li>
                                                    <button class="cc-visa-card-nav-btn tab-changelink {{ $introLetter->id == $selected_intro_letter->id ? 'active' : '' }}" data-target-tab="#cc-visa-card-tabpane-{{ $introLetter->id }}" type="button" data-id="{{ $introLetter->id }}">{!! $introLetter->template_title !!}</button>
                                                </li>

                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>

                        @endforeach

                    </div>
                </div>
                
                <div class="tab-pane fade" id="cc-edditor-tabpane-2" role="tabpanel" aria-labelledby="cc-edditor-tab-2">
                    <div class="cc-edditor-wrap">
                        <div class="cc-edditor-wrap-box">
                            <span class="cc-edditor-lbl">{!! fT('d_employee.e_offer_create.k_angebot_aufsetzen', 'Angebot aufsetzen') !!}</span>
                        </div>
                        <div id="cc-edditor1-container"></div>
                        <div class="cc-help-html d-none">
                            <a href="{{ asset('public') }}/gfx/kolt-erklarungsvideo-v8.mp4" class="cc-help-editor-box" data-fancybox="help-group">
                                <div class="ratio ratio-16x9" >
                                    <img src="{{ asset('public') }}/gfx/kolt-erklarungsvideo-v8.jpg" alt="So funktioniert KOLT">
                                </div>
                                <div class="text-a text-center">
                                    <p>So funktioniert KOLT</p>
                                </div>
                            </a>
                            <a href="{{ asset('public') }}/gfx/uberschriften.mp4" class="cc-help-editor-box" data-fancybox="help-group">
                                <div class="ratio ratio-16x9" >
                                    <img src="{{ asset('public') }}/gfx/uberschriften.jpg" alt="Überschriften">
                                </div>
                                <div class="text-a text-center">
                                    <p>Überschriften</p>
                                </div>
                            </a>
                            <a href="{{ asset('public') }}/gfx/texte-formatierungen.mp4" class="cc-help-editor-box" data-fancybox="help-group">
                                <div class="ratio ratio-16x9" >
                                    <img src="{{ asset('public') }}/gfx/texte-formatierungen.jpg" alt="texte-formatierungen">
                                </div>
                                <div class="text-a text-center">
                                    <p>Texte - Formatierungen</p>
                                </div>
                            </a>
                            <a href="{{ asset('public') }}/gfx/tabellen-zeilenoperationen.mp4" class="cc-help-editor-box" data-fancybox="help-group">
                                <div class="ratio ratio-16x9" >
                                    <img src="{{ asset('public') }}/gfx/tabellen-zeilenoperationen.jpg" alt="tabellen-zeilenoperationen">
                                </div>
                                <div class="text-a text-center">
                                    <p>Tabellen - Zeilenoperationen</p>
                                </div>
                            </a>
                            <a href="{{ asset('public') }}/gfx/tabellen-spaltenoperationen.mp4" class="cc-help-editor-box" data-fancybox="help-group">
                                <div class="ratio ratio-16x9" >
                                    <img src="{{ asset('public') }}/gfx/tabellen-spaltenoperationen.jpg" alt="tabellen-spaltenoperationen">
                                </div>
                                <div class="text-a text-center">
                                    <p>Tabellen - Spaltenoperationen</p>
                                </div>
                            </a>
                            <a href="{{ asset('public') }}/gfx/tabellen-sonstige-operationen.mp4" class="cc-help-editor-box" data-fancybox="help-group">
                                <div class="ratio ratio-16x9" >
                                    <img src="{{ asset('public') }}/gfx/tabellen-sonstige-operationen.jpg" alt="tabellen-sonstige-operationen">
                                </div>
                                <div class="text-a text-center">
                                    <p>Tabellen - Sonstige Operationen</p>
                                </div>
                            </a>
                            <a href="{{ asset('public') }}/gfx/elemente-per-drag-drop-einfuegen.mp4" class="cc-help-editor-box" data-fancybox="help-group">
                                <div class="ratio ratio-16x9" >
                                    <img src="{{ asset('public') }}/gfx/elemente-per-drag-drop-einfuegen.jpg" alt="elemente-per-drag-drop-einfuegen">
                                </div>
                                <div class="text-a text-center">
                                    <p>Elemente per Drag & Drop einfuegen</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<input type="hidden" name="letter_template_id" value="{{ $selected_intro_letter->id }}">