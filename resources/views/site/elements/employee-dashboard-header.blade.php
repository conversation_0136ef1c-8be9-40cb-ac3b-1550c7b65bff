<!--Start [ ed-header ]-->
<div class="ed-header">
    <div class="ed-header-content">
        <div class="ed-header-left">
            <a href="{{ route('site_index') }}" class="ed-header-logo" aria-label="EB-SPK Logo">
                <img src="{{ (@$institute && $institute->institute_logo) ? url('public/uploads/'.$institute->institute_logo) : url('public/gfx/EB-SPK_Logo.svg') }}" alt="{{ (@$institute && $institute->institute_name) ? $institute->institute_name : 'Logo Erste Bank' }}">
            </a>
        </div>

        @if(session()->get('user_logged_in') == config('app.name'))

            <div class="ed-header-right">
                <div class="ed-header-right-row">
                    <span class="ed-login-name">{!! @$user->first_name." ".@$user->last_name !!}</span>
                    @if(session()->get('user_group') == 'KUO-ADMIN')
                        <div class="btn-spacer">
                            <a href="{{ route('admin_overview') }}" class="btn btn-white">
                                <span class="btn-text">{!! fT('d_employee.a_header.a_Backend', 'Backend') !!}</span>
                            </a>
                        </div>
                    @endif

                    <div class="ed-header-popover-wrap">
                        @if(@$institute->contact_text)
                            <button type="button" class="ed-header-r-btn help contact-popover-js" aria-label="Help" data-bs-toggle="popover" data-bs-placement="bottom" data-bs-offset="10,15" data-bs-html="true" data-bs-trigger="focus" data-bs-content='<div class="cn-popover-space">
                                <div class="cn-popover">
                                    <div class="cn-popover-content">
                                        <div class="cn-popover-desc text-a">{!! $institute->contact_text !!}</div>
                                    </div>
                                </div>
                            </div>'>
                                <i class="cc_icon-help"><svg role="img"><title>help icon</title><use xlink:href="#cc_icon-help"></use></svg></i>
                            </button>
                        @endif
                    </div>
                    
                    <a href="{{ route('site_user_logout') }}" class="ed-header-r-btn logout">
                        <span class="visually-hidden">Logout</span>
                        <i class="cc_icon-logout"><svg role="img"><title>logout icon</title><use xlink:href="#cc_icon-logout"></use></svg></i>
                    </a>
                </div>
            </div>

        @endif

    </div>
</div>
<!--End   [ ed-header ]-->