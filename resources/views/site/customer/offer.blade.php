@extends('site.layouts.layout')

@section('content')

@include('site.customer.offer_css')

<!-- Start [ layout ] -->
<div class="layout cc-customer-offer">
    <div class="cc-dashboard">
        
        <header class="cc-sidebar">
            <div class="cc-collapse-btn-wrap">
                <button class="btn btn-icon toggle-animation" aria-label="Toggle side bar"><i class="cc_icon-caret-prev"><svg role="img"><title>Arrow right icon</title><use xlink:href="#cc_icon-caret-prev"></use></svg></i></button>
            </div>
            <div class="cc-sidebar-content">
                <div class="cc-sidebar-header">
                    <a href="{{ route('site_index') }}" class="erste-logo" aria-label="ERSTE bank">
                        <img src="{{ url('public/uploads/'.$institute->institute_logo) }}" alt="{{ $institute->institute_name }}">
                        <img src="{{ url('public') }}/gfx/EBOe_Logo-mobile.svg" alt="erste logo mobile">
                    </a>
                </div>
                <div class="cc-sidebar-nav" id="check-offer-menu">
                    <nav class="cc-sidebar-nav-content">
                        <ul class="cc-menu">
                            <li>
                                <a role="button" tabindex="0" class="cc-menu-link tab-changelink active" data-target-tab="#cc-customer-intro">
                                    <span class="cc-menu-icon"><i class="cc_icon-home"><svg role="img"><title>Home icon</title><use xlink:href="#cc_icon-home"></use></svg></i></span>
                                    <span class="cc-menu-text">{!! fT('b_customer.d_offer.a_ihr_angebot', 'Ihr Angebot') !!}</span>
                                </a>
                            </li>
                            
                            @foreach($categories as $catId => $cat)

                                <li>
                                    <a role="button" tabindex="0" class="cc-menu-link cc-product-category-link">
                                        <span class="cc-menu-icon">
                                            <i class="cc_icon-karte"><img src="{{ url('public/uploads/'.$cat->category_icon) }}" alt="category: {{ $cat->category_name }}"></i>
                                        </span>
                                        <span class="cc-menu-text">{!! $cat->category_name !!}</span>
                                    </a>

                                    <ul class="cc-submenu">
                                        @foreach($category_products[$catId] as $catProductId)

                                            @php  $product = $products[$catProductId];  @endphp

                                            <li>
                                                <a role="button" tabindex="0" class="cc-submenu-link tab-changelink" data-target-tab="#cc-product-{{$catId}}-{{$catProductId}}">
                                                    <span class="cc-submenu-arrow"><i class="cc_icon-caret-right" ><svg role="img"><title>Right arrow icon</title><use xlink:href="#cc_icon-caret-right"></use></svg></i></span>
                                                    <span class="cc-submenu-text">{!! $product->product_name !!}</span>
                                                    <span class="cc-submenu-dot d-none"></span>
                                                </a>
                                            </li>

                                        @endforeach

                                        @if($cat->is_financing && $covenants && $covenants->count() > 0)

                                            <li>
                                                <a role="button" tabindex="0" class="cc-submenu-link tab-changelink" data-target-tab="#cc-product-covenants-{{$catId}}-{{$catProductId}}">
                                                    <span class="cc-submenu-arrow"><i class="cc_icon-caret-right" ><svg role="img"><title>Right arrow icon</title><use xlink:href="#cc_icon-caret-right"></use></svg></i></span>
                                                    <span class="cc-submenu-text">{!! fT('b_customer.d_offer.b_covenants', 'Covenants') !!}</span>
                                                    <span class="cc-submenu-dot d-none"></span>
                                                </a>
                                            </li>
                                            
                                        @endif
                                    </ul>
                                </li>

                            @endforeach
                            
                        </ul>
                    </nav>
                </div>
                <div class="cc-sidebar-footer">
                    <div class="cc-sd-footer-a">
                        <div class="text-a">
                            <h2 class="h6">{!! fT('b_customer.d_offer.h_sie_haben_noch_fragen', 'Sie haben noch Fragen?') !!}</h2>
                            <p>{!! $user->first_name." ".$user->last_name !!}<br><a href="mailto:{{ $user->email }}">{!! $user->email !!}</a></p>
                        </div>
                        <?php /* <div class="btn-help-spacer">
                            <a href="#" class="btn-help">
                                <i class="cc_icon-question"><svg role="img"><title>Question icon</title><use xlink:href="#cc_icon-question"></use></svg></i>
                            </a>
                        </div> */ ?>
                        <div class="btn-spacer">
                            <a href="{{ route('site_customer_offer_pdf_download', ['offer_code' => $offer->offer_code]) }}" class="btn btn-outline-primary">
                                <i class="cc_icon-download"><svg role="img"><title>Download icon</title><use xlink:href="#cc_icon-download"></use></svg></i>
                                <span class="btn-text">{!! fT('b_customer.d_offer.c_angebot_herunterladen', 'Angebot herunterladen') !!}</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <main class="cc-dashboard-body" id="site-content">
            <div class="cc-body-content">
                <div class="cc-customer-intro-wrap">
                    <?php /*<span class="cc-section-bg">
                        <picture>
                            <source srcset="{{ url('public/uploads/'.$cover_image->image_4k) }}" media="(min-width:2560px)">
                            <source srcset="{{ url('public/uploads/'.$cover_image->image_fullhd) }}" media="(min-width:1920px)">
                            <source srcset="{{ url('public/uploads/'.$cover_image->image_hd) }}" media="(min-width:1px)">
                            <img loading="lazy" src="{{ url('public/uploads/'.$cover_image->image_hd) }}" alt="{{ $cover_image->template_name }}">
                        </picture>
                    </span>*/ ?>
                    <div class="cc-customer-intro-content">
                        <div class="cc-customer-intro-content-in">
                            <ul class="nav nav-pills d-none" id="pills-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="cc-customer-intro-tab" data-bs-toggle="pill" data-bs-target="#cc-customer-intro" type="button" role="tab" aria-controls="cc-customer-intro" aria-selected="true">customer-intro</button>
                                </li>
                                
                                @foreach($categories as $catId => $cat)
                                    @foreach($category_products[$catId] as $catProductId)
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="cc-product-{{$catId}}-{{$catProductId}}-tab" data-bs-toggle="pill" data-bs-target="#cc-product-{{$catId}}-{{$catProductId}}" type="button" role="tab" aria-controls="cc-product-{{$catId}}-{{$catProductId}}" aria-selected="false">customer-product</button>
                                        </li>
                                        @if($cat->is_financing && $covenants && $covenants->count() > 0)

                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="cc-product-covenants-{{$catId}}-{{$catProductId}}-tab" data-bs-toggle="pill" data-bs-target="#cc-product-covenants-{{$catId}}-{{$catProductId}}" type="button" role="tab" aria-controls="cc-product-covenants-{{$catId}}-{{$catProductId}}" aria-selected="false">customer-product</button>
                                            </li>

                                        @endif
                                    @endforeach    
                                @endforeach

                            </ul>
                            <div class="tab-content" id="pills-tabContent">
                                
                                <div class="tab-pane fade show active" id="cc-customer-intro" role="tabpanel" aria-labelledby="cc-customer-intro-tab">
                                    <div class="cc-visa-card-faq-content">
                                        {!!  replaceNameVars($offer->intro_text, $offer, $user, $institute)  !!}
                                    </div>
                                </div>

                                @foreach($categories as $catId => $cat)
                                    @foreach($category_products[$catId] as $catProductId)

                                        @php    
                                        $product = $products[$catProductId];  
                                        $sessData = $sess_products[$catProductId];

                                        $produktblatt_files = [];
                                        if(isset($sessData['produktblatt']) && $sessData['produktblatt'] == "Y" && $product->files)  {

                                            $productFiles = explode(",", $product->files);
                                            foreach ($productFiles as $fileName) {
                                                if($fileName)
                                                    $produktblatt_files[] = url('public/uploads/'.$fileName);
                                            }
                                        }
                                        @endphp

                                        <div class="tab-pane fade" id="cc-product-{{$catId}}-{{$catProductId}}" role="tabpanel" aria-labelledby="cc-product-{{$catId}}-{{$catProductId}}-tab">
                                            
                                            @if(!empty($produktblatt_files))
                                                <div class="btn-spacer ms-auto ps-2 cc-product-download-btn" data-produktblatt="{{ json_encode($produktblatt_files) }}">
                                                    <a href="#" class="btn btn-primary">
                                                        <i class="cc_icon-download"><svg role="img"><title>Download icon</title><use xlink:href="#cc_icon-download"></use></svg></i>
                                                        <span class="btn-text">{!! fT('b_customer.d_offer.d_produktblatt_herunterladen', 'Produktblatt herunterladen') !!}</span>
                                                    </a>
                                                </div>
                                            @endif    

                                            @if(isset($products_html['product-detail-'.$product->id]))
                                                {!! $products_html['product-detail-'.$product->id] !!}
                                            @endif

                                        </div>

                                        @if($cat->is_financing && $covenants && $covenants->count() > 0)

                                            <div class="tab-pane fade" id="cc-product-covenants-{{$catId}}-{{$catProductId}}" role="tabpanel" aria-labelledby="cc-product-covenants-{{$catId}}-{{$catProductId}}-tab">
                                                <div class="text-a cc-covenants-header">
                                                    <div class="cc-covenants-header-row">
                                                        <span class="cc-covenants-header-icon">
                                                            <i class="cc_icon-shopping-gold"><svg role="img"><title>Gold icon</title><use xlink:href="#cc_icon-shopping-gold"></use></svg></i>
                                                        </span>
                                                        <h4 class="h4">{!! fT('b_customer.d_offer.b_covenants', 'Covenants') !!}</h4>
                                                    </div>
                                                    
                                                    {!! auto_ptag(fT('b_customer.d_offer.b_covenants_description', '<p>Es gilt als vereinbart, dass folgende Covenants während der gesamten Laufzeit des/der Kreditvertrages/-verträge einzuhalten sind:</p>')) !!}
                                                    
                                                </div>
                                                
                                                @foreach($covenants as $covenant)
                                                    <div class="cc-covenants-low-box">
                                                        <div class="cc-toggle-sub-text-content">
                                                            <div class="text-a covenant-single" id="covenant-{{ $covenant->id }}">
                                                                {!! $covenant->safety_clause !!}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                                
                                            </div>

                                        @endif

                                    @endforeach    
                                @endforeach

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
<!-- End [ layout ] -->

@endsection